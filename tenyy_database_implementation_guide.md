# Tenyy 数据库实施指南

## 1. 实施概述

基于对现有 Supabase 结构分析、Tenyy PRD 需求和 TheirStack 竞品分析，我们设计了一套完整的数据库架构。本指南提供了分步实施方案。

### 1.1 文档结构
- **`supabase_database_design.md`** (1,191 行) - 核心数据库设计
- **`supabase_enhancements.md`** (755 行) - 增强功能设计  
- **`tenyy_theirstack_analysis.md`** (300 行) - 竞品分析与优化

### 1.2 实施阶段
```
阶段 1: 核心表结构 (1-2 周)
├── 用户管理系统
├── 应用和 SDK 核心表
└── 基础 RLS 策略

阶段 2: 搜索和列表功能 (2-3 周)  
├── 全文搜索系统
├── 列表管理功能
└── 搜索历史和建议

阶段 3: 高级功能 (3-4 周)
├── 物化视图和 API 层
├── 通知系统
└── 开发者工具

阶段 4: 优化和监控 (1-2 周)
├── 性能优化
├── 监控和日志
└── 数据维护任务
```

## 2. 阶段 1: 核心表结构实施

### 2.1 执行顺序

#### Step 1: 用户管理表
```bash
# 1. 扩展现有 users 表
psql -c "
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS company TEXT,
ADD COLUMN IF NOT EXISTS job_title TEXT,
ADD COLUMN IF NOT EXISTS timezone TEXT DEFAULT 'UTC',
ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT FALSE;
"

# 2. 创建用户配额表 (如果不存在)
psql -f sql/01_user_management.sql
```

#### Step 2: 应用和 SDK 核心表
```bash
# 3. 创建应用表 (扩展现有结构)
psql -f sql/02_apps_sdks_core.sql

# 4. 创建关联表
psql -f sql/03_app_sdk_relations.sql
```

#### Step 3: 分类系统
```bash
# 5. 创建分类表
psql -f sql/04_categories.sql

# 6. 插入基础分类数据
psql -f sql/05_initial_categories.sql
```

### 2.2 验证脚本
```sql
-- 验证核心表创建
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('users', 'user_quotas', 'apps', 'sdks', 'app_sdks', 'categories');

-- 验证 RLS 策略
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE schemaname = 'public';
```

## 3. 阶段 2: 搜索和列表功能

### 3.1 全文搜索系统
```bash
# 1. 创建搜索相关表
psql -f sql/06_search_system.sql

# 2. 创建全文搜索索引
psql -c "
-- 为应用表创建全文搜索索引
ALTER TABLE public.apps 
ADD COLUMN IF NOT EXISTS search_vector tsvector;

UPDATE public.apps 
SET search_vector = to_tsvector('english', 
    COALESCE(name, '') || ' ' || 
    COALESCE(description, '') || ' ' || 
    COALESCE(developer, '') || ' ' || 
    COALESCE(category, '')
);

CREATE INDEX IF NOT EXISTS idx_apps_search_vector 
ON public.apps USING GIN (search_vector);

-- 创建自动更新触发器
CREATE OR REPLACE FUNCTION update_apps_search_vector()
RETURNS TRIGGER AS \$\$
BEGIN
    NEW.search_vector = to_tsvector('english',
        COALESCE(NEW.name, '') || ' ' ||
        COALESCE(NEW.description, '') || ' ' ||
        COALESCE(NEW.developer, '') || ' ' ||
        COALESCE(NEW.category, '')
    );
    RETURN NEW;
END;
\$\$ LANGUAGE plpgsql;

CREATE TRIGGER update_apps_search_vector_trigger
    BEFORE INSERT OR UPDATE ON public.apps
    FOR EACH ROW EXECUTE FUNCTION update_apps_search_vector();
"
```

### 3.2 列表管理系统
```bash
# 3. 创建列表管理表
psql -f sql/07_list_management.sql

# 4. 创建列表操作函数
psql -f sql/08_list_functions.sql
```

### 3.3 搜索建议系统
```bash
# 5. 创建搜索建议表
psql -f sql/09_search_suggestions.sql

# 6. 初始化搜索建议数据
psql -c "
-- 从现有应用数据生成搜索建议
INSERT INTO public.search_suggestions (suggestion_text, suggestion_type, target_id, popularity_score)
SELECT DISTINCT 
    name,
    'app',
    id,
    CASE 
        WHEN download_count > 1000000 THEN 1.0
        WHEN download_count > 100000 THEN 0.8
        WHEN download_count > 10000 THEN 0.6
        ELSE 0.4
    END
FROM public.apps 
WHERE name IS NOT NULL
ON CONFLICT (suggestion_text, suggestion_type) DO NOTHING;
"
```

## 4. 阶段 3: 高级功能实施

### 4.1 物化视图创建
```bash
# 1. 创建物化视图
psql -f sql/10_materialized_views.sql

# 2. 创建 API 视图层
psql -f sql/11_api_views.sql

# 3. 设置自动刷新
psql -c "
-- 创建定时刷新任务 (需要 pg_cron 扩展)
SELECT cron.schedule('refresh-materialized-views', '0 */6 * * *', 
    'SELECT public.refresh_materialized_views();');
"
```

### 4.2 通知系统
```bash
# 4. 创建通知系统
psql -f sql/12_notification_system.sql

# 5. 创建通知触发器
psql -f sql/13_notification_triggers.sql
```

### 4.3 开发者工具
```bash
# 6. 创建开发者工具表
psql -f sql/14_developer_tools.sql

# 7. 创建 API 密钥管理函数
psql -f sql/15_api_key_functions.sql
```

## 5. 阶段 4: 优化和监控

### 5.1 性能优化
```bash
# 1. 创建性能监控视图
psql -f sql/16_performance_monitoring.sql

# 2. 优化索引策略
psql -c "
-- 分析表统计信息
ANALYZE;

-- 检查缺失的索引
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE schemaname = 'public'
AND n_distinct > 100
ORDER BY n_distinct DESC;
"
```

### 5.2 数据维护任务
```bash
# 3. 设置定期维护任务
psql -c "
-- 清理过期搜索历史 (每天凌晨 2 点)
SELECT cron.schedule('cleanup-search-history', '0 2 * * *', 
    'SELECT public.cleanup_old_search_history();');

-- 更新搜索建议热度 (每小时)
SELECT cron.schedule('update-search-suggestions', '0 * * * *', 
    'SELECT public.update_search_suggestion_scores();');

-- 刷新用户统计 (每 30 分钟)
SELECT cron.schedule('refresh-user-stats', '*/30 * * * *', 
    'REFRESH MATERIALIZED VIEW CONCURRENTLY serving.mv_user_usage_stats;');
"
```

## 6. 数据迁移策略

### 6.1 现有数据迁移
```sql
-- 迁移现有应用数据到新结构
INSERT INTO public.apps (id, name, description, developer, category, rating, download_count)
SELECT 
    id,
    name,
    description,
    developer,
    category,
    rating::DECIMAL(3,2),
    download_count::BIGINT
FROM existing_app_table
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- 迁移 SDK 数据
INSERT INTO public.sdks (id, name, package_prefix, company, category)
SELECT DISTINCT
    sdk_id,
    sdk_name,
    package_name,
    company,
    category
FROM existing_sdk_data
ON CONFLICT (id) DO NOTHING;
```

### 6.2 数据验证
```sql
-- 验证数据完整性
SELECT 
    'apps' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT id) as unique_ids,
    COUNT(*) FILTER (WHERE name IS NOT NULL) as with_names
FROM public.apps

UNION ALL

SELECT 
    'sdks' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT id) as unique_ids,
    COUNT(*) FILTER (WHERE name IS NOT NULL) as with_names
FROM public.sdks;
```

## 7. 监控和维护

### 7.1 关键指标监控
```sql
-- 创建监控仪表板查询
CREATE OR REPLACE VIEW public.system_health_dashboard AS
SELECT 
    -- 用户活跃度
    (SELECT COUNT(*) FROM public.users WHERE created_at >= NOW() - INTERVAL '7 days') as new_users_7d,
    (SELECT COUNT(DISTINCT user_id) FROM public.search_history WHERE searched_at >= NOW() - INTERVAL '24 hours') as active_searchers_24h,
    
    -- 搜索性能
    (SELECT AVG(search_time_ms) FROM public.search_analytics WHERE created_at >= NOW() - INTERVAL '1 hour') as avg_search_time_ms,
    (SELECT COUNT(*) FROM public.search_history WHERE searched_at >= NOW() - INTERVAL '1 hour') as searches_last_hour,
    
    -- 数据质量
    (SELECT COUNT(*) FROM public.apps WHERE search_vector IS NULL) as apps_missing_search_vector,
    (SELECT COUNT(*) FROM public.user_notifications WHERE is_read = FALSE) as unread_notifications,
    
    -- 系统资源
    (SELECT COUNT(*) FROM public.search_sessions WHERE is_active = TRUE) as active_sessions,
    NOW() as last_updated;
```

### 7.2 告警配置
```sql
-- 创建系统告警函数
CREATE OR REPLACE FUNCTION public.check_system_alerts()
RETURNS TABLE (alert_type TEXT, alert_message TEXT, severity TEXT) AS $$
BEGIN
    -- 检查搜索性能
    IF (SELECT AVG(search_time_ms) FROM public.search_analytics WHERE created_at >= NOW() - INTERVAL '10 minutes') > 1000 THEN
        RETURN QUERY SELECT 'performance'::TEXT, 'Search response time > 1s'::TEXT, 'warning'::TEXT;
    END IF;
    
    -- 检查配额使用
    IF (SELECT COUNT(*) FROM public.user_quotas WHERE api_calls_used::FLOAT / api_calls_limit > 0.95) > 10 THEN
        RETURN QUERY SELECT 'quota'::TEXT, 'Multiple users near API quota limit'::TEXT, 'critical'::TEXT;
    END IF;
    
    -- 检查数据质量
    IF (SELECT COUNT(*) FROM public.apps WHERE search_vector IS NULL) > 100 THEN
        RETURN QUERY SELECT 'data_quality'::TEXT, 'Many apps missing search vectors'::TEXT, 'warning'::TEXT;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 8. 部署检查清单

### 8.1 部署前检查
- [ ] 所有 SQL 脚本语法验证通过
- [ ] RLS 策略正确配置
- [ ] 索引创建完成
- [ ] 物化视图可以正常刷新
- [ ] API 视图返回预期数据
- [ ] 触发器正常工作
- [ ] 定时任务配置正确

### 8.2 部署后验证
- [ ] 用户注册和登录正常
- [ ] 搜索功能响应时间 < 500ms
- [ ] 列表创建和管理功能正常
- [ ] 通知系统正常推送
- [ ] API 密钥生成和验证正常
- [ ] 配额统计准确
- [ ] 数据导出功能正常

### 8.3 性能基准
- 搜索响应时间: < 300ms (P95)
- 列表操作响应时间: < 200ms (P95)  
- API 调用响应时间: < 100ms (P95)
- 数据库连接数: < 80% 最大连接数
- 物化视图刷新时间: < 5 分钟

这个实施指南提供了完整的分步部署方案，确保 Tenyy 数据库能够安全、高效地部署到生产环境。
