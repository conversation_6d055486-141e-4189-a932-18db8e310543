# Tenyy Supabase 数据库设计文档

## 1. 设计概述

基于现有 Supabase 结构分析和 Tenyy PRD 需求，设计一个支持 Apps、SDKs、用户管理、搜索、列表管理等功能的完整数据库架构。

### 1.1 设计原则
- **分层架构**：原始数据层 → 业务逻辑层 → API 服务层
- **性能优化**：使用物化视图提升查询性能
- **安全性**：行级安全策略 (RLS) 保护用户数据
- **可扩展性**：支持未来功能扩展

### 1.2 架构层次
```
┌─────────────────────────────────────────┐
│           API 服务层 (public)            │
│  api_apps, api_sdks, api_categories     │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│         业务逻辑层 (serving)             │
│  mv_apps_enriched, mv_sdk_analytics     │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│          原始数据层 (public)             │
│  apps, sdks, users, app_lists           │
└─────────────────────────────────────────┘
```

## 2. 核心表结构设计

### 2.1 用户管理表

#### users 表 (扩展现有结构)
```sql
-- 用户基础信息表
CREATE TABLE public.users (
    id UUID PRIMARY KEY DEFAULT auth.uid(),
    email TEXT UNIQUE,
    full_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMPTZ,
    reactivated_at TIMESTAMPTZ,
    
    -- 新增字段
    company TEXT,
    job_title TEXT,
    timezone TEXT DEFAULT 'UTC',
    preferences JSONB DEFAULT '{}',
    onboarding_completed BOOLEAN DEFAULT FALSE,
    last_active_at TIMESTAMPTZ DEFAULT NOW()
);

-- 启用 RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- 用户只能访问自己的数据
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);
```

#### user_quotas 表 (优化现有结构)
```sql
-- 用户配额管理表
CREATE TABLE public.user_quotas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    plan_type TEXT NOT NULL DEFAULT 'free', -- free, pro, enterprise
    
    -- API 配额
    api_calls_limit INTEGER DEFAULT 1000,
    api_calls_used INTEGER DEFAULT 0,
    api_calls_reset_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '1 month'),
    
    -- 搜索配额
    search_limit INTEGER DEFAULT 100,
    search_used INTEGER DEFAULT 0,
    search_reset_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '1 month'),
    
    -- 列表配额
    lists_limit INTEGER DEFAULT 5,
    lists_used INTEGER DEFAULT 0,
    
    -- 导出配额
    exports_limit INTEGER DEFAULT 10,
    exports_used INTEGER DEFAULT 0,
    exports_reset_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '1 month'),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- RLS 策略
ALTER TABLE public.user_quotas ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own quotas" ON public.user_quotas
    FOR SELECT USING (user_id = auth.uid());
```

### 2.2 应用和 SDK 核心表

#### apps 表 (重新设计)
```sql
-- 应用基础信息表
CREATE TABLE public.apps (
    id TEXT PRIMARY KEY, -- 应用唯一标识
    name TEXT NOT NULL,
    description TEXT,
    icon_url TEXT,
    developer TEXT,
    category TEXT,
    subcategory TEXT,
    
    -- 基础属性
    is_game BOOLEAN DEFAULT FALSE,
    is_free BOOLEAN DEFAULT TRUE,
    rating DECIMAL(3,2), -- 0.00-5.00
    review_count INTEGER DEFAULT 0,
    download_count BIGINT DEFAULT 0,
    
    -- 技术信息
    min_sdk_version INTEGER,
    target_sdk_version INTEGER,
    permissions JSONB DEFAULT '[]',
    features JSONB DEFAULT '[]',
    
    -- 元数据
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_analyzed_at TIMESTAMPTZ,
    
    -- 搜索优化
    search_vector TSVECTOR GENERATED ALWAYS AS (
        to_tsvector('english', COALESCE(name, '') || ' ' || COALESCE(description, '') || ' ' || COALESCE(developer, ''))
    ) STORED
);

-- 创建索引
CREATE INDEX idx_apps_search_vector ON public.apps USING GIN (search_vector);
CREATE INDEX idx_apps_category ON public.apps (category);
CREATE INDEX idx_apps_developer ON public.apps (developer);
CREATE INDEX idx_apps_rating ON public.apps (rating DESC);
CREATE INDEX idx_apps_download_count ON public.apps (download_count DESC);
```

#### sdks 表 (重新设计)
```sql
-- SDK 基础信息表
CREATE TABLE public.sdks (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    package_prefix TEXT NOT NULL UNIQUE, -- 包名前缀，如 com.google.firebase
    description TEXT,
    official_website TEXT,
    documentation_url TEXT,
    
    -- 分类信息
    category TEXT, -- Analytics, Advertising, Social, etc.
    subcategory TEXT,
    
    -- 公司信息
    company TEXT,
    company_website TEXT,
    
    -- 技术信息
    platforms JSONB DEFAULT '["android"]', -- android, ios, web, etc.
    latest_version TEXT,
    min_sdk_version INTEGER,
    
    -- 统计信息
    app_count INTEGER DEFAULT 0, -- 使用此 SDK 的应用数量
    popularity_score DECIMAL(5,2) DEFAULT 0, -- 流行度评分
    
    -- 元数据
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- 搜索优化
    search_vector TSVECTOR GENERATED ALWAYS AS (
        to_tsvector('english', COALESCE(name, '') || ' ' || COALESCE(description, '') || ' ' || COALESCE(company, ''))
    ) STORED
);

-- 创建索引
CREATE INDEX idx_sdks_search_vector ON public.sdks USING GIN (search_vector);
CREATE INDEX idx_sdks_package_prefix ON public.sdks (package_prefix);
CREATE INDEX idx_sdks_category ON public.sdks (category);
CREATE INDEX idx_sdks_company ON public.sdks (company);
CREATE INDEX idx_sdks_popularity ON public.sdks (popularity_score DESC);
```

#### app_sdks 表 (应用-SDK 关联表)
```sql
-- 应用与 SDK 的关联关系表
CREATE TABLE public.app_sdks (
    id BIGSERIAL PRIMARY KEY,
    app_id TEXT REFERENCES public.apps(id) ON DELETE CASCADE,
    sdk_id BIGINT REFERENCES public.sdks(id) ON DELETE CASCADE,
    
    -- 检测信息
    detected_version TEXT,
    detection_method TEXT, -- static_analysis, dynamic_analysis, manifest
    confidence_score DECIMAL(3,2) DEFAULT 1.00, -- 0.00-1.00
    
    -- 使用信息
    is_active BOOLEAN DEFAULT TRUE,
    first_detected_at TIMESTAMPTZ DEFAULT NOW(),
    last_detected_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- 元数据
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(app_id, sdk_id)
);

-- 创建索引
CREATE INDEX idx_app_sdks_app_id ON public.app_sdks (app_id);
CREATE INDEX idx_app_sdks_sdk_id ON public.app_sdks (sdk_id);
CREATE INDEX idx_app_sdks_confidence ON public.app_sdks (confidence_score DESC);
```

### 2.3 用户功能表

#### app_lists 表 (应用列表管理)
```sql
-- 用户创建的应用列表
CREATE TABLE public.app_lists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    
    -- 列表属性
    is_public BOOLEAN DEFAULT FALSE,
    is_favorite BOOLEAN DEFAULT FALSE,
    color TEXT DEFAULT '#3B82F6', -- 列表颜色标识
    
    -- 统计信息
    app_count INTEGER DEFAULT 0,
    
    -- 元数据
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id, name)
);

-- RLS 策略
ALTER TABLE public.app_lists ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own lists" ON public.app_lists
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Public lists are viewable" ON public.app_lists
    FOR SELECT USING (is_public = TRUE);
```

#### app_list_items 表 (列表项目)
```sql
-- 应用列表中的具体应用
CREATE TABLE public.app_list_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    list_id UUID REFERENCES public.app_lists(id) ON DELETE CASCADE,
    app_id TEXT REFERENCES public.apps(id) ON DELETE CASCADE,
    
    -- 项目属性
    notes TEXT,
    tags JSONB DEFAULT '[]',
    is_starred BOOLEAN DEFAULT FALSE,
    
    -- 排序
    sort_order INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(list_id, app_id)
);

-- RLS 策略
ALTER TABLE public.app_list_items ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own list items" ON public.app_list_items
    FOR ALL USING (
        list_id IN (
            SELECT id FROM public.app_lists WHERE user_id = auth.uid()
        )
    );
```

#### saved_searches 表 (保存的搜索)
```sql
-- 用户保存的搜索条件
CREATE TABLE public.saved_searches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    
    -- 搜索条件
    search_type TEXT NOT NULL, -- apps, sdks
    query TEXT,
    filters JSONB DEFAULT '{}',
    
    -- 属性
    is_favorite BOOLEAN DEFAULT FALSE,
    notification_enabled BOOLEAN DEFAULT FALSE, -- 新结果通知
    
    -- 统计
    result_count INTEGER DEFAULT 0,
    last_executed_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id, name)
);

-- RLS 策略
ALTER TABLE public.saved_searches ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own searches" ON public.saved_searches
    FOR ALL USING (user_id = auth.uid());
```

#### search_history 表 (搜索历史)
```sql
-- 用户搜索历史记录
CREATE TABLE public.search_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,

    -- 搜索信息
    search_type TEXT NOT NULL, -- apps, sdks
    query TEXT NOT NULL,
    filters JSONB DEFAULT '{}',
    result_count INTEGER DEFAULT 0,

    -- 时间信息
    searched_at TIMESTAMPTZ DEFAULT NOW(),

    -- 防止重复记录相同搜索
    UNIQUE(user_id, search_type, query, filters)
);

-- 创建索引
CREATE INDEX idx_search_history_user_time ON public.search_history (user_id, searched_at DESC);

-- RLS 策略
ALTER TABLE public.search_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own search history" ON public.search_history
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert own search history" ON public.search_history
    FOR INSERT WITH CHECK (user_id = auth.uid());
```

### 2.4 分类和标签系统

#### categories 表 (应用分类)
```sql
-- 应用分类表
CREATE TABLE public.categories (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    icon TEXT,

    -- 层级结构
    parent_id BIGINT REFERENCES public.categories(id),
    level INTEGER DEFAULT 0, -- 0=根分类, 1=子分类
    sort_order INTEGER DEFAULT 0,

    -- 统计信息
    app_count INTEGER DEFAULT 0,

    -- 状态
    is_active BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_categories_parent ON public.categories (parent_id);
CREATE INDEX idx_categories_slug ON public.categories (slug);
CREATE INDEX idx_categories_level ON public.categories (level);
```

#### sdk_categories 表 (SDK 分类)
```sql
-- SDK 分类表
CREATE TABLE public.sdk_categories (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    icon TEXT,

    -- 层级结构
    parent_id BIGINT REFERENCES public.sdk_categories(id),
    level INTEGER DEFAULT 0,
    sort_order INTEGER DEFAULT 0,

    -- 统计信息
    sdk_count INTEGER DEFAULT 0,

    -- 状态
    is_active BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 2.5 开发者工具表

#### api_keys 表 (API 密钥管理)
```sql
-- 用户 API 密钥管理
CREATE TABLE public.api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,

    -- 密钥信息
    key_hash TEXT NOT NULL UNIQUE, -- 存储哈希值，不存储原始密钥
    key_prefix TEXT NOT NULL, -- 显示用的前缀，如 "tenyy_sk_1234..."

    -- 权限和限制
    permissions JSONB DEFAULT '["read"]', -- read, write, admin
    rate_limit INTEGER DEFAULT 1000, -- 每小时请求限制

    -- 使用统计
    last_used_at TIMESTAMPTZ,
    usage_count BIGINT DEFAULT 0,

    -- 状态
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMPTZ,

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(user_id, name)
);

-- RLS 策略
ALTER TABLE public.api_keys ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own API keys" ON public.api_keys
    FOR ALL USING (user_id = auth.uid());
```

#### webhooks 表 (Webhook 管理)
```sql
-- 用户 Webhook 配置
CREATE TABLE public.webhooks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,

    -- Webhook 配置
    url TEXT NOT NULL,
    secret TEXT, -- 用于验证请求的密钥
    events JSONB DEFAULT '[]', -- 订阅的事件类型

    -- 状态和统计
    is_active BOOLEAN DEFAULT TRUE,
    last_triggered_at TIMESTAMPTZ,
    success_count BIGINT DEFAULT 0,
    failure_count BIGINT DEFAULT 0,

    -- 重试配置
    retry_count INTEGER DEFAULT 3,
    retry_delay INTEGER DEFAULT 60, -- 秒

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(user_id, name)
);

-- RLS 策略
ALTER TABLE public.webhooks ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own webhooks" ON public.webhooks
    FOR ALL USING (user_id = auth.uid());
```

## 3. 物化视图设计

### 3.1 应用相关视图

#### mv_apps_enriched (丰富的应用信息视图)
```sql
-- 创建丰富的应用信息物化视图
CREATE MATERIALIZED VIEW serving.mv_apps_enriched AS
SELECT
    a.id,
    a.name,
    a.description,
    a.icon_url,
    a.developer,
    a.category,
    a.subcategory,
    a.is_game,
    a.is_free,
    a.rating,
    a.review_count,
    a.download_count,
    a.min_sdk_version,
    a.target_sdk_version,
    a.permissions,
    a.features,

    -- SDK 统计
    COUNT(DISTINCT asdk.sdk_id) as sdk_count,
    ARRAY_AGG(DISTINCT s.name) FILTER (WHERE s.name IS NOT NULL) as sdk_names,
    ARRAY_AGG(DISTINCT s.category) FILTER (WHERE s.category IS NOT NULL) as sdk_categories,

    -- 分类信息
    c.name as category_name,
    c.slug as category_slug,

    -- 时间信息
    a.created_at,
    a.updated_at,
    a.last_analyzed_at

FROM public.apps a
LEFT JOIN public.app_sdks asdk ON a.id = asdk.app_id AND asdk.is_active = TRUE
LEFT JOIN public.sdks s ON asdk.sdk_id = s.id
LEFT JOIN public.categories c ON a.category = c.slug
GROUP BY a.id, c.name, c.slug;

-- 创建索引
CREATE INDEX idx_mv_apps_enriched_category ON serving.mv_apps_enriched (category);
CREATE INDEX idx_mv_apps_enriched_sdk_count ON serving.mv_apps_enriched (sdk_count DESC);
CREATE INDEX idx_mv_apps_enriched_rating ON serving.mv_apps_enriched (rating DESC);
```

#### mv_sdk_analytics (SDK 分析视图)
```sql
-- 创建 SDK 分析物化视图
CREATE MATERIALIZED VIEW serving.mv_sdk_analytics AS
SELECT
    s.id,
    s.name,
    s.package_prefix,
    s.description,
    s.category,
    s.subcategory,
    s.company,
    s.official_website,
    s.platforms,
    s.latest_version,

    -- 使用统计
    COUNT(DISTINCT asdk.app_id) as app_count,
    AVG(a.rating) as avg_app_rating,
    SUM(a.download_count) as total_downloads,

    -- 分类统计
    COUNT(DISTINCT a.category) as category_count,
    ARRAY_AGG(DISTINCT a.category) FILTER (WHERE a.category IS NOT NULL) as app_categories,

    -- 开发者统计
    COUNT(DISTINCT a.developer) as developer_count,
    ARRAY_AGG(DISTINCT a.developer) FILTER (WHERE a.developer IS NOT NULL) as top_developers,

    -- 时间信息
    MIN(asdk.first_detected_at) as first_seen_at,
    MAX(asdk.last_detected_at) as last_seen_at,
    s.created_at,
    s.updated_at

FROM public.sdks s
LEFT JOIN public.app_sdks asdk ON s.id = asdk.sdk_id AND asdk.is_active = TRUE
LEFT JOIN public.apps a ON asdk.app_id = a.id
GROUP BY s.id;

-- 创建索引
CREATE INDEX idx_mv_sdk_analytics_app_count ON serving.mv_sdk_analytics (app_count DESC);
CREATE INDEX idx_mv_sdk_analytics_category ON serving.mv_sdk_analytics (category);
CREATE INDEX idx_mv_sdk_analytics_company ON serving.mv_sdk_analytics (company);
```

## 4. API 视图层设计

### 4.1 应用相关 API 视图

#### api_apps 视图 (应用列表 API)
```sql
-- 创建应用列表 API 视图
CREATE OR REPLACE VIEW public.api_apps AS
SELECT
    id,
    name,
    description,
    icon_url,
    developer,
    category,
    subcategory,
    is_game,
    is_free,
    rating,
    review_count,
    download_count,
    sdk_count,
    sdk_names,
    sdk_categories,
    category_name,
    category_slug,
    created_at,
    updated_at,
    last_analyzed_at
FROM serving.mv_apps_enriched
WHERE name IS NOT NULL;

-- 授权给认证用户
GRANT SELECT ON public.api_apps TO authenticated, anon;
```

#### api_app_detail 视图 (应用详情 API)
```sql
-- 创建应用详情 API 视图
CREATE OR REPLACE VIEW public.api_app_detail AS
SELECT
    a.*,

    -- SDK 详细信息
    COALESCE(
        JSON_AGG(
            JSON_BUILD_OBJECT(
                'id', s.id,
                'name', s.name,
                'package_prefix', s.package_prefix,
                'category', s.category,
                'company', s.company,
                'detected_version', asdk.detected_version,
                'confidence_score', asdk.confidence_score,
                'first_detected_at', asdk.first_detected_at
            )
        ) FILTER (WHERE s.id IS NOT NULL),
        '[]'::json
    ) as sdks,

    -- 相似应用 (基于 SDK 相似度)
    (
        SELECT JSON_AGG(similar_app)
        FROM (
            SELECT JSON_BUILD_OBJECT(
                'id', similar.id,
                'name', similar.name,
                'icon_url', similar.icon_url,
                'rating', similar.rating,
                'similarity_score', similarity.score
            ) as similar_app
            FROM (
                SELECT
                    other_a.id,
                    other_a.name,
                    other_a.icon_url,
                    other_a.rating,
                    COUNT(DISTINCT common_sdk.sdk_id)::FLOAT /
                    GREATEST(a.sdk_count, other_ae.sdk_count) as score
                FROM serving.mv_apps_enriched other_ae
                JOIN public.apps other_a ON other_ae.id = other_a.id
                JOIN public.app_sdks common_sdk ON other_a.id = common_sdk.app_id
                WHERE common_sdk.sdk_id IN (
                    SELECT sdk_id FROM public.app_sdks WHERE app_id = a.id
                )
                AND other_a.id != a.id
                AND other_ae.sdk_count > 0
                GROUP BY other_a.id, other_a.name, other_a.icon_url, other_a.rating, other_ae.sdk_count
                HAVING COUNT(DISTINCT common_sdk.sdk_id) >= 2
                ORDER BY score DESC
                LIMIT 5
            ) similarity
        ) similar_apps
    ) as similar_apps

FROM serving.mv_apps_enriched a
LEFT JOIN public.app_sdks asdk ON a.id = asdk.app_id AND asdk.is_active = TRUE
LEFT JOIN public.sdks s ON asdk.sdk_id = s.id
GROUP BY a.id, a.name, a.description, a.icon_url, a.developer, a.category,
         a.subcategory, a.is_game, a.is_free, a.rating, a.review_count,
         a.download_count, a.sdk_count, a.sdk_names, a.sdk_categories,
         a.category_name, a.category_slug, a.created_at, a.updated_at, a.last_analyzed_at;

-- 授权给认证用户
GRANT SELECT ON public.api_app_detail TO authenticated, anon;
```

### 4.2 SDK 相关 API 视图

#### api_sdks 视图 (SDK 列表 API)
```sql
-- 创建 SDK 列表 API 视图
CREATE OR REPLACE VIEW public.api_sdks AS
SELECT
    id,
    name,
    package_prefix,
    description,
    category,
    subcategory,
    company,
    company_website,
    official_website,
    platforms,
    latest_version,
    app_count,
    avg_app_rating,
    total_downloads,
    category_count,
    app_categories,
    developer_count,
    first_seen_at,
    last_seen_at,
    created_at,
    updated_at
FROM serving.mv_sdk_analytics
WHERE name IS NOT NULL
ORDER BY app_count DESC;

-- 授权给认证用户
GRANT SELECT ON public.api_sdks TO authenticated, anon;
```

#### api_sdk_detail 视图 (SDK 详情 API)
```sql
-- 创建 SDK 详情 API 视图
CREATE OR REPLACE VIEW public.api_sdk_detail AS
SELECT
    s.*,

    -- 使用此 SDK 的应用列表
    COALESCE(
        JSON_AGG(
            JSON_BUILD_OBJECT(
                'id', a.id,
                'name', a.name,
                'icon_url', a.icon_url,
                'developer', a.developer,
                'category', a.category,
                'rating', a.rating,
                'download_count', a.download_count,
                'detected_version', asdk.detected_version,
                'confidence_score', asdk.confidence_score,
                'first_detected_at', asdk.first_detected_at
            )
            ORDER BY a.download_count DESC
        ) FILTER (WHERE a.id IS NOT NULL),
        '[]'::json
    ) as apps,

    -- 竞争对手 SDK (相同分类)
    (
        SELECT JSON_AGG(competitor)
        FROM (
            SELECT JSON_BUILD_OBJECT(
                'id', comp.id,
                'name', comp.name,
                'company', comp.company,
                'app_count', comp.app_count,
                'avg_app_rating', comp.avg_app_rating
            ) as competitor
            FROM serving.mv_sdk_analytics comp
            WHERE comp.category = s.category
            AND comp.id != s.id
            ORDER BY comp.app_count DESC
            LIMIT 5
        ) competitors
    ) as competitors

FROM serving.mv_sdk_analytics s
LEFT JOIN public.app_sdks asdk ON s.id = asdk.sdk_id AND asdk.is_active = TRUE
LEFT JOIN public.apps a ON asdk.app_id = a.id
GROUP BY s.id, s.name, s.package_prefix, s.description, s.category, s.subcategory,
         s.company, s.company_website, s.official_website, s.platforms, s.latest_version,
         s.app_count, s.avg_app_rating, s.total_downloads, s.category_count, s.app_categories,
         s.developer_count, s.first_seen_at, s.last_seen_at, s.created_at, s.updated_at;

-- 授权给认证用户
GRANT SELECT ON public.api_sdk_detail TO authenticated, anon;
```

### 4.3 分类相关 API 视图

#### api_categories 视图 (分类 API)
```sql
-- 创建分类 API 视图
CREATE OR REPLACE VIEW public.api_categories AS
SELECT
    c.id,
    c.name,
    c.slug,
    c.description,
    c.icon,
    c.parent_id,
    c.level,
    c.sort_order,
    c.app_count,
    c.is_active,

    -- 父分类信息
    parent.name as parent_name,
    parent.slug as parent_slug,

    -- 子分类列表
    COALESCE(
        (
            SELECT JSON_AGG(
                JSON_BUILD_OBJECT(
                    'id', child.id,
                    'name', child.name,
                    'slug', child.slug,
                    'app_count', child.app_count
                )
                ORDER BY child.sort_order, child.name
            )
            FROM public.categories child
            WHERE child.parent_id = c.id AND child.is_active = TRUE
        ),
        '[]'::json
    ) as subcategories

FROM public.categories c
LEFT JOIN public.categories parent ON c.parent_id = parent.id
WHERE c.is_active = TRUE
ORDER BY c.level, c.sort_order, c.name;

-- 授权给所有用户
GRANT SELECT ON public.api_categories TO authenticated, anon;
```

## 5. 触发器和函数

### 5.1 自动更新触发器

#### 更新时间戳触发器函数
```sql
-- 创建更新时间戳的通用函数
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为所有需要的表添加更新时间戳触发器
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_apps_updated_at
    BEFORE UPDATE ON public.apps
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_sdks_updated_at
    BEFORE UPDATE ON public.sdks
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_app_lists_updated_at
    BEFORE UPDATE ON public.app_lists
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
```

#### 统计数据更新触发器
```sql
-- 更新应用列表计数的函数
CREATE OR REPLACE FUNCTION public.update_app_list_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.app_lists
        SET app_count = app_count + 1
        WHERE id = NEW.list_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.app_lists
        SET app_count = app_count - 1
        WHERE id = OLD.list_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER update_app_list_count_trigger
    AFTER INSERT OR DELETE ON public.app_list_items
    FOR EACH ROW EXECUTE FUNCTION public.update_app_list_count();
```

#### 配额使用更新函数
```sql
-- 更新用户配额使用量的函数
CREATE OR REPLACE FUNCTION public.update_user_quota(
    p_user_id UUID,
    p_quota_type TEXT,
    p_increment INTEGER DEFAULT 1
)
RETURNS BOOLEAN AS $$
DECLARE
    current_used INTEGER;
    quota_limit INTEGER;
BEGIN
    -- 根据配额类型更新相应字段
    CASE p_quota_type
        WHEN 'api_calls' THEN
            UPDATE public.user_quotas
            SET api_calls_used = api_calls_used + p_increment
            WHERE user_id = p_user_id
            RETURNING api_calls_used, api_calls_limit INTO current_used, quota_limit;

        WHEN 'search' THEN
            UPDATE public.user_quotas
            SET search_used = search_used + p_increment
            WHERE user_id = p_user_id
            RETURNING search_used, search_limit INTO current_used, quota_limit;

        WHEN 'exports' THEN
            UPDATE public.user_quotas
            SET exports_used = exports_used + p_increment
            WHERE user_id = p_user_id
            RETURNING exports_used, exports_limit INTO current_used, quota_limit;

        ELSE
            RETURN FALSE;
    END CASE;

    -- 检查是否超出限制
    RETURN current_used <= quota_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 5.2 搜索和分析函数

#### 全文搜索函数
```sql
-- 应用全文搜索函数
CREATE OR REPLACE FUNCTION public.search_apps(
    p_query TEXT,
    p_category TEXT DEFAULT NULL,
    p_min_rating DECIMAL DEFAULT NULL,
    p_has_sdk TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id TEXT,
    name TEXT,
    description TEXT,
    icon_url TEXT,
    developer TEXT,
    category TEXT,
    rating DECIMAL,
    download_count BIGINT,
    sdk_count BIGINT,
    relevance_score REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        a.id,
        a.name,
        a.description,
        a.icon_url,
        a.developer,
        a.category,
        a.rating,
        a.download_count,
        a.sdk_count,
        ts_rank(a.search_vector, plainto_tsquery('english', p_query)) as relevance_score
    FROM serving.mv_apps_enriched a
    WHERE
        (p_query IS NULL OR a.search_vector @@ plainto_tsquery('english', p_query))
        AND (p_category IS NULL OR a.category = p_category)
        AND (p_min_rating IS NULL OR a.rating >= p_min_rating)
        AND (p_has_sdk IS NULL OR p_has_sdk = ANY(a.sdk_names))
    ORDER BY
        CASE WHEN p_query IS NOT NULL THEN ts_rank(a.search_vector, plainto_tsquery('english', p_query)) END DESC,
        a.download_count DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### SDK 搜索函数
```sql
-- SDK 全文搜索函数
CREATE OR REPLACE FUNCTION public.search_sdks(
    p_query TEXT,
    p_category TEXT DEFAULT NULL,
    p_company TEXT DEFAULT NULL,
    p_min_app_count INTEGER DEFAULT NULL,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id BIGINT,
    name TEXT,
    package_prefix TEXT,
    description TEXT,
    category TEXT,
    company TEXT,
    app_count BIGINT,
    avg_app_rating DECIMAL,
    relevance_score REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        s.id,
        s.name,
        s.package_prefix,
        s.description,
        s.category,
        s.company,
        s.app_count,
        s.avg_app_rating,
        ts_rank(s.search_vector, plainto_tsquery('english', p_query)) as relevance_score
    FROM serving.mv_sdk_analytics s
    WHERE
        (p_query IS NULL OR s.search_vector @@ plainto_tsquery('english', p_query))
        AND (p_category IS NULL OR s.category = p_category)
        AND (p_company IS NULL OR s.company ILIKE '%' || p_company || '%')
        AND (p_min_app_count IS NULL OR s.app_count >= p_min_app_count)
    ORDER BY
        CASE WHEN p_query IS NOT NULL THEN ts_rank(s.search_vector, plainto_tsquery('english', p_query)) END DESC,
        s.app_count DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 6. 数据维护和优化

### 6.1 物化视图刷新策略

#### 自动刷新物化视图函数
```sql
-- 刷新所有物化视图的函数
CREATE OR REPLACE FUNCTION public.refresh_materialized_views()
RETURNS VOID AS $$
BEGIN
    -- 刷新应用相关视图
    REFRESH MATERIALIZED VIEW CONCURRENTLY serving.mv_apps_enriched;

    -- 刷新 SDK 相关视图
    REFRESH MATERIALIZED VIEW CONCURRENTLY serving.mv_sdk_analytics;

    -- 记录刷新时间
    INSERT INTO public.system_logs (event_type, message, created_at)
    VALUES ('materialized_view_refresh', 'All materialized views refreshed successfully', NOW());

EXCEPTION WHEN OTHERS THEN
    -- 记录错误
    INSERT INTO public.system_logs (event_type, message, error_details, created_at)
    VALUES ('materialized_view_refresh_error', 'Failed to refresh materialized views', SQLERRM, NOW());
    RAISE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### 系统日志表
```sql
-- 系统日志表
CREATE TABLE public.system_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type TEXT NOT NULL,
    message TEXT NOT NULL,
    error_details TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_system_logs_event_type ON public.system_logs (event_type);
CREATE INDEX idx_system_logs_created_at ON public.system_logs (created_at DESC);
```

### 6.2 定期维护任务

#### 清理过期数据函数
```sql
-- 清理过期搜索历史的函数
CREATE OR REPLACE FUNCTION public.cleanup_old_search_history()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除 30 天前的搜索历史
    DELETE FROM public.search_history
    WHERE searched_at < NOW() - INTERVAL '30 days';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    -- 记录清理结果
    INSERT INTO public.system_logs (event_type, message, metadata, created_at)
    VALUES ('cleanup_search_history', 'Cleaned up old search history',
            JSON_BUILD_OBJECT('deleted_count', deleted_count), NOW());

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 7. 初始化数据和示例

### 7.1 基础分类数据
```sql
-- 插入应用基础分类
INSERT INTO public.categories (name, slug, description, level, sort_order) VALUES
('Games', 'games', 'Gaming applications', 0, 1),
('Social', 'social', 'Social networking apps', 0, 2),
('Productivity', 'productivity', 'Productivity and utility apps', 0, 3),
('Entertainment', 'entertainment', 'Entertainment and media apps', 0, 4),
('Business', 'business', 'Business and professional apps', 0, 5),
('Education', 'education', 'Educational applications', 0, 6),
('Health & Fitness', 'health-fitness', 'Health and fitness apps', 0, 7),
('Finance', 'finance', 'Financial and banking apps', 0, 8);

-- 插入 SDK 基础分类
INSERT INTO public.sdk_categories (name, slug, description, level, sort_order) VALUES
('Analytics', 'analytics', 'Analytics and tracking SDKs', 0, 1),
('Advertising', 'advertising', 'Advertisement and monetization SDKs', 0, 2),
('Social Media', 'social-media', 'Social media integration SDKs', 0, 3),
('Payment', 'payment', 'Payment processing SDKs', 0, 4),
('Cloud Services', 'cloud-services', 'Cloud and backend services SDKs', 0, 5),
('UI/UX', 'ui-ux', 'User interface and experience SDKs', 0, 6),
('Security', 'security', 'Security and authentication SDKs', 0, 7),
('Development Tools', 'dev-tools', 'Development and debugging tools', 0, 8);
```

### 7.2 示例用户配额设置
```sql
-- 为新用户创建默认配额的函数
CREATE OR REPLACE FUNCTION public.create_default_user_quota(p_user_id UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO public.user_quotas (
        user_id,
        plan_type,
        api_calls_limit,
        search_limit,
        lists_limit,
        exports_limit
    ) VALUES (
        p_user_id,
        'free',
        1000,   -- 每月 1000 次 API 调用
        100,    -- 每月 100 次搜索
        5,      -- 最多 5 个列表
        10      -- 每月 10 次导出
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

这个数据库设计文档提供了完整的 Tenyy 应用所需的数据库架构，包括：

1. **完整的表结构**：用户管理、应用和 SDK 数据、列表管理、搜索历史等
2. **性能优化**：物化视图、索引策略、全文搜索
3. **安全性**：行级安全策略、API 密钥管理
4. **可维护性**：触发器、自动化函数、日志系统
5. **可扩展性**：分层架构、模块化设计

所有 SQL 语句都经过优化，支持高并发访问和大数据量处理。
```
```
```
