#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态生成Prefect配置文件
根据环境变量自动生成正确的prefect.yaml配置
"""

import os
import yaml
import sys
from pathlib import Path

# 添加项目路径以导入配置
sys.path.append('.')
sys.path.append('./tenyy')

# 备注：避免在模块顶层导入 heavy 依赖（如 SQLAlchemy），
# 改为在 get_env_config 内部按需导入，并用 try/except 兜底到默认配置。

def get_env_config(env_name):
    """从统一配置文件获取环境配置"""
    try:
        # 延迟导入，避免顶层导入触发重型依赖
        from tenyy.config import get_config  # type: ignore
        # 从统一配置系统获取配置
        config = get_config(env_name)

        # 提取Prefect部署需要的配置
        prefect_config = {
            "postgres_password": config.DATABASE_CONFIG.get('password', 'your_strong_password_here'),
            "host_download_dir": getattr(config, 'HOST_DOWNLOAD_DIR', config.ARIA2_CONFIG.get('local_download_dir', '/tmp/downloads')),
            "docker_image": getattr(config, 'DOCKER_IMAGE', f"localhost:5000/tenyy-unified:latest"),
            "docker_network": getattr(config, 'DOCKER_NETWORK', "tenyy-net-local"),
            "aria2_rpc_url": config.ARIA2_CONFIG.get('rpc_url', 'http://localhost:6800/jsonrpc'),
            "aria2_rpc_token": config.ARIA2_CONFIG.get('rpc_token', 'your_token_here'),
            "prefect_api_url": getattr(config, 'PREFECT_API_URL', 'http://localhost:4200/api'),
        }

        return prefect_config

    except Exception as e:
        print(f"⚠️ 警告: 无法从配置文件读取 {env_name} 环境配置: {e}")
        print("🔄 使用默认配置...")

        # 降级到默认配置
        default_configs = {
            "local": {
                "postgres_password": "your_strong_password_here",
                "host_download_dir": "/Users/<USER>/dev/local_download/aria2-downloads",
                "docker_image": "localhost:5000/tenyy-unified:latest",
                "docker_network": "tenyy-net-local",
                "aria2_rpc_url": "http://localhost:6800/jsonrpc",
                "aria2_rpc_token": "zhangdi168",
                "prefect_api_url": "http://localhost:4200/api",
            },
            "prefect_local_container": {
                "postgres_password": "your_strong_password_here",
                "host_download_dir": "/Users/<USER>/dev/local_download/aria2-downloads",
                "docker_image": "localhost:5000/tenyy-unified:latest",
                "docker_network": "tenyy-net-local",
                "aria2_rpc_url": "http://host.docker.internal:6800/jsonrpc",
                "aria2_rpc_token": "zhangdi168",
                "prefect_api_url": "http://localhost:4200/api",
            },
            "production": {
                "postgres_password": "zhangdi168",
                "host_download_dir": "/mnt/ssd/tenyy/downloads",
                "docker_image": "*************:5000/tenyy-unified:latest",
                "docker_network": "tenyy-stack_tenyy-net",
                "aria2_rpc_url": "http://aria2-service:6800/jsonrpc",
                "aria2_rpc_token": "zhangdi168",
                "prefect_api_url": "http://*************:4200/api",
            }
        }

        return default_configs.get(env_name, default_configs["production"])

def generate_prefect_yaml(env_name="production"):
    """生成prefect.yaml配置"""
    
    # 获取环境配置
    env_config = get_env_config(env_name)
    
    # 基础配置模板
    config = {
        "name": "tenyy-unified",
        "prefect-version": "3.0.0",
        "build": None,
        "push": None,
        "pull": [
            {"prefect.deployments.steps.set_working_directory": {"directory": "/app"}}
        ],
        "deployments": []
    }
    
    # 爬虫部署配置
    crawler_deployments = [
        {
            "name": "yinyongbao-app-crawler",
            "version": "1.0.0",
            "tags": ["crawler", "yinyongbao", "app", env_name],
            "description": "腾讯应用宝应用爬虫 - 爬取应用信息",
            "entrypoint": "tenyy/src/crawler/prefect_flow/prefect_flow.py:universal_crawler",
            "parameters": {
                "store_type": "yinyongbao",
                "app_type": "app",
                "max_pages": -1,
                "max_categories": -1
            },
            "schedule": None,
            "work_pool": {
                "name": "tenyy-unified-pool",
                "work_queue_name": "crawler-queue",
                "job_variables": {
                    "image": env_config["docker_image"],
                    "image_pull_policy": "Always",
                    "networks": [env_config["docker_network"]],
                    "env": {
                        "PREFECT_LOGGING_LEVEL": "INFO",
                        "PYTHONPATH": "/app",
                        "PREFECT_API_URL": "http://prefect-server:4200/api",
                        "DB_HOST": "app-db",
                        "DB_PORT": "5432",
                        "DB_USER": "admin",
                        "DB_PASSWORD": env_config["postgres_password"],
                        "DB_NAME": "tenyy_app"
                    },
                    "cpu_limit": "1.0",
                    "memory_limit": "2Gi",
                    "restart_policy": "Always"
                }
            }
        },
        {
            "name": "yinyongbao-game-crawler",
            "version": "1.0.0", 
            "tags": ["crawler", "yinyongbao", "game", env_name],
            "description": "腾讯应用宝游戏爬虫 - 爬取游戏信息",
            "entrypoint": "tenyy/src/crawler/prefect_flow/prefect_flow.py:universal_crawler",
            "parameters": {
                "store_type": "yinyongbao",
                "app_type": "game",
                "max_pages": -1,
                "max_categories": -1
            },
            "schedule": None,
            "work_pool": {
                "name": "tenyy-unified-pool",
                "work_queue_name": "crawler-queue",
                "job_variables": {
                    "image": env_config["docker_image"],
                    "image_pull_policy": "Always",
                    "networks": [env_config["docker_network"]],
                    "env": {
                        "PREFECT_LOGGING_LEVEL": "INFO",
                        "PYTHONPATH": "/app",
                        "PREFECT_API_URL": "http://prefect-server:4200/api",
                        "DB_HOST": "app-db",
                        "DB_PORT": "5432",
                        "DB_USER": "admin",
                        "DB_PASSWORD": env_config["postgres_password"],
                        "DB_NAME": "tenyy_app"
                    },
                    "cpu_limit": "1.0",
                    "memory_limit": "2Gi",
                    "restart_policy": "Always"
                }
            }
        },
        {
            "name": "huawei-app-crawler",
            "version": "1.0.0",
            "tags": ["crawler", "huawei", "app", env_name],
            "description": "华为应用市场爬虫 - 爬取应用信息",
            "entrypoint": "tenyy/src/crawler/prefect_flow/prefect_flow.py:universal_crawler",
            "parameters": {
                "store_type": "huawei",
                "max_pages": -1,
                "max_categories": -1
            },
            "schedule": None,
            "work_pool": {
                "name": "tenyy-unified-pool",
                "work_queue_name": "crawler-queue",
                "job_variables": {
                    "image": env_config["docker_image"],
                    "image_pull_policy": "Always",
                    "networks": [env_config["docker_network"]],
                    "env": {
                        "PREFECT_LOGGING_LEVEL": "INFO",
                        "PYTHONPATH": "/app",
                        "PREFECT_API_URL": "http://prefect-server:4200/api",
                        "DB_HOST": "app-db",
                        "DB_PORT": "5432",
                        "DB_USER": "admin",
                        "DB_PASSWORD": env_config["postgres_password"],
                        "DB_NAME": "tenyy_app"
                    },
                    "cpu_limit": "1.0",
                    "memory_limit": "2Gi",
                    "restart_policy": "Always"
                }
            }
        },
        {
            "name": "yinyongbao-similar-crawler",
            "version": "1.0.0",
            "tags": ["crawler", "yinyongbao", "similar", env_name],
            "description": "腾讯应用宝推荐爬虫 - 爬取推荐应用信息",
            "entrypoint": "tenyy/src/crawler/prefect_flow/prefect_flow.py:universal_crawler",
            "parameters": {
                "store_type": "yinyongbao_similar",
                "max_pages": -1,
                "max_categories": 0
            },
            "schedule": None,
            "work_pool": {
                "name": "tenyy-unified-pool",
                "work_queue_name": "crawler-queue",
                "job_variables": {
                    "image": env_config["docker_image"],
                    "image_pull_policy": "Always",
                    "networks": [env_config["docker_network"]],
                    "env": {
                        "PREFECT_LOGGING_LEVEL": "INFO",
                        "PYTHONPATH": "/app",
                        "PREFECT_API_URL": "http://prefect-server:4200/api",
                        "DB_HOST": "app-db",
                        "DB_PORT": "5432",
                        "DB_USER": "admin",
                        "DB_PASSWORD": env_config["postgres_password"],
                        "DB_NAME": "tenyy_app"
                    },
                    "cpu_limit": "1.0",
                    "memory_limit": "2Gi",
                    "restart_policy": "Always"
                }
            }
        }
    ]
    
    # APK处理部署配置
    apk_deployment = {
        "name": "batch-apk-processing",
        "version": "1.0.0",
        "tags": ["download", "extract", "apk", env_name],
        "description": "批量APK下载和分析处理器 - 处理待下载的APK文件",
        "entrypoint": "tenyy/src/download_extract/flows/batch_processor.py:batch_apk_processing_flow",
        "parameters": {
            "batch_size": 10,
            "max_concurrent": 3
        },
        "schedule": None,
        "work_pool": {
            "name": "tenyy-unified-pool",
            "work_queue_name": "download-queue",
            "job_variables": {
                "image": env_config["docker_image"],
                "image_pull_policy": "Always",
                "networks": [env_config["docker_network"]],
                "env": {
                    "PREFECT_LOGGING_LEVEL": "INFO",
                    "PYTHONPATH": "/app",
                    "DOCKER_ENV": "true",
                    "PREFECT_API_URL": "http://prefect-server:4200/api",
                    "DB_HOST": "app-db",
                    "DB_PORT": "5432",
                    "DB_USER": "admin",
                    "DB_PASSWORD": env_config["postgres_password"],
                    "DB_NAME": "tenyy_app",
                    "ARIA2_RPC_URL": env_config["aria2_rpc_url"],
                    "ARIA2_RPC_TOKEN": env_config["aria2_rpc_token"],
                    "DOWNLOAD_DIR": "/downloads"
                },
                "volumes": [
                    f"{env_config['host_download_dir']}:/downloads"
                ],
                "cpu_limit": "0",  # 0表示无限制，自适应分配
                "memory_limit": "0",  # 0表示无限制，自适应分配
                "cpu_request": "1.0",  # 最小保证1核
                "memory_request": "2Gi",  # 最小保证2GB内存
                "restart_policy": "Always"
            }
        }
    }
    
    # 循环APK处理部署配置
    continuous_apk_deployment = {
        "name": "continuous-batch-apk-processing",
        "version": "1.0.0",
        "tags": ["download", "extract", "apk", "continuous", env_name],
        "description": "循环批量APK下载和分析处理器 - 持续处理直到没有待处理任务",
        "entrypoint": "tenyy/src/download_extract/flows/batch_processor.py:continuous_batch_apk_processing_flow",
        "parameters": {
            "batch_size": 50,
            "max_concurrent": 10,
            "max_cycles": 100,
            "cycle_delay": 30
        },
        "schedule": None,
        "work_pool": {
            "name": "tenyy-unified-pool",
            "work_queue_name": "download-queue",
            "job_variables": {
                "image": env_config["docker_image"],
                "image_pull_policy": "Always",
                "networks": [env_config["docker_network"]],
                "env": {
                    "PREFECT_LOGGING_LEVEL": "INFO",
                    "PYTHONPATH": "/app",
                    "DOCKER_ENV": "true",
                    "PREFECT_API_URL": "http://prefect-server:4200/api",
                    "DB_HOST": "app-db",
                    "DB_PORT": "5432",
                    "DB_USER": "admin",
                    "DB_PASSWORD": env_config["postgres_password"],
                    "DB_NAME": "tenyy_app",
                    "ARIA2_RPC_URL": "http://host.docker.internal:6800/jsonrpc",  # 容器内使用host.docker.internal
                    "ARIA2_RPC_TOKEN": env_config["aria2_rpc_token"],
                    "DOWNLOAD_DIR": "/downloads"
                },
                "volumes": [
                    f"{env_config['host_download_dir']}:/downloads"
                ],
                "cpu_limit": "0",  # 0表示无限制，自适应分配
                "memory_limit": "0",  # 0表示无限制，自适应分配
                "cpu_request": "2.0",  # 循环处理需要更多资源，最小保证2核
                "memory_request": "4Gi",  # 最小保证4GB内存
                "restart_policy": "Always"
            }
        }
    }

    # 真正的流水线APK处理部署配置
    pipeline_apk_deployment = {
        "name": "pipeline-apk-processing",
        "version": "1.0.0",
        "tags": ["download", "extract", "apk", "pipeline", "continuous", env_name],
        "description": "🚀 真正的流水线APK处理器 - 任务队列+工作池，无批次等待",
        "entrypoint": "tenyy/src/download_extract/flows/batch_processor.py:continuous_pipeline_apk_processing_flow",
        "parameters": {
            "max_concurrent": 10,      # 并发工作者数量 (建议: 5-20)
            "task_fetch_size": 20,     # 每次获取任务数 (建议: 10-50)
            "max_idle_time": 60,       # 空闲超时时间/秒 (建议: 30-300)
            "check_interval": 5        # 检查新任务间隔/秒 (建议: 3-10)
        },
        "schedule": None,
        "work_pool": {
            "name": "tenyy-unified-pool",
            "work_queue_name": "pipeline-queue",
            "job_variables": {
                "image": env_config["docker_image"],
                "image_pull_policy": "Always",
                "networks": [env_config["docker_network"]],
                "env": {
                    "PREFECT_LOGGING_LEVEL": "INFO",
                    "PYTHONPATH": "/app",
                    "DOCKER_ENV": "true",
                    "PREFECT_API_URL": "http://prefect-server:4200/api",
                    "DB_HOST": "app-db",
                    "DB_PORT": "5432",
                    "DB_USER": "admin",
                    "DB_PASSWORD": env_config["postgres_password"],
                    "DB_NAME": "tenyy_app",
                    "ARIA2_RPC_URL": env_config["aria2_rpc_url"],
                    "ARIA2_RPC_TOKEN": env_config["aria2_rpc_token"],
                    "DOWNLOAD_DIR": "/downloads"
                },
                "volumes": [
                    f"{env_config['host_download_dir']}:/downloads"
                ],
                "cpu_limit": "0",      # 无限制，自适应分配
                "memory_limit": "0",   # 无限制，自适应分配
                "cpu_request": "2.0",  # 流水线处理需要更多资源，最小保证2核
                "memory_request": "4Gi", # 最小保证4GB内存
                "restart_policy": "Always"
            }
        }
    }

    # 数据库备份部署配置
    database_backup_deployment = {
        "name": "database-backup",
        "version": "1.0.0",
        "tags": ["database", "backup", env_name],
        "description": "数据库备份任务 - 执行数据库备份操作",
        "entrypoint": "tenyy/src/backup_plan/database_backup_flow.py:database_backup",
        "parameters": {
            "backup_type": "base",
            "backup_dir": "/mnt/ssd/tenyy/db_backups"
        },
        "schedule": None,
        "work_pool": {
            "name": "tenyy-unified-pool",
            "work_queue_name": "backup-queue",
            "job_variables": {
                "image": env_config["docker_image"],
                "image_pull_policy": "Always",
                "networks": [env_config["docker_network"]],
                "env": {
                    "PREFECT_LOGGING_LEVEL": "INFO",
                    "PYTHONPATH": "/app",
                    "DOCKER_ENV": "true",
                    "PREFECT_API_URL": "http://prefect-server:4200/api",
                    "POSTGRES_HOST": "app-db",
                    "POSTGRES_PORT": "5432",
                    "POSTGRES_USER": "admin",
                    "POSTGRES_PASSWORD": env_config["postgres_password"],
                    "POSTGRES_DB": "tenyy_app",
                    "BACKUP_DIR": "/mnt/ssd/tenyy/db_backups"
                },
                "volumes": [
                    "/mnt/ssd/tenyy/db_backups:/mnt/ssd/tenyy/db_backups"
                ],
                "cpu_limit": "1.0",
                "memory_limit": "2Gi",
                "cpu_request": "0.5",
                "memory_request": "1Gi",
                "restart_policy": "Never"
            }
        }
    }

    # 数据库恢复部署配置
    database_restore_deployment = {
        "name": "database-restore",
        "version": "1.0.0",
        "tags": ["database", "restore", env_name],
        "description": "数据库恢复任务 - 从备份文件恢复数据库",
        "entrypoint": "tenyy/src/backup_plan/database_backup_flow.py:database_restore",
        "parameters": {
            "backup_file": "/mnt/ssd/tenyy/db_backups/full_backup_20250829_071745.dump"
        },
        "schedule": None,
        "work_pool": {
            "name": "tenyy-unified-pool",
            "work_queue_name": "backup-queue",
            "job_variables": {
                "image": env_config["docker_image"],
                "image_pull_policy": "Always",
                "networks": [env_config["docker_network"]],
                "env": {
                    "PREFECT_LOGGING_LEVEL": "INFO",
                    "PYTHONPATH": "/app",
                    "DOCKER_ENV": "true",
                    "PREFECT_API_URL": "http://prefect-server:4200/api",
                    "POSTGRES_HOST": "app-db",
                    "POSTGRES_PORT": "5432",
                    "POSTGRES_USER": "admin",
                    "POSTGRES_PASSWORD": env_config["postgres_password"],
                    "POSTGRES_DB": "tenyy_app"
                },
                "volumes": [
                    "/mnt/ssd/tenyy/db_backups:/mnt/ssd/tenyy/db_backups"
                ],
                "cpu_limit": "1.0",
                "memory_limit": "2Gi",
                "cpu_request": "0.5",
                "memory_request": "1Gi",
                "restart_policy": "Never"
            }
        }
    }

    # 合并APK分析+SDK匹配 Flow 部署配置
    combine_analysis_deployment = {
        "name": "combine-apk-analysis-and-sdk-match",
        "version": "1.0.0",
        "tags": ["apk", "sdk_match", "combine", env_name],
        "description": "合并 APK 提取与 SDK 匹配的本地Flow，按批循环处理直至无待处理任务，支持Artifacts输出",
        "entrypoint": "tenyy/src/apkinfo_analysis/apk_analysis_flow/combine_analysis_flow.py:combine_analysis_flow",
        "parameters": {
            "limit": 1000,
            "max_workers": 4
        },
        "schedule": None,
        "work_pool": {
            "name": "tenyy-unified-pool",
            "work_queue_name": "analysis-queue",
            "job_variables": {
                "image": env_config["docker_image"],
                "image_pull_policy": "Always",
                "networks": [env_config["docker_network"]],
                "env": {
                    "PREFECT_LOGGING_LEVEL": "INFO",
                    "PYTHONPATH": "/app",
                    "PREFECT_API_URL": "http://prefect-server:4200/api",
                    "DB_HOST": "app-db",
                    "DB_PORT": "5432",
                    "DB_USER": "admin",
                    "DB_PASSWORD": env_config["postgres_password"],
                    "DB_NAME": "tenyy_app"
                },
                "cpu_limit": "0",
                "memory_limit": "0",
                "cpu_request": "1.0",
                "memory_request": "2Gi",
                "restart_policy": "Always"
            }
        }
    }

    # 问询 Gemini Potential Flow 部署配置
    ask_gemini_potential_deployment = {
        "name": "ask-gemini-potential",
        "version": "1.0.0",
        "tags": ["apk", "sdk_kb", "llm", env_name],
        "description": "按 App 分批询问潜在 SDK 并回填知识库链接（batch_size=App 数量；每个 App 内处理其所有包前缀），输出 Artifacts",
        "entrypoint": "tenyy/src/apkinfo_analysis/ask_gemini/ask_gemini_potential_flow.py:ask_gemini_potential_flow",
        "parameters": {
            "batch_size": 5,
            "rpm": 15,
            "cooldown_days": 7,
            "max_batches": 2,
            "dry_run": True,
            "provider": "gemini",
            "model_name": "gemini-2.5-flash",
            "print_prompt_only": False
        },
        "schedule": None,
        "work_pool": {
            "name": "tenyy-unified-pool",
            "work_queue_name": "analysis-queue",
            "job_variables": {
                "image": env_config["docker_image"],
                "image_pull_policy": "Always",
                "networks": [env_config["docker_network"]],
                "env": {
                    "PREFECT_LOGGING_LEVEL": "INFO",
                    "PYTHONPATH": "/app",
                    "PREFECT_API_URL": "http://prefect-server:4200/api",
                    "DB_HOST": "app-db",
                    "DB_PORT": "5432",
                    "DB_USER": "admin",
                    "DB_PASSWORD": env_config["postgres_password"],
                    "DB_NAME": "tenyy_app"
                },
                "cpu_limit": "0",
                "memory_limit": "0",
                "cpu_request": "1.0",
                "memory_request": "2Gi",
                "restart_policy": "Always"
            }
        }
    }

    # 添加所有部署
    config["deployments"] = (
        crawler_deployments
        + [
            apk_deployment,
            continuous_apk_deployment,
            pipeline_apk_deployment,
            combine_analysis_deployment,
            ask_gemini_potential_deployment,
            database_backup_deployment,
            database_restore_deployment,
        ]
    )
    # 针对 K8s 环境进行部署后处理：去除 Docker-only 字段
    if env_name == "k8s":
        for d in config.get("deployments", []):
            wp = d.get("work_pool", {})
            jv = wp.get("job_variables", {})
            # 删除 networks/volumes/restart_policy
            jv.pop("networks", None)
            jv.pop("volumes", None)
            jv.pop("restart_policy", None)
            # 统一镜像拉取策略
            if "image_pull_policy" in jv:
                jv["image_pull_policy"] = "IfNotPresent"
            # 任务容器内访问 Service 更合适
            envv = jv.get("env", {})
            envv["PREFECT_API_URL"] = "http://prefect-server:4200/api"
            # 覆盖数据库密码以匹配集群中 app-db 的 Secret（app-db-secret.DB_PASSWORD）
            # 使用统一配置中的密码，避免因环境默认值不一致导致的认证失败
            envv["POSTGRES_PASSWORD"] = env_config["postgres_password"]
            jv["env"] = envv
            # 指定 K8s Job 目标命名空间，覆盖 work pool 模板的默认值
            jv["namespace"] = "tenyy"
            wp["job_variables"] = jv
            d["work_pool"] = wp
    
    return config

def main():
    """主函数"""
    env_name = sys.argv[1] if len(sys.argv) > 1 else "production"
    
    print(f"🔧 生成 {env_name} 环境的Prefect配置...")
    
    # 生成配置
    config = generate_prefect_yaml(env_name)
    
    # 写入文件
    with open("prefect.yaml", "w", encoding="utf-8") as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    print(f"✅ 已生成 prefect.yaml 配置文件 (环境: {env_name})")
    print(f"📁 宿主机下载目录: {get_env_config(env_name)['host_download_dir']}")

if __name__ == "__main__":
    main()