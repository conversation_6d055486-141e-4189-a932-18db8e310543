kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
name: tenyy
nodes:
  - role: control-plane
    extraMounts:
      # 将 local-path-provisioner 的数据目录持久化到宿主机
      - hostPath: /Users/<USER>/dev/k8s/kind_storage/var
        containerPath: /var/local-path-provisioner
      - hostPath: /Users/<USER>/dev/k8s/kind_storage/opt
        containerPath: /opt/local-path-provisioner
      # 将 Docker Desktop VM 内的 Docker 卷目录映射到 kind 节点
      - hostPath: /var/lib/docker/volumes
        containerPath: /var/lib/docker/volumes
    extraPortMappings:
      # Prefect Server NodePort（Service: prefect-server -> 31420）
      - containerPort: 31420
        hostPort: 4200
        protocol: TCP
      # Duplicati NodePort（Service: duplicati -> 30820）
      - containerPort: 30820
        hostPort: 8200
        protocol: TCP
      # App DB NodePort（Service: app-db -> 31432）映射为宿主机 5432
      - containerPort: 31432
        hostPort: 5432
        protocol: TCP
      # Prefect DB NodePort（Service: prefect-db -> 31433）映射为宿主机 5433
      - containerPort: 31433
        hostPort: 5433
        protocol: TCP
      # 通过 NodePort 暴露，并将宿主机端口保持与原端口一致
      # Docker Registry（Service: docker-registry -> nodePort 31500）
      - containerPort: 31500
        hostPort: 5001
        protocol: TCP
      # Aria2 RPC（Service: aria2-service -> nodePort 31680）
      - containerPort: 31680
        hostPort: 6800
        protocol: TCP
      # Aria2 BT 监听（Service: aria2-service -> nodePort 31688）
      - containerPort: 31688
        hostPort: 6888
        protocol: TCP
      # AriaNg UI（Service: aria2-ui -> nodePort 31690）
      - containerPort: 31690
        hostPort: 6880
        protocol: TCP
