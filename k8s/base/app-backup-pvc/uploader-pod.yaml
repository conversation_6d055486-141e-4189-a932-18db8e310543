apiVersion: v1
kind: Pod
metadata:
  name: backup-uploader
  namespace: tenyy
  labels:
    app: backup-uploader
spec:
  restartPolicy: Always
  containers:
    - name: uploader
      image: busybox:1.36
      command: ["sh", "-c", "sleep 360000"]
      volumeMounts:
        - name: backup-pvc
          mountPath: /mnt/ssd/tenyy/db_backups
  volumes:
    - name: backup-pvc
      persistentVolumeClaim:
        claimName: app-backup-pvc
