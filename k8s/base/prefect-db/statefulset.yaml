apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: prefect-db
spec:
  serviceName: prefect-db
  replicas: 1
  selector:
    matchLabels:
      app: prefect-db
  template:
    metadata:
      labels:
        app: prefect-db
    spec:
      containers:
        - name: postgres
          image: postgres:15
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 5432
              name: tcp-postgres
          env:
            - name: POSTGRES_USER
              value: "prefect"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: prefect-db-secret
                  key: PREFECT_DB_PASSWORD
            - name: POSTGRES_DB
              value: "prefect_server"
          readinessProbe:
            exec:
              command: ["/bin/sh","-c","pg_isready -U prefect -d prefect_server"]
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 6
          livenessProbe:
            exec:
              command: ["/bin/sh","-c","pg_isready -U prefect -d prefect_server"]
            initialDelaySeconds: 30
            periodSeconds: 20
            timeoutSeconds: 5
            failureThreshold: 6
          resources:
            requests:
              cpu: "250m"
              memory: "512Mi"
            limits:
              cpu: "2"
              memory: "5Gi"
          volumeMounts:
            - name: data
              mountPath: /var/lib/postgresql/data
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 20Gi
