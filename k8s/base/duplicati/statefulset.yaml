apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: duplicati
spec:
  serviceName: duplicati
  replicas: 1
  selector:
    matchLabels:
      app: duplicati
  template:
    metadata:
      labels:
        app: duplicati
    spec:
      securityContext:
        runAsUser: 0
        runAsGroup: 0
      containers:
        - name: duplicati
          image: duplicati/duplicati:latest
          imagePullPolicy: IfNotPresent
          command: ["/bin/sh","-lc"]
          args:
            - |
              set -e
              exec /opt/duplicati/duplicati-server \
                --webservice-interface=${DUPLICATI__WEBSERVICE_INTERFACE} \
                --webservice-port=${DUPLICATI__WEBSERVICE_PORT} \
                --server-datafolder=${DUPLICATI__SERVER_DATAFOLDER} \
                --webservice-username="${DUPLICATI__WEBSERVICE_USERNAME}" \
                --webservice-password="${DUPLICATI__WEBSERVICE_PASSWORD}"
          ports:
            - containerPort: 8200
              name: web-ui
          env:
            - name: PUID
              value: "0"
            - name: PGID
              value: "0"
            - name: TZ
              value: "Asia/Shanghai"
            - name: DUPLICATI__WEBSERVICE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: duplicati-web-secret
                  key: WEB_USERNAME
            - name: DUPLICATI__WEBSERVICE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: duplicati-web-secret
                  key: WEB_PASSWORD
            - name: DUPLICATI__WEBSERVICE_INTERFACE
              value: "any"
            - name: DUPLICATI__WEBSERVICE_PORT
              value: "8200"
            - name: DUPLICATI__SERVER_DATAFOLDER
              value: "/config"
          readinessProbe:
            httpGet:
              path: /
              port: 8200
            initialDelaySeconds: 30
            periodSeconds: 10
            failureThreshold: 6
          livenessProbe:
            httpGet:
              path: /
              port: 8200
            initialDelaySeconds: 60
            periodSeconds: 30
            failureThreshold: 6
          resources:
            requests:
              cpu: "500m"
              memory: "512Mi"
            limits:
              cpu: "1000m"
              memory: "1Gi"
          volumeMounts:
            - name: duplicati-config
              mountPath: /config
            - name: duplicati-source
              mountPath: /source
            - name: duplicati-cache
              mountPath: /usr/lib/duplicati/cache
            - name: app-backup
              mountPath: /source/db-backups
            - name: app-db-data
              mountPath: /source/app-db-data
            - name: docker-volumes
              mountPath: /source/docker-volumes
      volumes:
        - name: duplicati-config
          persistentVolumeClaim:
            claimName: duplicati-config
        - name: duplicati-source
          persistentVolumeClaim:
            claimName: duplicati-source
        - name: duplicati-cache
          persistentVolumeClaim:
            claimName: duplicati-cache
        - name: app-backup
          persistentVolumeClaim:
            claimName: app-backup-pvc
        - name: app-db-data
          persistentVolumeClaim:
            claimName: data-app-db-0
        - name: docker-volumes
          hostPath:
            path: /var/lib/docker/volumes
            type: Directory
      affinity:
        podAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - app-db
              topologyKey: kubernetes.io/hostname
