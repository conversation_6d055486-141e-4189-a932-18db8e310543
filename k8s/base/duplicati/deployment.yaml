apiVersion: apps/v1
kind: Deployment
metadata:
  name: duplicati
spec:
  replicas: 1
  selector:
    matchLabels:
      app: duplicati
  template:
    metadata:
      labels:
        app: duplicati
    spec:
      containers:
        - name: duplicati
          image: duplicati/duplicati:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8200
              name: web-ui
          env:
            - name: PUID
              value: "1000"
            - name: PGID
              value: "1000"
            - name: TZ
              value: "Asia/Shanghai"
          readinessProbe:
            httpGet:
              path: /
              port: 8200
            initialDelaySeconds: 30
            periodSeconds: 10
            failureThreshold: 6
          livenessProbe:
            httpGet:
              path: /
              port: 8200
            initialDelaySeconds: 60
            periodSeconds: 30
            failureThreshold: 6
          resources:
            requests:
              cpu: "500m"
              memory: "512Mi"
            limits:
              cpu: "1000m"
              memory: "1Gi"
          volumeMounts:
            - name: duplicati-config
              mountPath: /config
            - name: duplicati-source
              mountPath: /source  # 备份源目录
            - name: duplicati-cache
              mountPath: /usr/lib/duplicati/cache
      volumes:
        - name: duplicati-config
          persistentVolumeClaim:
            claimName: duplicati-config
        - name: duplicati-source
          persistentVolumeClaim:
            claimName: duplicati-source
        - name: duplicati-cache
          persistentVolumeClaim:
            claimName: duplicati-cache
