apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: duplicati-config
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: duplicati-source
spec:
  accessModes:
    - ReadWriteOnce  # 修改为ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: duplicati-cache
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi  # 备份缓存目录
