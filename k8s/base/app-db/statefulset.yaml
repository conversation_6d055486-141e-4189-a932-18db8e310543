apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: app-db
spec:
  serviceName: app-db
  replicas: 1
  selector:
    matchLabels:
      app: app-db
  template:
    metadata:
      labels:
        app: app-db
    spec:
      containers:
        - name: postgres
          image: postgres:17
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 5432
              name: tcp-postgres
          env:
            - name: POSTGRES_USER
              value: "admin"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: app-db-secret
                  key: DB_PASSWORD
            - name: POSTGRES_DB
              value: "tenyy_app"
          readinessProbe:
            exec:
              command: ["/bin/sh","-c","PGPASSWORD=$POSTGRES_PASSWORD pg_isready -U admin -d tenyy_app"]
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 6
          livenessProbe:
            exec:
              command: ["/bin/sh","-c","PGPASSWORD=$POSTGRES_PASSWORD pg_isready -U admin -d tenyy_app"]
            initialDelaySeconds: 30
            periodSeconds: 20
            timeoutSeconds: 5
            failureThreshold: 6
          resources:
            requests:
              cpu: "500m"
              memory: "1Gi"
            limits:
              cpu: "8"
              memory: "16Gi"
          volumeMounts:
            - name: data
              mountPath: /var/lib/postgresql/data
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 20Gi
