apiVersion: batch/v1
kind: CronJob
metadata:
  name: app-db-pg-dump
  namespace: tenyy
spec:
  schedule: "0 * * * *"  # 每小时执行一次
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 2
  failedJobsHistoryLimit: 2
  jobTemplate:
    spec:
      backoffLimit: 1
      template:
        spec:
          restartPolicy: OnFailure
          containers:
            - name: pg-dump
              image: postgres:17
              imagePullPolicy: IfNotPresent
              env:
                - name: PGPASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: app-db-secret
                      key: DB_PASSWORD
              command: ["/bin/sh","-c"]
              args:
                - |
                  set -euo pipefail
                  ts=$(date +"%Y%m%d%H%M%S")
                  outfile="/backup/tenyy_app_${ts}.dump"
                  echo "[pg_dump] writing to ${outfile}"
                  pg_dump \
                    -h app-db \
                    -U admin \
                    -d tenyy_app \
                    -F c \
                    -f "${outfile}"
                  echo "[pg_dump] done: ${outfile}"
                  # 可选：清理超过7天的文件
                  find /backup -type f -name 'tenyy_app_*.dump' -mtime +7 -delete || true
              volumeMounts:
                - name: backup
                  mountPath: /backup
          volumes:
            - name: backup
              persistentVolumeClaim:
                claimName: app-backup-pvc
