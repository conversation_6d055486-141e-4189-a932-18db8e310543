apiVersion: apps/v1
kind: Deployment
metadata:
  name: aria2-ui
spec:
  replicas: 1
  selector:
    matchLabels:
      app: aria2-ui
  template:
    metadata:
      labels:
        app: aria2-ui
    spec:
      containers:
        - name: ariang
          image: p3terx/ariang
          imagePullPolicy: IfNotPresent
          args: ["--port","6880","--ipv6"]
          ports:
            - containerPort: 6880
              name: http
          resources:
            requests:
              cpu: "100m"
              memory: "64Mi"
            limits:
              cpu: "500m"
              memory: "1Gi"
