apiVersion: apps/v1
kind: Deployment
metadata:
  name: aria2-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: aria2-service
  template:
    metadata:
      labels:
        app: aria2-service
    spec:
      containers:
        - name: aria2
          image: p3terx/aria2-pro
          imagePullPolicy: IfNotPresent
          env:
            - name: RPC_SECRET
              valueFrom:
                secretKeyRef:
                  name: aria2-secret
                  key: ARIA2_RPC_TOKEN
            - name: RPC_PORT
              value: "6800"
            - name: LISTEN_PORT
              value: "6888"
            - name: PUID
              value: "1000"
            - name: PGID
              value: "1000"
            - name: TZ
              value: "Asia/Shanghai"
            - name: MAX_CONCURRENT_DOWNLOADS
              value: "25"
            - name: DISK_CACHE
              value: "256M"
            - name: ENABLE_RPC
              value: "true"
            - name: RPC_ALLOW_ORIGIN_ALL
              value: "true"
            - name: RPC_LISTEN_ALL
              value: "true"
            - name: ENABLE_BITTORRENT
              value: "false"
            - name: <PERSON><PERSON><PERSON><PERSON>_METALINK
              value: "false"
          ports:
            - containerPort: 6800
              name: rpc
            - containerPort: 6888
              name: listen
          readinessProbe:
            tcpSocket:
              port: 6800
            initialDelaySeconds: 10
            periodSeconds: 15
            failureThreshold: 6
          livenessProbe:
            tcpSocket:
              port: 6800
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 6
          resources:
            requests:
              cpu: "200m"
              memory: "512Mi"
            limits:
              cpu: "500m"
              memory: "1Gi"
          volumeMounts:
            - name: downloads
              mountPath: /downloads
            - name: config
              mountPath: /config
      volumes:
        - name: downloads
          persistentVolumeClaim:
            claimName: aria2-downloads
        - name: config
          persistentVolumeClaim:
            claimName: aria2-config
