apiVersion: apps/v1
kind: Deployment
metadata:
  name: prefect-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prefect-server
  template:
    metadata:
      labels:
        app: prefect-server
    spec:
      containers:
        - name: prefect-server
          image: prefecthq/prefect:3.4.8-python3.11
          imagePullPolicy: IfNotPresent
          command:
            - /bin/sh
            - -c
            - |
              export PREFECT_API_DATABASE_CONNECTION_URL="postgresql+asyncpg://prefect:${PREFECT_DB_PASSWORD}@prefect-db:5432/prefect_server" && \
              exec prefect server start
          env:
            - name: PREFECT_SERVER_API_HOST
              value: "0.0.0.0"
            - name: PREFECT_ANALYTICS_ENABLED
              value: "false"
            - name: PREFECT_SERVER_LOG_RETENTION_DAYS
              value: "5"
            - name: PREFECT_SERVER_RUN_RETENTION_DAYS
              value: "5"
          envFrom:
            - secretRef:
                name: prefect-db-secret
          ports:
            - containerPort: 4200
              name: http
          readinessProbe:
            httpGet:
              path: /api/health
              port: 4200
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 6
          livenessProbe:
            httpGet:
              path: /api/health
              port: 4200
            initialDelaySeconds: 60
            periodSeconds: 20
            timeoutSeconds: 5
            failureThreshold: 6
          resources:
            requests:
              cpu: "500m"
              memory: "1Gi"
            limits:
              cpu: "2"
              memory: "4Gi"
          volumeMounts:
            - name: prefect-data
              mountPath: /root/.prefect
      volumes:
        - name: prefect-data
          emptyDir: {}
