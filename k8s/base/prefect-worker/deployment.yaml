apiVersion: apps/v1
kind: Deployment
metadata:
  name: prefect-worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prefect-worker
  template:
    metadata:
      labels:
        app: prefect-worker
    spec:
      serviceAccountName: prefect-worker
      containers:
        - name: worker
          image: prefecthq/prefect:3.4.8-python3.11
          imagePullPolicy: IfNotPresent
          command:
            - /bin/sh
            - -c
            - |
              pip install --no-cache-dir prefect-kubernetes prefect-docker && \
              prefect worker start -p tenyy-unified-pool -t kubernetes
          env:
            - name: PREFECT_API_URL
              value: "http://prefect-server:4200/api"
            - name: PREFECT_LOGGING_LEVEL
              value: "INFO"
            - name: POSTGRES_HOST
              value: "app-db"
            - name: POSTGRES_PORT
              value: "5432"
            - name: POSTGRES_USER
              value: "admin"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: app-db-secret
                  key: DB_PASSWORD
            - name: POSTGRES_DB
              value: "tenyy_app"
            - name: DB_HOST
              value: "app-db"
            - name: DB_PORT
              value: "5432"
            - name: DB_USER
              value: "admin"
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: app-db-secret
                  key: DB_PASSWORD
            - name: DB_NAME
              value: "tenyy_app"
            - name: ARIA2_RPC_URL
              value: "http://aria2-service:6800/jsonrpc"
            - name: ARIA2_RPC_TOKEN
              valueFrom:
                secretKeyRef:
                  name: aria2-secret
                  key: ARIA2_RPC_TOKEN
            - name: TENYY_ENV
              value: "production"
            - name: PYTHONPATH
              value: "/app"
          resources:
            requests:
              cpu: "1"
              memory: "2Gi"
            limits:
              cpu: "2"
              memory: "4Gi"
