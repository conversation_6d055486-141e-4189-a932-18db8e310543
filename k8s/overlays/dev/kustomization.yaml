apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: tenyy
resources:
  - ../../base
  - ingress/prefect.yaml
  - ingress/registry.yaml
  - ingress/aria2-ui.yaml
patches:
  # Set storageClassName for PVCs and volumeClaimTemplates
  - target:
      version: v1
      kind: PersistentVolumeClaim
      name: registry-data
    patch: |-
      - op: add
        path: /spec/storageClassName
        value: standard
  - target:
      version: v1
      kind: PersistentVolumeClaim
      name: aria2-downloads
    patch: |-
      - op: add
        path: /spec/storageClassName
        value: standard
  - target:
      version: v1
      kind: PersistentVolumeClaim
      name: aria2-config
    patch: |-
      - op: add
        path: /spec/storageClassName
        value: standard
  - target:
      group: apps
      version: v1
      kind: StatefulSet
      name: app-db
    patch: |-
      - op: add
        path: /spec/volumeClaimTemplates/0/spec/storageClassName
        value: standard
  - target:
      group: apps
      version: v1
      kind: StatefulSet
      name: prefect-db
    patch: |-
      - op: add
        path: /spec/volumeClaimTemplates/0/spec/storageClassName
        value: standard
  # Fix aria2-service probes - use TCP socket for port check
  - target:
      group: apps
      version: v1
      kind: Deployment
      name: aria2-service
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/readinessProbe
        value:
          tcpSocket:
            port: 6800
          initialDelaySeconds: 10
          periodSeconds: 15
          failureThreshold: 6
      - op: replace
        path: /spec/template/spec/containers/0/livenessProbe
        value:
          tcpSocket:
            port: 6800
          initialDelaySeconds: 30
          periodSeconds: 30
          failureThreshold: 6
  # Expose DB services via NodePort (Scheme B) while keeping external ports consistent via kind extraPortMappings
  - target:
      version: v1
      kind: Service
      name: app-db
    patch: |-
      - op: replace
        path: /spec/type
        value: NodePort
      - op: replace
        path: /spec/ports/0
        value:
          name: pg
          port: 5432
          targetPort: 5432
          nodePort: 31432
  - target:
      version: v1
      kind: Service
      name: prefect-db
    patch: |-
      - op: replace
        path: /spec/type
        value: NodePort
      - op: replace
        path: /spec/ports/0
        value:
          name: pg
          port: 5432
          targetPort: 5432
          nodePort: 31433
