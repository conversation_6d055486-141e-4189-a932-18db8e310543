apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: prefect-ui
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
spec:
  rules:
    - host: prefect.local
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: prefect-server
                port:
                  number: 4200
