apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: registry
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
spec:
  rules:
    - host: registry.local
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: docker-registry
                port:
                  number: 5000
