apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: aria2-ui
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: "20m"
spec:
  rules:
    - host: dl.local
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: aria2-ui
                port:
                  number: 6880
