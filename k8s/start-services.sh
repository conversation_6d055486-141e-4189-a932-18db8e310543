#!/bin/bash
# Tenyy Kind 集群自动化端口转发脚本

# 解析命令行参数
BACKGROUND=false
if [[ "$1" == "--background" ]] || [[ "$1" == "-b" ]]; then
    BACKGROUND=true
fi

echo "🚀 启动 Tenyy Kind 集群端口转发..."

# 检查 kubectl 是否可用
if ! command -v kubectl &> /dev/null; then
    echo "❌ 错误: kubectl 未安装或未配置"
    exit 1
fi

# 检查集群状态
echo "🔍 检查集群状态..."
if ! kubectl cluster-info &> /dev/null; then
    echo "❌ 错误: 无法连接到 Kubernetes 集群"
    exit 1
fi

# 等待命名空间就绪
echo "⏳ 等待 tenyy 命名空间就绪..."
if ! kubectl get ns tenyy &> /dev/null; then
    echo "❌ 错误: 未找到 tenyy 命名空间"
    exit 1
fi

# 清理旧的端口转发进程
echo "🧹 清理旧的端口转发进程..."
pkill -f "kubectl port-forward" || true

# 启动端口转发 (后台运行)
echo "🔗 启动端口转发..."

start_pf() {
  local ns=$1; shift
  local resource=$1; shift
  local mapping=$1; shift
  (
    while true; do
      # 自动寻找可用的端点并尝试转发；失败时短暂休眠后重试
      kubectl port-forward -n "$ns" "$resource" "$mapping" >/dev/null 2>&1 || true
      sleep 1
    done
  ) &
}

# Aria2 RPC
start_pf tenyy svc/aria2-service 6800:6800
echo "   Aria2 RPC:     http://localhost:6800"

# Aria2 UI
start_pf tenyy svc/aria2-ui 6880:6880
echo "   Aria2 UI:      http://localhost:6880"

# Prefect
start_pf tenyy svc/prefect-server 4200:4200
echo "   Prefect UI:    http://localhost:4200"

# Docker Registry
start_pf tenyy svc/docker-registry 5000:5000
echo "   Registry:      http://localhost:5000"

# App Database
start_pf tenyy svc/app-db 5432:5432
echo "   App DB:        localhost:5432 (PostgreSQL)"

# Prefect Database
start_pf tenyy svc/prefect-db 5433:5432
echo "   Prefect DB:    localhost:5433 (PostgreSQL)"

# Duplicati Web UI
start_pf tenyy svc/duplicati 8200:8200
echo "   Duplicati UI:  http://localhost:8200"

echo "✅ 端口转发已启动!"

if [[ "$BACKGROUND" == true ]]; then
    echo "📋 后台运行模式 - 进程已启动"
    echo "📌 要停止所有转发，请运行:"
    echo "   pkill -f \"kubectl port-forward\""
    # 保存进程信息
    jobs -p > /tmp/kind-forward-pids 2>/dev/null || true
    disown
    exit 0
else
    echo "📋 前台运行模式 - 按 Ctrl+C 停止"
    echo ""
    echo "📌 要停止所有转发，请运行:"
    echo "   pkill -f \"kubectl port-forward\""
    
    # 保持脚本运行
    wait
fi
