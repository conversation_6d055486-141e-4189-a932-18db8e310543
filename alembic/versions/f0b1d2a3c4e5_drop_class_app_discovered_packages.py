"""drop class_app_discovered_packages

Revision ID: f0b1d2a3c4e5
Revises: e211bbb739be
Create Date: 2025-08-21 08:55:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'f0b1d2a3c4e5'
down_revision: Union[str, Sequence[str], None] = 'e211bbb739be'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema: drop legacy table class_app_discovered_packages."""
    # Drop indexes first (defensive), then drop table
    try:
        op.drop_index('idx_app_discovered_packages_package_name', table_name='class_app_discovered_packages')
    except Exception:
        pass
    try:
        op.drop_index('idx_app_discovered_packages_last_checked', table_name='class_app_discovered_packages')
    except Exception:
        pass
    try:
        op.drop_index('idx_app_discovered_packages_app_version_id', table_name='class_app_discovered_packages')
    except Exception:
        pass

    try:
        op.drop_table('class_app_discovered_packages')
    except Exception:
        # Table may already be absent (e.g., on fresh databases)
        pass


def downgrade() -> None:
    """Downgrade schema: recreate class_app_discovered_packages with latest known columns and indexes.

    Latest schema after b5537f39bf91 added column `type` and made `package_name` TEXT.
    """
    op.create_table(
        'class_app_discovered_packages',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('app_version_id', sa.Integer(), nullable=False),
        sa.Column('package_name', sa.Text(), nullable=False),
        sa.Column('type', sa.String(length=50), nullable=True),
        sa.Column('last_checked', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_app_discovered_packages_app_version_id', 'class_app_discovered_packages', ['app_version_id'], unique=False)
    op.create_index('idx_app_discovered_packages_last_checked', 'class_app_discovered_packages', ['last_checked'], unique=False)
    op.create_index('idx_app_discovered_packages_package_name', 'class_app_discovered_packages', ['package_name'], unique=False)
