"""add kb_last_ask

Revision ID: 1c7082aecb8e
Revises: ad26ac890dbb
Create Date: 2025-08-17 16:06:54.071336

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '1c7082aecb8e'
down_revision: Union[str, Sequence[str], None] = 'ad26ac890dbb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 仅新增两个列，避免对其它对象的无关改动
    op.add_column('class_app_version_sdks', sa.Column('kb_last_ask_at', sa.DateTime(), nullable=True))
    op.add_column('class_app_version_sdks', sa.Column('kb_last_ask_result', sa.Integer(), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    # 仅回滚两个列
    op.drop_column('class_app_version_sdks', 'kb_last_ask_result')
    op.drop_column('class_app_version_sdks', 'kb_last_ask_at')
