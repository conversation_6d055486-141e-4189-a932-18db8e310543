"""add category tables

Revision ID: ad26ac890dbb
Revises: e211bbb739be
Create Date: 2025-08-16 13:20:40.424491

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ad26ac890dbb'
down_revision: Union[str, Sequence[str], None] = 'e211bbb739be'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('class_category',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('class_subcategory',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('category_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['class_category.id'], ondelete='RESTRICT'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('category_id', 'name', name='uq_category_subcategory_name')
    )
    op.add_column('class_sdk_knowledge_base', sa.Column('subcategory_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'class_sdk_knowledge_base', 'class_subcategory', ['subcategory_id'], ['id'], ondelete='SET NULL')
    op.drop_column('class_sdk_knowledge_base', 'subcategory')
    op.drop_column('class_sdk_knowledge_base', 'category')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('class_sdk_knowledge_base', sa.Column('category', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.add_column('class_sdk_knowledge_base', sa.Column('subcategory', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'class_sdk_knowledge_base', type_='foreignkey')
    op.drop_column('class_sdk_knowledge_base', 'subcategory_id')
    op.drop_table('class_subcategory')
    op.drop_table('class_category')
    # ### end Alembic commands ###
