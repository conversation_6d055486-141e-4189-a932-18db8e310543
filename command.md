TENYY_ENV=test alembic current

TENYY_ENV=test alembic stamp head

### 查看forward
ps aux | grep "kubectl port-forward"


### 自动产生迁移的代码

TENYY_ENV=test alembic revision --autogenerate -m "add new tables"


### dbt change
dbt run -s marts.mv_sdks_lite

IMPORT FOREIGN SCHEMA marts
  LIMIT TO (mv_sdks_lite)
  FROM SERVER tenyy_src INTO fdw_marts;


  -- 第一次创建
CREATE MATERIALIZED VIEW IF NOT EXISTS serving.mv_sdks_lite AS
SELECT
  id::bigint                                AS id,
  sdk_name,
  package_prefix::varchar(255)               AS package_prefix,
  description,
  official_web::varchar(1024)                AS official_web,
  company_name::varchar(255)                 AS company_name,
  subcategory_id
FROM fdw_marts.mv_sdks_lite
WITH NO DATA;

-- 唯一键与查询索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_sdks_lite_id ON serving.mv_sdks_lite(id);
CREATE INDEX IF NOT EXISTS idx_mv_sdks_lite_subcat_id ON serving.mv_sdks_lite(subcategory_id);

-- 首次加载/刷新（后续按计划任务定时）
REFRESH MATERIALIZED VIEW  serving.mv_sdks_lite;



### refresh supabase
-- 强烈建议在非事务里执行
REFRESH MATERIALIZED VIEW CONCURRENTLY serving.mv_app_sdks_detail;
REFRESH MATERIALIZED VIEW CONCURRENTLY serving.mv_app_sdk_count_by_category;
REFRESH MATERIALIZED VIEW CONCURRENTLY serving.mv_app_sdk_market_share;

### 测试与production数据库升级同步

TENYY_ENV=test alembic upgrade head

TENYY_ENV=production alembic upgrade head

### make frontend
make step-frontend \
  REGISTRY=crpi-3akfk1c833x0o8uo.cn-hangzhou.personal.cr.aliyuncs.com/tenyy \
  FRONTEND_IMAGE=frontend:latest

### make step2
 make step2 LOAD_TO_KIND=1

 ### docker desktop dns
 host.docker.internal

 ### next.js launch dev
 npm run dev

 ### change the name space

 kubectl config set-context --current --namespace=tenyy

 ### launch cluster
kubectl apply -k k8s/overlays/dev

### stop cluster
kubectl delete -k k8s/overlays/dev


### install local-path-provisioner
kubectl apply -f https://raw.githubusercontent.com/rancher/local-path-provisioner/v0.0.24/deploy/local-path-storage.yaml


cd /home/<USER>/dev/tenyy-dind
python3 -m tenyy.src.apkinfo_analysis.apk_analysis_flow.apk_analysis_flow

cd /home/<USER>/dev/tenyy-dind
python3 -m tenyy.src.apkinfo_analysis.apk_analysis_flow.sdk_match_flow

### sql
-- 查看特定应用的所有版本发现的 SDK
SELECT * FROM app_version_sdk_view WHERE app_id = 'com.example.app' ORDER BY app_version_id, is_potential;

-- 查看特定应用版本发现的所有 SDK
SELECT * FROM app_version_sdk_view WHERE app_version_id = 123;

-- 统计每个应用发现的 SDK 数量
SELECT app_id, app_name, COUNT(*) as sdk_count 
FROM app_version_sdk_view 
GROUP BY app_id, app_name 
ORDER BY sdk_count DESC;

-- 查看所有已知 SDK（非潜在 SDK）
SELECT DISTINCT app_name, app_version, sdk_name 
FROM app_version_sdk_view 
WHERE is_potential = false 
ORDER BY app_name, app_version;

-- 查看所有潜在 SDK
SELECT app_name, app_version, sdk_package_prefix, signal_count
FROM app_version_sdk_view 
WHERE is_potential = true 
ORDER BY signal_count DESC;


python -m tenyy.src.apkinfo_analysis.ask_gemini.ask_gemini_potential   --batch-size 5 --rpm 8 --cooldown-days 7 --max-batches 2   --dry-run 0 --provider gemini --model-name gemini-2.5-flash   --print-prompt-only 0


### 删掉配对成功的记录，还原 version_sdk
BEGIN;

WITH del_kb AS (
  DELETE FROM class_sdk_knowledge_base
  WHERE status = 'gemini_asked'
  RETURNING id
),
upd AS (
  UPDATE class_app_version_sdks s
  SET
    sdk_knowledge_base_id = NULL,
    kb_last_ask_at       = NULL,
    kb_last_ask_result   = NULL,
    match_type = CASE
                   WHEN s.match_type IN ('prefix', 'regex') THEN 'potential'
                   ELSE s.match_type
                 END
  WHERE s.sdk_knowledge_base_id IN (SELECT id FROM del_kb)
  RETURNING 1
)
SELECT
  (SELECT COUNT(*) FROM del_kb) AS deleted_kb,
  (SELECT COUNT(*) FROM upd)    AS reset_rows;

COMMIT;
