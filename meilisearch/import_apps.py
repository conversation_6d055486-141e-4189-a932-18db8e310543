#!/usr/bin/env python3
"""
导入 marts.mv_apps_with_detail 到 Meilisearch 索引 `apps`
- Meilisearch: http://localhost:7700，主键 app_id（已创建）
- 数据来源: Postgres tenyy_app 库的 marts.mv_apps_with_detail（MV-only 最终口径）

注意：按你的要求，数据库连接与 Meilisearch Key 采用硬编码常量。
"""
from __future__ import annotations

import os
import sys
import time
from typing import Iterable, Dict, Any, List
from datetime import datetime, date, time
from decimal import Decimal
import re
import hashlib

import psycopg2  # type: ignore
import psycopg2.extras  # type: ignore
from meilisearch import Client
import argparse

# === 硬编码配置（按需修改）===
MEILI_URL = "http://localhost:7700"
MEILI_API_KEY = "bx3QfVJJh34XXsb4IlKjUqLmP5puIQ1X-Zgnh6q__jI"  # Admin/Master Key

# Postgres 连接串（推荐 URL 形式）
DATABASE_URL = "postgres://admin:zhangdi168@127.0.0.1:5432/tenyy_app?sslmode=disable"

INDEX_UID = "apps"
PRIMARY_KEY = "app_id"  # 需与 Meilisearch 中的索引主键一致
BATCH_SIZE = int(os.getenv("BATCH_SIZE", "1000"))

SQL = """
SELECT
  app_id,
  app_name AS name,
  description,
  icon_url,
  category,
  tags,
  download_count,
  last_updated AS updated_at
FROM marts.mv_apps_with_detail
"""

# SDKs 数据源
SDKS_INDEX_UID = "sdks"
SDKS_PRIMARY_KEY = "id"
SQL_SDKS = """
SELECT
  id::bigint AS id,
  sdk_name,
  package_prefix,
  description,
  official_web,
  company_name,
  subcategory_id
FROM marts.mv_sdks_lite
"""


def stream_rows(cur) -> Iterable[Dict[str, Any]]:
    # 使用服务器端游标减少内存占用
    cur.itersize = BATCH_SIZE
    while True:
        rows = cur.fetchmany(BATCH_SIZE)
        if not rows:
            break
        for r in rows:
            yield dict(r)


def normalize(value: Any) -> Any:
    """将 PG 返回的数据转换为可 JSON 序列化的格式。
    - datetime/date/time -> ISO8601 字符串
    - Decimal -> float（或 int）
    - list/dict -> 递归
    其他类型原样返回。
    """
    if isinstance(value, (datetime, date, time)):
        return value.isoformat()
    if isinstance(value, Decimal):
        # 如果是整数小数，转为 int；否则转 float
        return int(value) if value == value.to_integral_value() else float(value)
    if isinstance(value, list):
        return [normalize(v) for v in value]
    if isinstance(value, dict):
        return {k: normalize(v) for k, v in value.items()}
    return value


def sanitize_pk(value: str) -> str:
    """将主键标准化为 Meilisearch 支持的 ID：
    仅允许 [A-Za-z0-9_-]，其余转为 '_'; 并截断到 511 字节。
    若结果为空，则使用 md5 做后缀，确保非空且稳定。
    """
    if value is None:
        value = ""
    s = re.sub(r"[^A-Za-z0-9_-]", "_", str(value))
    if not s:
        s = "id_" + hashlib.md5(str(value).encode("utf-8")).hexdigest()[:16]
    # 截断到 511 字节（不是字符数）
    b = s.encode("utf-8")
    if len(b) > 511:
        b = b[:511]
        # 确保不截断在多字节中间
        s = b.decode("utf-8", errors="ignore")
    return s
def ensure_index(client: Client):
    """确保索引存在；若不存在则按 PRIMARY_KEY 创建，并更新基础设置。"""
    # 获取或创建索引
    try:
        idx = client.get_index(INDEX_UID)
    except Exception:
        task = client.create_index(INDEX_UID, {"primaryKey": PRIMARY_KEY})
        wait_tasks(client, task)
        idx = client.get_index(INDEX_UID)
    # 可选：更新常用设置并等待完成
    task = idx.update_settings({
        "searchableAttributes": ["name", "description", "category", "tags"],
        "filterableAttributes": ["category", "tags"],
        "sortableAttributes": ["download_count", "updated_at"],
    })
    wait_tasks(client, task)
def ensure_sdks_index(client: Client):
    """确保 sdks 索引存在并设置基础属性。"""
    try:
        idx = client.get_index(SDKS_INDEX_UID)
    except Exception:
        task = client.create_index(SDKS_INDEX_UID, {"primaryKey": SDKS_PRIMARY_KEY})
        wait_tasks(client, task)
        idx = client.get_index(SDKS_INDEX_UID)
    task = idx.update_settings({
        "searchableAttributes": ["sdk_name", "company_name", "description", "package_prefix"],
        "filterableAttributes": ["subcategory_id"],
        "sortableAttributes": [],
    })
    wait_tasks(client, task)


def wait_tasks(client: Client, *tasks_or_uids):
    """等待一批 Task 完成。兼容 Task 对象或 taskUid（int/dict）。"""
    for t in tasks_or_uids:
        if t is None:
            continue
        # 兼容 Task 对象、dict 或直接是 uid
        uid = getattr(t, "task_uid", None)
        if uid is None:
            if isinstance(t, dict):
                uid = t.get("taskUid")
            elif isinstance(t, int):
                uid = t
        if uid is None:
            continue
        # 使用官方提供的方法等待
        client.wait_for_task(uid, timeout_in_ms=600_000, interval_in_ms=200)


def get_count(conn, sql: str) -> int:
    """执行 count 语句并返回整数。"""
    with conn.cursor() as cur:
        cur.execute(sql)
        row = cur.fetchone()
        return int(row[0]) if row and row[0] is not None else 0

def main():
    parser = argparse.ArgumentParser(description="Import data into Meilisearch")
    parser.add_argument("--target", choices=["apps", "sdks", "both"], default="both",
                        help="选择导入对象：apps / sdks / both (默认)")
    args = parser.parse_args()
    if not MEILI_API_KEY:
        print("[ERR] 缺少 MEILI_API_KEY。", file=sys.stderr)
        sys.exit(1)

    # 连接 Meilisearch
    client = Client(MEILI_URL, MEILI_API_KEY)
    # 连接 Postgres（使用硬编码 URL）
    dsn = DATABASE_URL
    conn = psycopg2.connect(dsn)
    try:
        # apps 索引
        if args.target in ("apps", "both"):
            ensure_index(client)
            with conn.cursor(name="apps_cursor", cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                # 预估总数用于进度显示
                total_rows = get_count(conn, "SELECT count(*) FROM marts.mv_apps_with_detail")
                cur.execute(SQL)

                batch: List[Dict[str, Any]] = []
                total = 0
                for row in stream_rows(cur):
                    # 确保包含主键字段 app_id
                    if not row.get("app_id"):
                        continue  # 跳过无主键的数据（按理 MV 不会为空）
                    # 规范化为可 JSON 序列化
                    norm = {k: normalize(v) for k, v in row.items()}
                    # 主键清洗：保留原值到 app_id_raw，用清洗后的值作为主键 app_id
                    original_id = str(norm.get("app_id", ""))
                    norm["app_id_raw"] = original_id
                    norm["app_id"] = sanitize_pk(original_id)
                    batch.append(norm)
                    if len(batch) >= BATCH_SIZE:
                        task = client.index(INDEX_UID).add_documents(batch)
                        wait_tasks(client, task)
                        total += len(batch)
                        # 进度打印
                        if total_rows > 0:
                            pct = (total / total_rows) * 100
                            print(f"[apps] {total}/{total_rows} ({pct:.1f}%)")
                        batch.clear()
                if batch:
                    task = client.index(INDEX_UID).add_documents(batch)
                    wait_tasks(client, task)
                    total += len(batch)
                    if total_rows > 0:
                        pct = (total / total_rows) * 100
                        print(f"[apps] {total}/{total_rows} ({pct:.1f}%)")

                print(f"[OK] 导入完成，共 {total} 条文档写入索引 '{INDEX_UID}'。")

        # 导入 SDKs
        if args.target in ("sdks", "both"):
            ensure_sdks_index(client)
            # 先加载 subcategory_id -> slugs 的映射
            slug_map: Dict[int, Dict[str, str]] = {}
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur_slug:
                cur_slug.execute("SELECT subcategory_id::bigint AS subcategory_id, category_slug, subcategory_slug FROM marts.mv_subcategories_slug")
                for row in cur_slug.fetchall():
                    slug_map[int(row["subcategory_id"])] = {
                        "category_slug": str(row["category_slug"]),
                        "subcategory_slug": str(row["subcategory_slug"]),
                    }

            with conn.cursor(name="sdks_cursor", cursor_factory=psycopg2.extras.RealDictCursor) as cur2:
                total_rows2 = get_count(conn, "SELECT count(*) FROM marts.mv_sdks")
                cur2.execute(SQL_SDKS)
                batch2: List[Dict[str, Any]] = []
                total2 = 0
                for row in stream_rows(cur2):
                    if row.get("id") is None or not row.get("sdk_name"):
                        continue
                    r = {k: normalize(v) for k, v in row.items()}
                    # 主键直接使用 mv_sdks 的 id
                    r[SDKS_PRIMARY_KEY] = r["id"]
                    # 附加 slug 字段（如可用）
                    sc_id = r.get("subcategory_id")
                    if sc_id is not None:
                        try:
                            sc_id_int = int(sc_id)
                            if sc_id_int in slug_map:
                                r.update(slug_map[sc_id_int])
                        except Exception:
                            pass
                    batch2.append(r)
                    if len(batch2) >= BATCH_SIZE:
                        task = client.index(SDKS_INDEX_UID).add_documents(batch2)
                        wait_tasks(client, task)
                        total2 += len(batch2)
                        if total_rows2 > 0:
                            pct = (total2 / total_rows2) * 100
                            print(f"[sdks] {total2}/{total_rows2} ({pct:.1f}%)")
                        batch2.clear()
                if batch2:
                    task = client.index(SDKS_INDEX_UID).add_documents(batch2)
                    wait_tasks(client, task)
                    total2 += len(batch2)
                    if total_rows2 > 0:
                        pct = (total2 / total_rows2) * 100
                        print(f"[sdks] {total2}/{total_rows2} ({pct:.1f}%)")
                print(f"[OK] 导入完成，共 {total2} 条文档写入索引 '{SDKS_INDEX_UID}'。")

    finally:
        conn.close()


if __name__ == "__main__":
    main()
