# meilisync configuration for syncing final MV/view to Meilisearch

debug: false

progress:
  type: file
  path: ./meilisync-progress.json

# 源数据库（你的 PostgreSQL）
source:
  type: postgres
  host: 127.0.0.1
  port: 5432
  user: admin
  password: zhangdi168
  database: tenyy_app
  sslmode: disable

# Meilisearch 连接
meilisearch:
  api_url: http://localhost:7700
  api_key: bx3QfVJJh34XXsb4IlKjUqLmP5puIQ1X-Zgnh6q__jI   # Master/Admin 仅用于后台同步
  insert_size: 1000
  insert_interval: 5

# 同步任务（MV-only：推荐使用 refresh 全量+索引交换）
sync:
  - table: marts.mv_apps_with_detail
    index: apps
    full: true
    # 字段映射：左=数据库列，右=Meili文档字段
    fields:
      app_id: id
      app_name: name
      description: description
      icon_url: icon_url
      category: category
      tags: tags
      download_count: download_count
      last_updated: updated_at