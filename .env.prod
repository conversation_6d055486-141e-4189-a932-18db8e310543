# 生产环境 - 环境变量

# 生产环境 - 应用数据库配置
POSTGRES_USER=admin
POSTGRES_PASSWORD=zhangdi168
POSTGRES_DB=tenyy_app
POSTGRES_PORT_PROD=5432

# 生产环境 - Prefect 后端数据库配置
PREFECT_DB_USER=prefect
PREFECT_DB_PASSWORD=zhangdi168
PREFECT_DB_NAME=prefect_server

# Supabase 本地配置（更新后）
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
SUPABASE_JWT_SECRET=super-secret-jwt-token-with-at-least-32-characters-long
S3_ACCESS_KEY=625729a08b95bf1b7ff351a663f3a23c
S3_SECRET_KEY=850181e4652dd023b7a98c58ae0d2d34bd487ee0cc3254aed6eda37307425907
S3_REGION=local
S3_HOST=http://127.0.0.1:54321/storage/v1/s3

# 数据库连接（更新后）
DB_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
