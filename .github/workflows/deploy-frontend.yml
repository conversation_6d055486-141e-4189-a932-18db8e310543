name: Deploy Frontend to Alibaba Cloud FC

on:
  push:
    branches: [ main ]
    paths:
      - 'frontend/**'
      - 's.yaml'
  workflow_dispatch: {}

concurrency:
  group: deploy-frontend
  cancel-in-progress: true

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: npm
          cache-dependency-path: frontend/package-lock.json

      - name: Install Serverless Devs CLI
        run: npm i -g @serverless-devs/s

      - name: Write Alicloud credentials
        run: |
          mkdir -p ~/.aliyun
          cat > ~/.aliyun/config.json << 'EOF'
          {"current":"default","profiles":[{"name":"default","access_key_id":"${{ secrets.ALICLOUD_ACCESS_KEY_ID }}","access_key_secret":"${{ secrets.ALICLOUD_ACCESS_KEY_SECRET }}","language":"zh","region_id":"cn-hangzhou"}]}
          EOF

      - name: Install deps
        working-directory: frontend
        run: npm ci

      - name: Build
        working-directory: frontend
        run: npm run build

      - name: Deploy to FC
        run: s deploy -y
