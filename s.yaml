service: tenyy-frontend
frameworkVersion: '3'

provider:
  name: aliyun
  runtime: custom.debian12
  region: cn-hangzhou
  credentials: ~/.aliyun/config.json

functions:
  tenyy_frontend:
    name: tenyy_frontend
    description: Next.js frontend for tenyy
    runtime: custom.debian12
    code: ./frontend
    handler: handler
    memorySize: 512
    timeout: 15
    instanceConcurrency: 10
    cpu: 0.5
    diskSize: 10240
    environmentVariables:
      NODE_ENV: production
    customRuntimeConfig:
      command:
        - node
        - node_modules/next/dist/bin/next
        - start
        - -p
        - '3000'
      port: 3000
    logConfig:
      enableInstanceMetrics: true
      enableRequestMetrics: true
    internetAccess: true
    role: acs:ram::1774752256557315:role/aliyunfcdefaultrole

custom:
  customDomain:
    domainName: test.tiktech.cn
    protocol: HTTP
    routeConfig:
      routes:
        - path: '/*'
          functionName: tenyy_frontend
          qualifier: LATEST
