import type { Config } from "tailwindcss";

export default {
  darkMode: ['class', 'class'],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
  	extend: {
  		colors: {
  			satin: {
  				'50': 'var(--color-satin-linen-50)',
  				'100': 'var(--color-satin-linen-100)',
  				'200': 'var(--color-satin-linen-200)',
  				'300': 'var(--color-satin-linen-300)',
  				'400': 'var(--color-satin-linen-400)',
  				'500': 'var(--color-satin-linen-500)',
  				'600': 'var(--color-satin-linen-600)',
  				'700': 'var(--color-satin-linen-700)',
  				'800': 'var(--color-satin-linen-800)',
  				'900': 'var(--color-satin-linen-900)',
  				'950': 'var(--color-satin-linen-950)'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				light: 'var(--color-satin-linen-600)',
  				dark: 'var(--color-satin-linen-800)',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			danger: {
  				DEFAULT: '#DC2626',
  				light: '#F87171',
  				dark: '#B91C1C'
  			},
  			neutral: {
  				DEFAULT: '#F8FAFC',
  				dark: '#1E293B',
  				darker: '#0F172A'
  			},
  			text: {
  				DEFAULT: '#0F172A',
  				light: '#64748B',
  				dark: '#F8FAFC'
  			},
  			surface: {
  				light: '#FFFFFF',
  				dark: '#1E293B'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				light: '#7DD3FC',
  				dark: '#0EA5E9',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		boxShadow: {
  			subtle: '0 1px 3px rgba(0,0,0,0.05)',
  			hover: '0 4px 6px -1px rgba(0,0,0,0.08), 0 2px 4px -1px rgba(0,0,0,0.06)'
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;
