export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          extensions?: Json
          operationName?: string
          query?: string
          variables?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      app: {
        Row: {
          _ab_cdc_deleted_at: string | null
          _ab_cdc_lsn: number | null
          _ab_cdc_updated_at: string | null
          _airbyte_extracted_at: string
          _airbyte_generation_id: number | null
          _airbyte_meta: Json
          _airbyte_raw_id: string
          category: string | null
          created_at: string | null
          description: string | null
          developer: string | null
          icon: string | null
          id: string | null
          is_game: boolean | null
          metadata: string | null
          name: string | null
          updated_at: string | null
        }
        Insert: {
          _ab_cdc_deleted_at?: string | null
          _ab_cdc_lsn?: number | null
          _ab_cdc_updated_at?: string | null
          _airbyte_extracted_at: string
          _airbyte_generation_id?: number | null
          _airbyte_meta: Json
          _airbyte_raw_id: string
          category?: string | null
          created_at?: string | null
          description?: string | null
          developer?: string | null
          icon?: string | null
          id?: string | null
          is_game?: boolean | null
          metadata?: string | null
          name?: string | null
          updated_at?: string | null
        }
        Update: {
          _ab_cdc_deleted_at?: string | null
          _ab_cdc_lsn?: number | null
          _ab_cdc_updated_at?: string | null
          _airbyte_extracted_at?: string
          _airbyte_generation_id?: number | null
          _airbyte_meta?: Json
          _airbyte_raw_id?: string
          category?: string | null
          created_at?: string | null
          description?: string | null
          developer?: string | null
          icon?: string | null
          id?: string | null
          is_game?: boolean | null
          metadata?: string | null
          name?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      app_version: {
        Row: {
          _ab_cdc_deleted_at: string | null
          _ab_cdc_lsn: number | null
          _ab_cdc_updated_at: string | null
          _airbyte_extracted_at: string
          _airbyte_generation_id: number | null
          _airbyte_meta: Json
          _airbyte_raw_id: string
          analysis_result: string | null
          analyze_error_message: string | null
          analyze_status: string | null
          analyzed_at: string | null
          apk_hash: string | null
          apk_size: number | null
          app_update_time: string | null
          booking_gift: string | null
          booking_url: string | null
          booking_user_cnt: string | null
          category: string | null
          channel_info: string | null
          cloud_game_info: string | null
          cp_id: string | null
          description: string | null
          detail_template: string | null
          developer: string | null
          download_count: number | null
          download_num: number | null
          download_status: string | null
          download_url: string | null
          editor_intro: string | null
          error_message: string | null
          exe_download_url: string | null
          features: string | null
          filename: string | null
          game_type: string | null
          icon_url: string | null
          icp_entity: string | null
          icp_number: string | null
          id: number | null
          ios_app_link_info: string | null
          ios_url: string | null
          is_booking: boolean | null
          is_cloud_game: boolean | null
          is_free: boolean | null
          is_game: boolean | null
          is_latest: boolean | null
          is_pc_yyb_available: boolean | null
          last_attempt: string | null
          last_updated: string | null
          libraries: string | null
          min_sdk: number | null
          ms_store_id: string | null
          ms_store_status: string | null
          operator: string | null
          permissions: string | null
          permissions_list: string | null
          price: number | null
          privacy_agreement: string | null
          public_time: string | null
          rating: number | null
          release_date: string | null
          restrict_level: string | null
          retry_count: number | null
          review_count: number | null
          show_text: string | null
          snap_shots: string | null
          store_app_id: number | null
          store_description: string | null
          store_metadata: string | null
          store_name: string | null
          syzs_download_num: string | null
          tag_alias: string | null
          tags: string | null
          target_sdk: number | null
          username: string | null
          version: string | null
          video: string | null
        }
        Insert: {
          _ab_cdc_deleted_at?: string | null
          _ab_cdc_lsn?: number | null
          _ab_cdc_updated_at?: string | null
          _airbyte_extracted_at: string
          _airbyte_generation_id?: number | null
          _airbyte_meta: Json
          _airbyte_raw_id: string
          analysis_result?: string | null
          analyze_error_message?: string | null
          analyze_status?: string | null
          analyzed_at?: string | null
          apk_hash?: string | null
          apk_size?: number | null
          app_update_time?: string | null
          booking_gift?: string | null
          booking_url?: string | null
          booking_user_cnt?: string | null
          category?: string | null
          channel_info?: string | null
          cloud_game_info?: string | null
          cp_id?: string | null
          description?: string | null
          detail_template?: string | null
          developer?: string | null
          download_count?: number | null
          download_num?: number | null
          download_status?: string | null
          download_url?: string | null
          editor_intro?: string | null
          error_message?: string | null
          exe_download_url?: string | null
          features?: string | null
          filename?: string | null
          game_type?: string | null
          icon_url?: string | null
          icp_entity?: string | null
          icp_number?: string | null
          id?: number | null
          ios_app_link_info?: string | null
          ios_url?: string | null
          is_booking?: boolean | null
          is_cloud_game?: boolean | null
          is_free?: boolean | null
          is_game?: boolean | null
          is_latest?: boolean | null
          is_pc_yyb_available?: boolean | null
          last_attempt?: string | null
          last_updated?: string | null
          libraries?: string | null
          min_sdk?: number | null
          ms_store_id?: string | null
          ms_store_status?: string | null
          operator?: string | null
          permissions?: string | null
          permissions_list?: string | null
          price?: number | null
          privacy_agreement?: string | null
          public_time?: string | null
          rating?: number | null
          release_date?: string | null
          restrict_level?: string | null
          retry_count?: number | null
          review_count?: number | null
          show_text?: string | null
          snap_shots?: string | null
          store_app_id?: number | null
          store_description?: string | null
          store_metadata?: string | null
          store_name?: string | null
          syzs_download_num?: string | null
          tag_alias?: string | null
          tags?: string | null
          target_sdk?: number | null
          username?: string | null
          version?: string | null
          video?: string | null
        }
        Update: {
          _ab_cdc_deleted_at?: string | null
          _ab_cdc_lsn?: number | null
          _ab_cdc_updated_at?: string | null
          _airbyte_extracted_at?: string
          _airbyte_generation_id?: number | null
          _airbyte_meta?: Json
          _airbyte_raw_id?: string
          analysis_result?: string | null
          analyze_error_message?: string | null
          analyze_status?: string | null
          analyzed_at?: string | null
          apk_hash?: string | null
          apk_size?: number | null
          app_update_time?: string | null
          booking_gift?: string | null
          booking_url?: string | null
          booking_user_cnt?: string | null
          category?: string | null
          channel_info?: string | null
          cloud_game_info?: string | null
          cp_id?: string | null
          description?: string | null
          detail_template?: string | null
          developer?: string | null
          download_count?: number | null
          download_num?: number | null
          download_status?: string | null
          download_url?: string | null
          editor_intro?: string | null
          error_message?: string | null
          exe_download_url?: string | null
          features?: string | null
          filename?: string | null
          game_type?: string | null
          icon_url?: string | null
          icp_entity?: string | null
          icp_number?: string | null
          id?: number | null
          ios_app_link_info?: string | null
          ios_url?: string | null
          is_booking?: boolean | null
          is_cloud_game?: boolean | null
          is_free?: boolean | null
          is_game?: boolean | null
          is_latest?: boolean | null
          is_pc_yyb_available?: boolean | null
          last_attempt?: string | null
          last_updated?: string | null
          libraries?: string | null
          min_sdk?: number | null
          ms_store_id?: string | null
          ms_store_status?: string | null
          operator?: string | null
          permissions?: string | null
          permissions_list?: string | null
          price?: number | null
          privacy_agreement?: string | null
          public_time?: string | null
          rating?: number | null
          release_date?: string | null
          restrict_level?: string | null
          retry_count?: number | null
          review_count?: number | null
          show_text?: string | null
          snap_shots?: string | null
          store_app_id?: number | null
          store_description?: string | null
          store_metadata?: string | null
          store_name?: string | null
          syzs_download_num?: string | null
          tag_alias?: string | null
          tags?: string | null
          target_sdk?: number | null
          username?: string | null
          version?: string | null
          video?: string | null
        }
        Relationships: []
      }
      class_app_version_sdks: {
        Row: {
          _ab_cdc_deleted_at: string | null
          _ab_cdc_lsn: number | null
          _ab_cdc_updated_at: string | null
          _airbyte_extracted_at: string
          _airbyte_generation_id: number | null
          _airbyte_meta: Json
          _airbyte_raw_id: string
          app_version_id: number | null
          child_packages: string | null
          created_at: string | null
          is_potential: boolean | null
          kb_last_ask_at: string | null
          kb_last_ask_result: number | null
          match_type: string | null
          sdk_knowledge_base_id: number | null
          sdk_package_prefix: string | null
          signal_count: number | null
          type: string | null
          updated_at: string | null
        }
        Insert: {
          _ab_cdc_deleted_at?: string | null
          _ab_cdc_lsn?: number | null
          _ab_cdc_updated_at?: string | null
          _airbyte_extracted_at: string
          _airbyte_generation_id?: number | null
          _airbyte_meta: Json
          _airbyte_raw_id: string
          app_version_id?: number | null
          child_packages?: string | null
          created_at?: string | null
          is_potential?: boolean | null
          kb_last_ask_at?: string | null
          kb_last_ask_result?: number | null
          match_type?: string | null
          sdk_knowledge_base_id?: number | null
          sdk_package_prefix?: string | null
          signal_count?: number | null
          type?: string | null
          updated_at?: string | null
        }
        Update: {
          _ab_cdc_deleted_at?: string | null
          _ab_cdc_lsn?: number | null
          _ab_cdc_updated_at?: string | null
          _airbyte_extracted_at?: string
          _airbyte_generation_id?: number | null
          _airbyte_meta?: Json
          _airbyte_raw_id?: string
          app_version_id?: number | null
          child_packages?: string | null
          created_at?: string | null
          is_potential?: boolean | null
          kb_last_ask_at?: string | null
          kb_last_ask_result?: number | null
          match_type?: string | null
          sdk_knowledge_base_id?: number | null
          sdk_package_prefix?: string | null
          signal_count?: number | null
          type?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      class_category: {
        Row: {
          _ab_cdc_deleted_at: string | null
          _ab_cdc_lsn: number | null
          _ab_cdc_updated_at: string | null
          _airbyte_extracted_at: string
          _airbyte_generation_id: number | null
          _airbyte_meta: Json
          _airbyte_raw_id: string
          created_at: string | null
          id: number | null
          name: string | null
        }
        Insert: {
          _ab_cdc_deleted_at?: string | null
          _ab_cdc_lsn?: number | null
          _ab_cdc_updated_at?: string | null
          _airbyte_extracted_at: string
          _airbyte_generation_id?: number | null
          _airbyte_meta: Json
          _airbyte_raw_id: string
          created_at?: string | null
          id?: number | null
          name?: string | null
        }
        Update: {
          _ab_cdc_deleted_at?: string | null
          _ab_cdc_lsn?: number | null
          _ab_cdc_updated_at?: string | null
          _airbyte_extracted_at?: string
          _airbyte_generation_id?: number | null
          _airbyte_meta?: Json
          _airbyte_raw_id?: string
          created_at?: string | null
          id?: number | null
          name?: string | null
        }
        Relationships: []
      }
      class_sdk_knowledge_base: {
        Row: {
          _ab_cdc_deleted_at: string | null
          _ab_cdc_lsn: number | null
          _ab_cdc_updated_at: string | null
          _airbyte_extracted_at: string
          _airbyte_generation_id: number | null
          _airbyte_meta: Json
          _airbyte_raw_id: string
          brief_message: string | null
          company_name: string | null
          compliance_instructions_url: string | null
          description: string | null
          detection_type: number | null
          id: number | null
          is_regex_rule: boolean | null
          last_checked_at: string | null
          official_web: string | null
          package_prefix: string | null
          privacy_policy_url: string | null
          regex_pattern: string | null
          sdk_document_url: string | null
          sdk_name: string | null
          status: string | null
          subcategory_id: number | null
          tags: string | null
          version: string | null
        }
        Insert: {
          _ab_cdc_deleted_at?: string | null
          _ab_cdc_lsn?: number | null
          _ab_cdc_updated_at?: string | null
          _airbyte_extracted_at: string
          _airbyte_generation_id?: number | null
          _airbyte_meta: Json
          _airbyte_raw_id: string
          brief_message?: string | null
          company_name?: string | null
          compliance_instructions_url?: string | null
          description?: string | null
          detection_type?: number | null
          id?: number | null
          is_regex_rule?: boolean | null
          last_checked_at?: string | null
          official_web?: string | null
          package_prefix?: string | null
          privacy_policy_url?: string | null
          regex_pattern?: string | null
          sdk_document_url?: string | null
          sdk_name?: string | null
          status?: string | null
          subcategory_id?: number | null
          tags?: string | null
          version?: string | null
        }
        Update: {
          _ab_cdc_deleted_at?: string | null
          _ab_cdc_lsn?: number | null
          _ab_cdc_updated_at?: string | null
          _airbyte_extracted_at?: string
          _airbyte_generation_id?: number | null
          _airbyte_meta?: Json
          _airbyte_raw_id?: string
          brief_message?: string | null
          company_name?: string | null
          compliance_instructions_url?: string | null
          description?: string | null
          detection_type?: number | null
          id?: number | null
          is_regex_rule?: boolean | null
          last_checked_at?: string | null
          official_web?: string | null
          package_prefix?: string | null
          privacy_policy_url?: string | null
          regex_pattern?: string | null
          sdk_document_url?: string | null
          sdk_name?: string | null
          status?: string | null
          subcategory_id?: number | null
          tags?: string | null
          version?: string | null
        }
        Relationships: []
      }
      class_subcategory: {
        Row: {
          _ab_cdc_deleted_at: string | null
          _ab_cdc_lsn: number | null
          _ab_cdc_updated_at: string | null
          _airbyte_extracted_at: string
          _airbyte_generation_id: number | null
          _airbyte_meta: Json
          _airbyte_raw_id: string
          category_id: number | null
          created_at: string | null
          id: number | null
          name: string | null
        }
        Insert: {
          _ab_cdc_deleted_at?: string | null
          _ab_cdc_lsn?: number | null
          _ab_cdc_updated_at?: string | null
          _airbyte_extracted_at: string
          _airbyte_generation_id?: number | null
          _airbyte_meta: Json
          _airbyte_raw_id: string
          category_id?: number | null
          created_at?: string | null
          id?: number | null
          name?: string | null
        }
        Update: {
          _ab_cdc_deleted_at?: string | null
          _ab_cdc_lsn?: number | null
          _ab_cdc_updated_at?: string | null
          _airbyte_extracted_at?: string
          _airbyte_generation_id?: number | null
          _airbyte_meta?: Json
          _airbyte_raw_id?: string
          category_id?: number | null
          created_at?: string | null
          id?: number | null
          name?: string | null
        }
        Relationships: []
      }
      store_app: {
        Row: {
          _ab_cdc_deleted_at: string | null
          _ab_cdc_lsn: number | null
          _ab_cdc_updated_at: string | null
          _airbyte_extracted_at: string
          _airbyte_generation_id: number | null
          _airbyte_meta: Json
          _airbyte_raw_id: string
          app_id: string | null
          id: number | null
          store_id: string | null
          store_name: string | null
          store_type: string | null
        }
        Insert: {
          _ab_cdc_deleted_at?: string | null
          _ab_cdc_lsn?: number | null
          _ab_cdc_updated_at?: string | null
          _airbyte_extracted_at: string
          _airbyte_generation_id?: number | null
          _airbyte_meta: Json
          _airbyte_raw_id: string
          app_id?: string | null
          id?: number | null
          store_id?: string | null
          store_name?: string | null
          store_type?: string | null
        }
        Update: {
          _ab_cdc_deleted_at?: string | null
          _ab_cdc_lsn?: number | null
          _ab_cdc_updated_at?: string | null
          _airbyte_extracted_at?: string
          _airbyte_generation_id?: number | null
          _airbyte_meta?: Json
          _airbyte_raw_id?: string
          app_id?: string | null
          id?: number | null
          store_id?: string | null
          store_name?: string | null
          store_type?: string | null
        }
        Relationships: []
      }
      subscriptions: {
        Row: {
          cancel_at_period_end: boolean | null
          created_at: string | null
          current_period_end: string | null
          id: string
          price_id: string | null
          status: string | null
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          cancel_at_period_end?: boolean | null
          created_at?: string | null
          current_period_end?: string | null
          id?: string
          price_id?: string | null
          status?: string | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          cancel_at_period_end?: boolean | null
          created_at?: string | null
          current_period_end?: string | null
          id?: string
          price_id?: string | null
          status?: string | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_preferences: {
        Row: {
          created_at: string
          has_completed_onboarding: boolean | null
          id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          has_completed_onboarding?: boolean | null
          id?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          has_completed_onboarding?: boolean | null
          id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      user_trials: {
        Row: {
          id: string
          is_trial_used: boolean | null
          trial_end_time: string
          trial_start_time: string | null
          user_id: string
        }
        Insert: {
          id?: string
          is_trial_used?: boolean | null
          trial_end_time: string
          trial_start_time?: string | null
          user_id: string
        }
        Update: {
          id?: string
          is_trial_used?: boolean | null
          trial_end_time?: string
          trial_start_time?: string | null
          user_id?: string
        }
        Relationships: []
      }
      users: {
        Row: {
          created_at: string
          deleted_at: string | null
          email: string | null
          id: string
          is_deleted: boolean | null
          reactivated_at: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          deleted_at?: string | null
          email?: string | null
          id: string
          is_deleted?: boolean | null
          reactivated_at?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          deleted_at?: string | null
          email?: string | null
          id?: string
          is_deleted?: boolean | null
          reactivated_at?: string | null
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      api_app_sdk_count_by_category: {
        Row: {
          app_id: string | null
          category_name: string | null
          sdk_count: number | null
          subcategory_name: string | null
        }
        Relationships: []
      }
      api_app_sdk_market_share: {
        Row: {
          cnt: number | null
          kb_sdk_name: string | null
          subcategory_id: number | null
        }
        Relationships: []
      }
      api_app_sdks_detail: {
        Row: {
          app_id: string | null
          category_name: string | null
          match_type: string | null
          sdk_name: string | null
          subcategory_name: string | null
        }
        Relationships: []
      }
      api_apps_by_sdk_with_downloads: {
        Row: {
          app_id: string | null
          app_name: string | null
          download_max: number | null
          kb_sdk_name: string | null
          kb_subcategory_id: number | null
        }
        Relationships: []
      }
      api_apps_by_sdk_with_icon: {
        Row: {
          app_id: string | null
          app_name: string | null
          download_max: number | null
          icon: string | null
          kb_sdk_name: string | null
          kb_subcategory_id: number | null
        }
        Relationships: []
      }
      api_apps_with_detail: {
        Row: {
          analysis_result: Json | null
          analyzed_at: string | null
          apk_hash: string | null
          apk_size: number | null
          app_id: string | null
          app_name: string | null
          app_update_time: string | null
          app_version_id: number | null
          booking_gift: string | null
          booking_url: string | null
          booking_user_cnt: string | null
          category: string | null
          channel_info: string | null
          cp_id: string | null
          description: string | null
          detail_json: Json | null
          detail_template: string | null
          developer: string | null
          download_count: number | null
          download_num: number | null
          download_url: string | null
          editor_intro: string | null
          exe_download_url: string | null
          features: Json | null
          game_type: string | null
          icon_url: string | null
          icp_entity: string | null
          icp_number: string | null
          ios_app_link_info: string | null
          ios_url: string | null
          is_booking: boolean | null
          is_cloud_game: boolean | null
          is_free: boolean | null
          is_game: boolean | null
          is_latest: boolean | null
          is_pc_yyb_available: boolean | null
          last_updated: string | null
          libraries: Json | null
          min_sdk: number | null
          ms_store_id: string | null
          ms_store_status: string | null
          operator: string | null
          permissions: Json | null
          permissions_list: Json | null
          price: number | null
          privacy_agreement: string | null
          public_time: string | null
          rating: number | null
          release_date: string | null
          restrict_level: string | null
          review_count: number | null
          show_text: string | null
          snap_shots: string | null
          store_app_name: string | null
          store_app_pk: number | null
          store_description: string | null
          store_id: string | null
          store_name: string | null
          store_type: string | null
          syzs_download_num: string | null
          tag_alias: string | null
          tags: string | null
          target_sdk: number | null
          username: string | null
          version: string | null
          video: string | null
        }
        Relationships: []
      }
      api_categories_slug: {
        Row: {
          category_id: number | null
          category_name: string | null
          category_slug: string | null
          sdk_count: number | null
          subcategory_count: number | null
        }
        Relationships: []
      }
      api_sdks: {
        Row: {
          description: string | null
          id: number | null
          official_web: string | null
          package_prefix: string | null
          sdk_name: string | null
          subcategory_id: number | null
        }
        Relationships: []
      }
      api_sdks_lite: {
        Row: {
          company_name: string | null
          description: string | null
          id: number | null
          official_web: string | null
          package_prefix: string | null
          sdk_name: string | null
          subcategory_id: number | null
        }
        Relationships: []
      }
      api_subcategories_slug: {
        Row: {
          category_id: number | null
          category_name: string | null
          category_slug: string | null
          sdk_count: number | null
          subcategory_id: number | null
          subcategory_name: string | null
          subcategory_slug: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      postgres_fdw_disconnect: {
        Args: { "": string }
        Returns: boolean
      }
      postgres_fdw_disconnect_all: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      postgres_fdw_get_connections: {
        Args: Record<PropertyKey, never>
        Returns: Record<string, unknown>[]
      }
      postgres_fdw_handler: {
        Args: Record<PropertyKey, never>
        Returns: unknown
      }
      refresh_mv_app_sdk_market_share: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {},
  },
} as const

