{"mcpServers": {"stripe": {"command": "npx", "args": ["-y", "@stripe/mcp"], "env": {"STRIPE_SECRET_KEY": "your_stripe_test_key_here"}}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "your_supabase_access_token_here"]}, "github": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN", "ghcr.io/github/github-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_personal_access_token_here"}}}}