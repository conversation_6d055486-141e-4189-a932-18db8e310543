import { defineConfig } from 'drizzle-kit'

// Drizzle 只负责“生成 SQL 迁移”和为服务端代码提供 TS 类型。
// 迁移输出目录统一到仓库根的 supabase/migrations，保持单一迁移通道。
// 连接串使用本地 Supabase 容器（建议放到 frontend/.env.local）。
// 例如：DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

export default defineConfig({
  schema: './db/schema.ts',
  out: '../supabase/migrations',
  dialect: 'postgresql',
  dbCredentials: {
    url: process.env.DATABASE_URL as string,
  },
  strict: true,
})
