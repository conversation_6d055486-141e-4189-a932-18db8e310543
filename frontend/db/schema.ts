// Drizzle schema placeholder.
// 在这里按需定义你的表结构（仅当你需要 Drizzle 管理“表/索引”等应用侧结构时）。
// 视图（public.api_*）、物化视图（serving.mv_* / marts.mv_*）、授权与 RLS 等
// 仍建议用原生 SQL 放在仓库根的 supabase/migrations/ 下进行管理。

// 举例（后续需要时再启用）：
// import { pgTable, text, integer, timestamp } from 'drizzle-orm/pg-core'
// export const demoTable = pgTable('demo_table', {
//   id: integer('id').primaryKey().generatedAlwaysAsIdentity(),
//   name: text('name').notNull(),
//   createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
// })

export {} // 保持为模块，避免编译器报错
