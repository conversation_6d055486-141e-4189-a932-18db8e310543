"use client";

import Link from "next/link";
import { Home, Search, Briefcase, Building2, List, Settings, Webhook, Database, KeyRound } from "lucide-react";

export default function DashboardSidebar() {
  const Item = ({ href, icon: Icon, label }: { href: string; icon: any; label: string }) => (
    <Link
      href={href}
      className="flex items-center gap-2 px-3 py-2 rounded-md text-sm text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-neutral/40"
    >
      <Icon className="h-4 w-4" />
      <span>{label}</span>
    </Link>
  );

  return (
    <aside className="w-56 shrink-0 border-r border-slate-200 dark:border-slate-700 bg-white/60 dark:bg-neutral-900/60 backdrop-blur-sm p-3 space-y-3">
      <div className="px-2 text-xs font-semibold text-slate-500 dark:text-slate-400">Home</div>
      <Item href="/dashboard" icon={Home} label="Home" />

      <div className="px-2 pt-2 text-xs font-semibold text-slate-500 dark:text-slate-400">Search</div>
      <Item href="/search/jobs" icon={Briefcase} label="Jobs" />
      <Item href="/search/companies" icon={Building2} label="Companies" />

      <div className="px-2 pt-2 text-xs font-semibold text-slate-500 dark:text-slate-400">Company lists</div>
      <Item href="/lists" icon={List} label="Lists" />

      <div className="px-2 pt-2 text-xs font-semibold text-slate-500 dark:text-slate-400">Settings</div>
      <Item href="/settings" icon={Settings} label="Settings" />

      <div className="px-2 pt-2 text-xs font-semibold text-slate-500 dark:text-slate-400">Developers</div>
      <Item href="/developers/webhooks" icon={Webhook} label="Webhooks" />
      <Item href="/developers/datasets" icon={Database} label="Datasets" />
      <Item href="/developers/api-keys" icon={KeyRound} label="API Keys" />

      <div className="mt-4 p-3 rounded-lg border border-slate-200 dark:border-slate-700 text-xs text-slate-600 dark:text-slate-300">
        <div className="flex items-center justify-between"><span>Usage</span><span className="text-slate-400">—</span></div>
        <div className="mt-2">
          <div className="flex items-center justify-between"><span>Company credits</span><span>14 / 50</span></div>
          <div className="flex items-center justify-between"><span>API credits</span><span>0 / 200</span></div>
        </div>
        <Link href="/upgrade" className="mt-3 inline-flex w-full justify-center rounded-md bg-primary text-white py-1.5 text-xs">Upgrade</Link>
      </div>
    </aside>
  );
}
