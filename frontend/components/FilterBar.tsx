"use client"

import { useEffect, useRef, useState } from "react"
import { supabase as supabasePublicClient } from '@/utils/supabase'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { readFiltersFromUrl, writeFiltersToUrl } from '@/utils/routerFilters'

const MEILI_HOST = process.env.NEXT_PUBLIC_MEILI_URL || "http://localhost:7700"
const MEILI_API_KEY = process.env.NEXT_PUBLIC_MEILI_KEY || ""
const INDEX_APPS = "apps"

export default function FilterBar() {
  const [open, setOpen] = useState(false)
  const [q, setQ] = useState("")
  const panelRef = useRef<HTMLDivElement | null>(null)
  const [hits, setHits] = useState<{ value: string; count: number }[]>([])
  const [selected, setSelected] = useState<{ slug: string; name: string }[]>([])
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const router = useRouter()

  // 点击外部关闭
  useEffect(() => {
    function onDoc(e: MouseEvent) {
      if (!panelRef.current) return
      if (!panelRef.current.contains(e.target as Node)) setOpen(false)
    }
    if (open) document.addEventListener("mousedown", onDoc)
    return () => document.removeEventListener("mousedown", onDoc)
  }, [open])

  // facet-search 候选
  useEffect(() => {
    let aborted = false
    async function run() {
      try {
        const headers: Record<string, string> = { "Content-Type": "application/json" }
        if (MEILI_API_KEY) headers["Authorization"] = `Bearer ${MEILI_API_KEY}`
        const body = { facetName: "subcategory_name", q, filter: [], limit: 20 }
        const res = await fetch(`${MEILI_HOST}/indexes/${INDEX_APPS}/facet-search`, { method: "POST", headers, body: JSON.stringify(body) })
        const data = await res.json()
        const fac = (data?.facetHits ?? []).map((x: any) => ({ value: String(x.value), count: Number(x.count || 0) }))
        if (!aborted) setHits(fac)
        // Fallback：facet 结果为空时（或 q 为空时首次打开），尝试从 Supabase 拉取子分类
        if (!aborted && fac.length === 0) {
          const { data: subs } = await supabasePublicClient
            .from('mv_subcategories_slug')
            .select('subcategory_name, sdk_count')
            .order('sdk_count', { ascending: false })
            .limit(30)
          if (!aborted && Array.isArray(subs)) {
            const uniq = new Map<string, number>()
            for (const r of subs) {
              const name = String((r as any).subcategory_name || '')
              const cnt = Number((r as any).sdk_count || 0)
              if (name) uniq.set(name, (uniq.get(name) ?? 0) + cnt)
            }
            const list = Array.from(uniq.entries()).map(([value, count]) => ({ value, count }))
            setHits(list)
          }
        }
      } catch { if (!aborted) setHits([]) }
    }
    if (open) run()
    return () => { aborted = true }
  }, [q, open])

  // 广播选中到全局（SearchBox 可监听）
  useEffect(() => {
    const slugs = selected.map(s => s.slug)
    const names = selected.map(s => s.name)
    window.dispatchEvent(new CustomEvent("filters:changed", { detail: { subcategory_slug: slugs, subcategory_name: names, subcategory: selected } }))
  }, [selected])

  // 同步选中到 URL（过渡期：与 CustomEvent 并存）
  useEffect(() => {
    if (!searchParams || !pathname) return
    const slugs = selected.map(s => s.slug)
    writeFiltersToUrl(router, pathname, searchParams, { subcats: slugs }, { replace: true })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selected])

  // 从 URL 初始化/响应前进后退
  useEffect(() => {
    if (!searchParams) return
    const { subcats } = readFiltersFromUrl(searchParams)
    const current = selected.map(s => s.slug).join(',')
    const next = subcats.join(',')
    if (current !== next) {
      setSelected(subcats.map(slug => ({ slug, name: slug })))
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams])

  function toSlug(name: string) {
    return name.toLowerCase().replace(/&/g, "and").replace(/\s+/g, "-").replace(/[^a-z0-9\-]/g, "")
  }
  function toggle(name: string) {
    const slug = toSlug(name)
    setSelected(prev => prev.find(s => s.slug === slug) ? prev.filter(s => s.slug !== slug) : [...prev, { slug, name }])
  }

  return (
    <div className="relative">
      <button
        type="button"
        className="inline-flex items-center gap-2 rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-neutral-darker px-3 py-1.5 text-xs sm:text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-50 dark:hover:bg-neutral-800"
        onClick={() => setOpen(v => !v)}
        aria-expanded={open}
      >
        <span>Subcategory is any of</span>
        {selected.length > 0 && (
          <span className="ml-1 inline-flex items-center rounded bg-emerald-50 text-emerald-700 px-1.5 py-0.5 text-[10px] ring-1 ring-emerald-200">{selected.length}</span>
        )}
      </button>

      {open && (
        <div ref={panelRef} className="absolute z-50 mt-2 w-72 sm:w-96 rounded-md border border-gray-200 dark:border-gray-700 bg-surface-light dark:bg-surface-dark shadow-lg p-2">
          <input
            value={q}
            onChange={(e) => setQ(e.target.value)}
            placeholder="Search subcategories..."
            className="w-full mb-2 px-2 py-1.5 text-sm rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-neutral-darker"
          />
          <div className="max-h-64 overflow-auto divide-y divide-gray-100/60 dark:divide-gray-800/60">
            {hits.map((h) => {
              const slug = toSlug(h.value)
              const checked = !!selected.find(s => s.slug === slug)
              return (
                <label key={slug} className="flex items-center justify-between gap-2 px-2 py-1.5 cursor-pointer hover:bg-neutral/40 dark:hover:bg-neutral-dark">
                  <span className="flex items-center gap-2">
                    <input type="checkbox" className="h-3.5 w-3.5" checked={checked} onChange={() => toggle(h.value)} />
                    <span className="text-sm text-neutral-800 dark:text-neutral-200">{h.value}</span>
                  </span>
                  <span className="text-[11px] text-neutral-500">{h.count}</span>
                </label>
              )
            })}
            {hits.length === 0 && (
              <div className="px-2 py-3 text-xs text-neutral-500">No matches</div>
            )}
          </div>
          {selected.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-1">
              {selected.map(s => (
                <button key={s.slug} onClick={() => toggle(s.name)} className="inline-flex items-center gap-1 rounded bg-satin-100 text-satin-700 px-1.5 py-0.5 text-[11px] ring-1 ring-black/5">
                  <span>{s.name}</span>
                  <span aria-hidden>×</span>
                </button>
              ))}
              <button onClick={() => setSelected([])} className="ml-auto text-[11px] text-rose-600 hover:underline">Clear all</button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
