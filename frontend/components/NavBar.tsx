'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'

function humanize(slug: string) {
  try {
    const s = decodeURIComponent(slug || '')
    return s.replace(/-/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase())
  } catch {
    return slug
  }
}

export default function NavBar() {
  const pathname = usePathname() || '/'
  const parts = pathname.split('/').filter(Boolean)

  // Build breadcrumb items
  const items: { href: string; label: string }[] = [{ href: '/', label: 'Home' }]
  if (parts.length > 0) {
    let acc = ''
    for (let i = 0; i < parts.length; i++) {
      acc += '/' + parts[i]
      const label = i === 0 && parts[i] === 'categories' ? 'Categories' : humanize(parts[i])
      items.push({ href: acc, label })
    }
  }

  return (
    <nav className="w-full bg-white/80 backdrop-blur border-b border-satin-200 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 h-12 flex items-center">
        <ol className="flex items-center text-sm text-satin-700 gap-2 overflow-x-auto whitespace-nowrap">
          {items.map((it, idx) => (
            <li key={it.href} className="flex items-center gap-2">
              {idx === items.length - 1 ? (
                <span className="text-slate-900 font-medium truncate max-w-[28ch]" title={it.label}>{it.label}</span>
              ) : (
                <Link href={it.href} className="hover:underline truncate max-w-[24ch]" title={it.label}>{it.label}</Link>
              )}
              {idx < items.length - 1 && <span className="text-satin-500">/</span>}
            </li>
          ))}
        </ol>
        <div className="ml-auto"/>
      </div>
    </nav>
  )
}
