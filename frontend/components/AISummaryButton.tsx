'use client'

import { useRef, useState, useEffect } from 'react'

type SdkItem = { category_name: string | null, subcategory_name: string | null, sdk_name: string, match_type?: string | null }

type Props = {
  app: {
    app_id: string
    app_name?: string
    description?: string | null
    download_count?: number | null
    rating?: any
  }
  sdkList: SdkItem[]
}

export default function AISummaryButton({ app, sdkList }: Props) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [text, setText] = useState<string>('')
  const [ask, setAsk] = useState<string>('请用 200-300 字总结该 App 的 SDK 用途与启示，并给出 3-5 条可执行建议。')
  const [thinking, setThinking] = useState<string>('')
  const [aborting, setAborting] = useState(false)
  const abortRef = useRef<AbortController | null>(null)
  const endRef = useRef<HTMLDivElement | null>(null)
  let dotsTimer: NodeJS.Timeout | null = null

  async function runSummary() {
    setLoading(true)
    setError(null)
    setText('')
    setThinking('思考中')
    // 简易动态省略号动画
    let dots = 0
    if (dotsTimer) clearInterval(dotsTimer)
    dotsTimer = setInterval(() => {
      dots = (dots + 1) % 4
      setThinking('思考中' + '.'.repeat(dots))
    }, 400)
    try {
      const controller = new AbortController()
      abortRef.current = controller
      const res = await fetch('/api/ai/summary', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ app, sdkList, ask }),
        signal: controller.signal,
      })
      if (!res.ok || !res.body) throw new Error(`请求失败：${res.status}`)
      const reader = res.body.getReader()
      const decoder = new TextDecoder('utf-8')
      let firstChunk = true
      while (true) {
        const { value, done } = await reader.read()
        if (done) break
        const chunk = decoder.decode(value, { stream: true })
        if (firstChunk) {
          // 首次拿到内容，结束“思考中”动画
          firstChunk = false
          if (dotsTimer) clearInterval(dotsTimer)
          setThinking('')
        }
        setText(prev => prev + chunk)
      }
    } catch (e: any) {
      if (e?.name === 'AbortError') {
        setError(null)
      } else {
        setError(e.message || '未知错误')
      }
    } finally {
      if (dotsTimer) clearInterval(dotsTimer)
      setThinking('')
      setLoading(false)
      setAborting(false)
      abortRef.current = null
    }
  }

  function stopSummary() {
    if (abortRef.current) {
      setAborting(true)
      abortRef.current.abort()
    }
  }

  function copyText() {
    if (!text) return
    navigator.clipboard?.writeText(text)
  }

  useEffect(() => {
    if (endRef.current) {
      endRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [text])

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <input
          className="input input-sm flex-1"
          value={ask}
          onChange={(e) => setAsk(e.target.value)}
          placeholder="向 AI 追问（可选）"
        />
        <button className="btn btn-sm" onClick={runSummary} disabled={loading}>
          {loading ? '生成中…' : 'AI 总结 SDK 用途'}
        </button>
        {loading ? (
          <button className="btn btn-sm" onClick={stopSummary} disabled={aborting}>
            {aborting ? '停止中…' : '停止'}
          </button>
        ) : (
          <button className="btn btn-sm" onClick={copyText} disabled={!text}>复制</button>
        )}
      </div>
      {thinking && !text && (
        <div className="text-sm text-neutral-600">{thinking}</div>
      )}
      {error && <div className="text-sm text-red-600">{error}</div>}
      {text && (
        <div className="prose prose-sm max-w-none whitespace-pre-wrap p-3 rounded border bg-white">
          {text}
          <div ref={endRef} />
        </div>
      )}
    </div>
  )
}
