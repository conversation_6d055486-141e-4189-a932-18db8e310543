'use client'

import { useState } from 'react'
import AISummaryPanel from '@/components/AISummaryPanel'

type SdkItem = { category_name: string | null, subcategory_name: string | null, sdk_name: string, match_type?: string | null }

type Props = {
  app: {
    app_id: string
    app_name?: string
    description?: string | null
    download_count?: number | null
    rating?: any
  }
  sdkList: SdkItem[]
}

export default function AISummaryFloat({ app, sdkList }: Props) {
  const [open, setOpen] = useState(false)

  return (
    <>
      {/* 悬浮触发按钮 */}
      <button
        className="fixed bottom-6 right-6 z-40 btn"
        onClick={() => setOpen(true)}
        aria-label="打开 AI 总结"
      >
        AI 总结
      </button>

      {/* 浮窗（固定大小，内部可滚动） */}
      {open && (
        <div className="fixed inset-0 z-50">
          {/* 遮罩 */}
          <div className="absolute inset-0 bg-black/30" onClick={() => setOpen(false)} />
          {/* 面板 */}
          <div className="absolute bottom-6 right-6 w-[min(92vw,840px)] h-[70vh] card p-4 shadow-2xl border bg-background flex flex-col">
            <div className="flex items-center justify-between pb-2">
              <div className="text-sm font-semibold">AI 总结：这个 App 的 SDK 用途</div>
              <button className="btn btn-xs" onClick={() => setOpen(false)}>关闭</button>
            </div>
            <div className="flex-1 min-h-0">
              <AISummaryPanel app={app} sdkList={sdkList} className="h-full" />
            </div>
          </div>
        </div>
      )}
    </>
  )
}
