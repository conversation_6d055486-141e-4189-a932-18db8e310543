"use client"

import SearchBox from './SearchBox'
import FilterBar from './FilterBar'

// UnifiedSearch：将搜索输入与子分类筛选合并在一条工具栏上
// 说明：当前版本直接复用已改造好的 SearchBox 与 FilterBar（都以 URL 为单一数据源）
// 后续可逐步把 FilterBar 的 facet 与 SearchBox 的搜索合并为一个请求层
export default function UnifiedSearch() {
  return (
    <div className="w-full">
      <div className="flex items-stretch gap-2">
        <div className="flex-1">
          <SearchBox />
        </div>
        <div className="flex-shrink-0">
          <FilterBar />
        </div>
      </div>
    </div>
  )
}
