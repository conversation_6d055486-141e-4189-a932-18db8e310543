"use client"

import { useEffect, useMemo, useRef, useState } from "react"
import Link from "next/link"
import { cn } from "@/lib/utils"

// AI Elements
import { Conversation, ConversationContent, ConversationScrollButton } from "@/components/ai-elements/conversation"
import { Message } from "@/components/ai-elements/message"
import { Response } from "@/components/ai-elements/response"
import { Loader } from "@/components/ai-elements/loader"
import { PromptInput, PromptInputBody, PromptInputTextarea, PromptInputToolbar, PromptInputSubmit } from "@/components/ai-elements/prompt-input"

// 颜色映射（公司标签）
function badgeColor(hint?: string) {
  const m: Record<string, string> = {
    rose: "bg-rose-100 text-rose-700",
    amber: "bg-amber-100 text-amber-700",
    emerald: "bg-emerald-100 text-emerald-700",
    sky: "bg-sky-100 text-sky-700",
    violet: "bg-violet-100 text-violet-700",
  }
  return m[hint || ""] || "bg-satin-100 text-satin-700"
}

// 将分类/子分类转为 URL 友好的 slug
function slugify(s: string) {
  return (s || "")
    .toLowerCase()
    .replace(/&/g, "and")
    .replace(/\s+/g, "-")
    .replace(/[^a-z0-9\-]/g, "")
}

// 分类图标（彩色圆形徽标 + SVG，根据主分类关键字）
function categoryIcon(label: string) {
  const lc = (label || '').toLowerCase()
  const style = lc.includes('advertis')
    ? { fg: 'text-amber-600', bg: 'bg-amber-100', key: 'ad' }
    : lc.includes('analytic')
    ? { fg: 'text-sky-600', bg: 'bg-sky-100', key: 'analytics' }
    : lc.includes('security')
    ? { fg: 'text-rose-600', bg: 'bg-rose-100', key: 'security' }
    : lc.includes('develop')
    ? { fg: 'text-violet-600', bg: 'bg-violet-100', key: 'dev' }
    : (lc.includes('audio') || lc.includes('video'))
    ? { fg: 'text-emerald-600', bg: 'bg-emerald-100', key: 'av' }
    : { fg: 'text-neutral-600', bg: 'bg-neutral-100', key: 'default' }

  const icon = (() => {
    const cls = `w-4 h-4 ${style.fg}`
    switch (style.key) {
      case 'ad':
        return (<svg className={cls} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4 8h12l4-3v14l-4-3H4V8Z" stroke="currentColor" strokeWidth="1.5" strokeLinejoin="round"/></svg>)
      case 'analytics':
        return (<svg className={cls} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4 20V10m6 10V4m6 16v-6m6 6V8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/></svg>)
      case 'security':
        return (<svg className={cls} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 3l7 3v5c0 5-3.5 9-7 10-3.5-1-7-5-7-10V6l7-3Z" stroke="currentColor" strokeWidth="1.5"/></svg>)
      case 'dev':
        return (<svg className={cls} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 6l-6 6 6 6M16 6l6 6-6 6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/></svg>)
      case 'av':
        return (<svg className={cls} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4 7h10v10H4z" stroke="currentColor" strokeWidth="1.5"/><path d="M20 7l-4 2v6l4 2V7Z" fill="currentColor"/></svg>)
      default:
        return (<svg className={cls} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="8" stroke="currentColor" strokeWidth="1.5"/></svg>)
    }
  })()

  return (
    <span className={`inline-flex h-6 w-6 items-center justify-center rounded-full ring-1 ring-black/5 ${style.bg}`}>
      {icon}
    </span>
  )
}

type SdkItem = { category_name: string | null; subcategory_name: string | null; sdk_name: string; match_type?: string | null }

type Props = {
  app: {
    app_id: string
    app_name?: string
    description?: string | null
    download_count?: number | null
    rating?: any
  }
  sdkList: SdkItem[]
  className?: string
}

export default function AISummaryPanel({ app, sdkList, className }: Props) {
  const [showPrompt, setShowPrompt] = useState(false)
  useEffect(() => {
    if (typeof window === 'undefined') return
    const sp = new URLSearchParams(window.location.search)
    const v = sp.get('debug') || ''
    if (v === 'true' || v === 'ture') setShowPrompt(true)
    else setShowPrompt(false)
  }, [])
  const [input, setInput] = useState("请基于上方信息，严格输出仅包含 companies 与 services 的 JSON（不要 summary、不要多余说明）。")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [answer, setAnswer] = useState("")
  const [thinking, setThinking] = useState("")
  const [fullPrompt, setFullPrompt] = useState<string>("")
  const [parsed, setParsed] = useState<any | null>(null)
  const abortRef = useRef<AbortController | null>(null)
  let dotsTimer: NodeJS.Timeout | null = null
  const [analyzingIdx, setAnalyzingIdx] = useState(0)

  // SDK 分布（按 分类/子分类 计数），用于顶部常驻展示
  const sdkGroupsMini = useMemo(() => {
    const m = new Map<string, number>()
    for (const s of sdkList || []) {
      const cat = s.category_name || '未分类'
      const sub = s.subcategory_name || '未分子类'
      const key = `${cat} / ${sub}`
      m.set(key, (m.get(key) || 0) + 1)
    }
    return Array.from(m.entries()).sort((a,b)=>b[1]-a[1]).slice(0, 12)
  }, [sdkList])

  // SDK 分布（完整明细，用于可展开详情）
  const sdkGroupsFull = useMemo(() => {
    const m = new Map<string, { cat: string, sub: string, items: string[] }>()
    for (const s of sdkList || []) {
      const cat = s.category_name || '未分类'
      const sub = s.subcategory_name || '未分子类'
      const key = `${cat} / ${sub}`
      if (!m.has(key)) m.set(key, { cat, sub, items: [] })
      if (s.sdk_name) m.get(key)!.items.push(s.sdk_name)
    }
    return Array.from(m.entries()).sort((a,b)=> (b[1].items.length - a[1].items.length))
  }, [sdkList])

  // 构建完整提示词（仅用于展示，不参与请求体）：包含 System + Prompt，且不截断
  function buildFullPrompt(defaultAsk: string) {
    const appName = app?.app_name || "该应用"
    const description = app?.description || ""
    const downloads = app?.download_count
    const rating = app?.rating
    const all = (sdkList || [])
    const sdkText = all
      .map((s) => `- ${s.category_name || '未分类'} / ${s.subcategory_name || '未分子类'} / ${s.sdk_name}${s.match_type ? ` (${s.match_type})` : ''}`)
      .join('\n')
    const askText = defaultAsk || input
    const system = `你是严谨的移动应用分析助手。基于给定的应用信息与其集成的 SDK 列表，请只输出 JSON（不要任何额外说明或 markdown），字段如下：\n{\n  "companies": [ {\n    "name": string,\n    "colorHint?": string,\n    "services": [ { "name": string, "reason": string, "sdks": string[], "confidence": number } ]\n  } ],\n  "services": {\n    "AI?":   [ { "name": string, "reason": string, "sdks": string[], "confidence": number } ],\n    "IaaS?": [ { "name": string, "reason": string, "sdks": string[], "confidence": number } ],\n    "PaaS?": [ { "name": string, "reason": string, "sdks": string[], "confidence": number } ],\n    "BaaS?": [ { "name": string, "reason": string, "sdks": string[], "confidence": number } ],\n    "SaaS?": [ { "name": string, "reason": string, "sdks": string[], "confidence": number } ]\n  }\n}\n要求：\n- companies 用于展示“发现的 SDK 背后的公司及其被该 App 使用的云服务”，每个服务必须给出具体 reason，并列出来自输入的证据 sdks；confidence 范围 0.60-0.95。\n- services 各类建议 2-6 条推断，name 使用标准化能力名；严格 JSON 输出。`
    return `【System】\n${system}\n\n【Prompt】\n应用：${appName}\n下载量：${downloads ?? '未知'}，评分：${rating ?? '未知'}\n简介：${description}\n\n已识别的 SDK 列表：\n${sdkText || '（暂无识别到 SDK）'}\n\n${askText}`
  }

  // 页面加载：仅构建并展示完整提示词，不自动提交
  useEffect(() => {
    const p = buildFullPrompt(input)
    setFullPrompt(p)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // 监听来自父容器的“开始分析”事件
  useEffect(() => {
    function handler() {
      if (!loading) onSubmit()
    }
    if (typeof window !== 'undefined') {
      window.addEventListener('aisummary:start', handler)
    }
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('aisummary:start', handler)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading])

  // 不再在生成后自动滚动

  async function onSubmit(e?: React.FormEvent) {
    if (e) e.preventDefault()
    if (!input?.trim() || loading) return
    setLoading(true)
    setError(null)
    setAnswer("")
    setThinking("思考中")
    let dots = 0
    if (dotsTimer) clearInterval(dotsTimer)
    dotsTimer = setInterval(() => {
      dots = (dots + 1) % 4
      setThinking("思考中" + ".".repeat(dots))
    }, 400)

    try {
      const controller = new AbortController()
      abortRef.current = controller
      const res = await fetch("/api/ai/summary", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ app, sdkList, ask: input }),
        signal: controller.signal,
      })
      if (!res.ok) throw new Error(`请求失败：${res.status}`)
      // 非流式：等待完整文本后一次性展示
      const text = await res.text()
      if (dotsTimer) clearInterval(dotsTimer)
      setThinking("")
      // 解析结构化 JSON（更健壮）：去除```包裹，提取首尾大括号
      try {
        let raw = text.trim()
        raw = raw.replace(/^```json\s*/i, '').replace(/^```\s*/i, '').replace(/```\s*$/i, '')
        const start = raw.indexOf('{')
        const end = raw.lastIndexOf('}')
        if (start !== -1 && end !== -1 && end > start) {
          raw = raw.slice(start, end + 1)
        }
        const obj = JSON.parse(raw)
        setParsed(obj)
      } catch {
        setParsed(null)
      }
      setAnswer(text)
    } catch (err: any) {
      if (err?.name !== "AbortError") setError(err?.message || "未知错误")
    } finally {
      if (dotsTimer) clearInterval(dotsTimer)
      setThinking("")
      setLoading(false)
      abortRef.current = null
    }
  }

  function onStop() {
    abortRef.current?.abort()
  }

  function onCopy() {
    if (!answer) return
    navigator.clipboard?.writeText(answer)
  }

  // 逐个 SDK 名称轮播（思考期）
  const sdkNamesTicker = useMemo(() => (sdkList || []).map(s => s.sdk_name).filter(Boolean).slice(0, 30), [sdkList])
  useEffect(() => {
    if (!loading || sdkNamesTicker.length === 0) return
    const t = setInterval(() => {
      setAnalyzingIdx((i) => (i + 1) % sdkNamesTicker.length)
    }, 600)
    return () => clearInterval(t)
  }, [loading, sdkNamesTicker])

  return (
    <div className={cn("relative flex h-full flex-col gap-3", className)}>
      {loading && (
        <div aria-hidden className="pointer-events-none absolute inset-0 rounded-lg ring-2 ring-emerald-400/50 shadow-[0_0_24px_8px_rgba(16,185,129,0.30)] animate-pulse" />
      )}
      {/* 展示完整 Prompt（仅 debug=true/ture 时显示） */}
      {showPrompt && (
        <details className="rounded border bg-white p-3" open>
          <summary className="cursor-pointer text-sm font-medium flex items-center justify-between">
            <span>查看本次提示词（Prompt）</span>
            <button
              type="button"
              className="ml-4 inline-flex items-center gap-1 rounded border px-2 py-1 text-[11px] text-neutral-700 hover:bg-neutral-50"
              onClick={() => { if (fullPrompt) navigator.clipboard?.writeText(fullPrompt) }}
            >
              复制全部
            </button>
          </summary>
          <pre className="mt-2 whitespace-pre-wrap text-xs leading-relaxed">{fullPrompt || '（准备中…）'}</pre>
        </details>
      )}
      {/* 操作区移交到标题右侧按钮，这里不再放按钮 */}

      <Conversation className="flex-1 min-h-0">
        <ConversationContent>
          {/* 将分析动画与结果移动到最上方 */}
          <Message from="assistant">
            {thinking && !answer ? (
              <div className="space-y-4">
                <div className="h-5 rounded bg-satin-100 animate-pulse" />
                <div className="h-5 rounded bg-satin-100 animate-pulse" />
                <div className="h-24 rounded bg-satin-100 animate-pulse" />
                <div className="flex items-center gap-2 text-xs text-neutral-500">
                  <Loader />
                  <span>智能思考中… 分析应用与 SDK，推断 IaaS/PaaS/BaaS/SaaS 需求与公司云服务归属</span>
                </div>
                {sdkNamesTicker.length > 0 && (
                  <div className="text-xs text-emerald-700">
                    当前分析：
                    <span className="ml-1 inline-block rounded bg-emerald-100 px-1.5 py-0.5">{sdkNamesTicker[analyzingIdx]}</span>
                  </div>
                )}
              </div>
            ) : parsed ? (
              <div className="space-y-4">
                {/* 公司与使用的云服务 */}
                {Array.isArray(parsed.companies) && parsed.companies.length > 0 && (
                  <div className="space-y-2">
                    <div className="text-sm font-semibold">公司与使用的云服务</div>
                    {parsed.companies.map((c: any, i: number) => (
                      <div key={i} className="rounded border bg-white ring-1 ring-black/5 p-2">
                        <div className={cn("inline-flex items-center gap-2 rounded px-2.5 py-1 text-xs ring-1 ring-black/5", badgeColor(c?.colorHint))}>
                          <span className="font-medium">{c?.name}</span>
                        </div>
                        {Array.isArray(c?.services) && c.services.length > 0 && (
                          <ul className="mt-2 space-y-1 text-xs text-neutral-700">
                            {c.services.map((s: any, j: number) => (
                              <li key={j} className="flex items-start gap-2">
                                <span className="mt-0.5 h-1.5 w-1.5 rounded-full bg-emerald-500" aria-hidden />
                                <div className="flex-1">
                                  <div className="flex items-center gap-2">
                                    <div className="font-medium text-neutral-800">{s?.name}</div>
                                    {typeof s?.confidence === 'number' && (
                                      <span className="rounded bg-emerald-50 text-emerald-700 px-1 py-0.5 text-[10px] ring-1 ring-emerald-200">{Math.round(Math.min(Math.max(s.confidence,0),1)*100)}%</span>
                                    )}
                                  </div>
                                  {s?.reason && <div className="text-neutral-600 mt-0.5">{s.reason}</div>}
                                  {Array.isArray(s?.sdks) && s.sdks.length > 0 && (
                                    <div className="mt-1 flex flex-wrap gap-1">
                                      {s.sdks.map((n: string, k: number) => (
                                        <span key={k} className="rounded bg-satin-100 text-satin-700 px-1 py-0.5 text-[10px] ring-1 ring-black/5">{n}</span>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>
                    ))}
                  </div>
                )}
                {/* 云服务需求按类 */}
                {(() => {
                  const order = ["AI", "IaaS", "PaaS", "BaaS", "SaaS"]
                  return (
                    <div className="space-y-3">
                      {order.map((k) => {
                        let arr = parsed?.services?.[k]
                        if (!Array.isArray(arr) || arr.length === 0) return null
                        // 按置信度降序
                        arr = [...arr].sort((a:any,b:any)=> (Number(b?.confidence||0) - Number(a?.confidence||0)))
                        return (
                          <div key={k} className="space-y-2">
                            <div className="text-sm font-semibold">{k} 需求推断</div>
                            <div className="grid grid-cols-1 gap-2">
                              {arr.map((it: any, idx: number) => (
                                <div key={idx} className="rounded border-l-4 border-satin-400/70 bg-white ring-1 ring-black/5 p-2">
                                  <div className="flex items-center gap-2">
                                    <div className="text-[13px] font-medium text-neutral-800">{it?.name}</div>
                                    {typeof it?.confidence === 'number' && (
                                      <span className="rounded bg-emerald-50 text-emerald-700 px-1 py-0.5 text-[10px] ring-1 ring-emerald-200">{Math.round(Math.min(Math.max(it.confidence,0),1)*100)}%</span>
                                    )}
                                  </div>
                                  {it?.reason && <div className="text-xs text-neutral-600 leading-relaxed mt-0.5">{it.reason}</div>}
                                  {Array.isArray(it?.sdks) && it.sdks.length > 0 && (
                                    <div className="mt-1 flex flex-wrap gap-1">
                                      {it.sdks.map((n: string, k: number) => (
                                        <span key={k} className="rounded bg-satin-100 text-satin-700 px-1 py-0.5 text-[10px] ring-1 ring-black/5">{n}</span>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  )
                })()}
                {/* 按新契约移除 summary 展示 */}
              </div>
            ) : answer ? (
              <Response>{answer}</Response>
            ) : null}
            {/* 已移除自动滚动占位 */}
          </Message>
          {/* 非对话模式：在总结内容下方展示 SDK 分布（可展开明细） */}
          {!!sdkGroupsFull.length && (
            <div className="rounded border bg-white/80 p-3 mb-2 mt-2">
              <div className="mb-2 flex items-center justify-between">
                <div className="text-sm font-medium text-neutral-800">SDK 分布</div>
                <button
                  type="button"
                  className="relative inline-flex items-center gap-2 rounded-md bg-emerald-600 px-3 py-1.5 text-white text-xs hover:bg-emerald-700 focus:outline-none disabled:opacity-60"
                  onClick={() => { if (!loading) onSubmit() }}
                  disabled={loading}
                  aria-label="人工智能云服务分析"
                  title="人工智能云服务分析"
                >
                  <span className="pointer-events-none absolute inset-0 rounded-md ring-2 ring-emerald-400/40 animate-pulse" aria-hidden />
                  {/* 云朵 + 闪电图标 */}
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="relative">
                    <path d="M7 16a4 4 0 1 1 .6-7.96A5 5 0 0 1 17 6a5 5 0 0 1 1 9.9" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                    <path d="M12 12l-2 4h3l-1 4 3-5h-3l1-3Z" fill="currentColor"/>
                  </svg>
                  <span>人工智能云服务分析</span>
                </button>
              </div>
              <div className="space-y-2">
                {sdkGroupsFull.map(([k, g], idx) => (
                  <div key={idx} className="rounded border border-satin-200 bg-white">
                    <div className="px-3 py-2 flex items-center justify-between">
                      <div className="text-sm truncate flex items-center gap-2">
                        <span>{categoryIcon(g.cat)}</span>
                        <span className="text-neutral-800">{g.cat}</span>
                        <span className="mx-1 text-neutral-400">/</span>
                        <span className="text-neutral-700">{g.sub}</span>
                      </div>
                      <span className="text-xs text-neutral-600">{g.items.length} 个 SDK</span>
                    </div>
                    {g.items.length > 0 && (
                      <ul className="px-3 pb-2 text-sm grid md:grid-cols-2 gap-x-6 gap-y-1">
                        {g.items.map((name, j) => (
                          <li key={j} className="truncate">
                            <Link
                              href={`/categories/${slugify(g.cat)}/${slugify(g.sub)}/apps?sdk=${encodeURIComponent(name)}`}
                              className="inline-block w-full text-left hover:underline hover:text-emerald-700 truncate"
                            >
                              {name}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </ConversationContent>
        <ConversationScrollButton />
      </Conversation>
      {/* 非对话模式：隐藏输入区，仅保留复制/停止逻辑在思考阶段使用 */}
      {error && <div className="text-sm text-red-600">{error}</div>}
    </div>
  )
}
