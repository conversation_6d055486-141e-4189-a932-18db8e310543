"use client"

import Link from "next/link"
import { useEffect, useMemo, useState } from "react"
import { usePathname } from "next/navigation"
import { supabase } from "@/utils/supabase"

// 全局面包屑：目前聚焦 categories 体系
// 路径示例：
// - /categories
// - /categories/[categorySlug]/[subcategorySlug]
// - /categories/[categorySlug]/[subcategorySlug]/apps
// 其余路由暂不显示面包屑
export default function Breadcrumbs() {
  const pathname = usePathname()
  const [names, setNames] = useState<{
    categoryName?: string | null
    subcategoryName?: string | null
    categoryId?: number | null
  }>({})

  const parts = useMemo(() => (pathname || "/").split("/").filter(Boolean), [pathname])
  const isCategories = parts[0] === "categories"
  const categorySlug = isCategories && parts.length >= 2 ? parts[1] : undefined
  const subcategorySlug = isCategories && parts.length >= 3 ? parts[2] : undefined

  useEffect(() => {
    let aborted = false
    async function load() {
      if (!isCategories) return
      if (categorySlug && subcategorySlug) {
        // 与页面 SubcategoryHeader.tsx 相同的数据源
        const { data } = await supabase
          .from("mv_subcategories_slug")
          .select("subcategory_id, subcategory_name, category_id, category_name")
          .eq("category_slug", categorySlug)
          .eq("subcategory_slug", subcategorySlug)
          .maybeSingle()
        if (!aborted) {
          setNames({
            categoryName: (data as any)?.category_name ?? null,
            subcategoryName: (data as any)?.subcategory_name ?? null,
            categoryId: (data as any)?.category_id ?? null,
          })
        }
      } else {
        setNames({})
      }
    }
    load()
    return () => { aborted = true }
  }, [isCategories, categorySlug, subcategorySlug])

  return (
    <nav className="text-sm text-satin-700 flex items-center">
      {/* Home 图标始终显示 */}
      <Link href="/" className="link inline-flex items-center gap-1" title="Home">
        <span aria-hidden>🏠</span>
        <span className="sr-only">Home</span>
      </Link>

      {/* 全局都展示 Categories 入口 */}
      <span className="mx-2 text-satin-600">/</span>
      <Link href="/categories" className="link">Categories</Link>

      {/* 仅在 categories 体系下追加更深层级 */}
      {isCategories && (
        <>
          {categorySlug && (
            <>
              <span className="mx-2 text-satin-600">/</span>
              {names.categoryName ? (
                <Link href={`/categories#cat-${names.categoryId ?? ""}`} className="link">{names.categoryName}</Link>
              ) : (
                <span className="opacity-80">{decodeURIComponent(categorySlug)}</span>
              )}
            </>
          )}
          {subcategorySlug && (
            <>
              <span className="mx-2 text-satin-600">/</span>
              <span className="text-slate-900 font-medium">{names.subcategoryName || decodeURIComponent(subcategorySlug)}</span>
            </>
          )}
        </>
      )}
    </nav>
  )
}
