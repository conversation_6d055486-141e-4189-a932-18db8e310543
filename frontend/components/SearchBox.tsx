'use client';

import { useEffect, useRef, useState } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { readFiltersFromUrl, writeFiltersToUrl } from '@/utils/routerFilters';

// 简易搜索组件：对接本地 Meilisearch `apps` 索引
// 注意：生产环境请使用「Search-only API Key」，不要在前端暴露 Master Key
const MEILI_HOST = process.env.NEXT_PUBLIC_MEILI_URL || 'http://localhost:7700';
const MEILI_API_KEY = process.env.NEXT_PUBLIC_MEILI_KEY || '';
const INDEX_APPS = 'apps';
const INDEX_SDKS = 'sdks';

interface Hit {
  id?: string | number;
  app_id?: string | number;
  app_id_raw?: string | number;
  name?: string;
  icon_url?: string;
  sdk_name?: string;
  category_slug?: string;
  subcategory_slug?: string;
  __source?: 'app' | 'sdk';
  [key: string]: any;
}

export default function SearchBox() {
  const [q, setQ] = useState('');
  const [hits, setHits] = useState<Hit[]>([]);
  const [open, setOpen] = useState(false);
  const timer = useRef<number | undefined>(undefined);
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [subcats, setSubcats] = useState<string[]>([]);
  const [subcatNames, setSubcatNames] = useState<string[]>([]);
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const bootedFromUrl = useRef(false);

  useEffect(() => {
    // 监听来自 FilterBar 的筛选变更
    function onFiltersChanged(e: any) {
      const arr = (e?.detail?.subcategory_slug || []) as string[]
      const namesArr = (e?.detail?.subcategory_name || []) as string[]
      setSubcats(Array.isArray(arr) ? arr : [])
      setSubcatNames(Array.isArray(namesArr) ? namesArr : [])
    }
    window.addEventListener('filters:changed', onFiltersChanged)
    return () => window.removeEventListener('filters:changed', onFiltersChanged)
  }, [])

  // 初始化与响应 URL 变化（过渡期：与 CustomEvent 并存）
  useEffect(() => {
    if (!searchParams) return;
    const { q: urlQ, subcats: urlSubcats } = readFiltersFromUrl(searchParams);
    // 首次从 URL 注入，或当 URL 与本地不一致时同步
    if (!bootedFromUrl.current) {
      bootedFromUrl.current = true;
      if (urlQ && urlQ !== q) setQ(urlQ);
      if (urlSubcats.length > 0 && urlSubcats.join(',') !== subcats.join(',')) setSubcats(urlSubcats);
      return;
    }
    // 浏览器前进/后退导致的 URL 变化
    if (urlQ !== q) setQ(urlQ);
    if (urlSubcats.join(',') !== subcats.join(',')) setSubcats(urlSubcats);
  }, [searchParams])

  useEffect(() => {
    const hasFilters = (subcats.length > 0) || (subcatNames.length > 0)
    if (!q && !hasFilters) {
      setHits([]);
      setOpen(false);
      return;
    }
    window.clearTimeout(timer.current);
    timer.current = window.setTimeout(async () => {
      try {
        const headers = {
          'Content-Type': 'application/json',
          ...(MEILI_API_KEY ? { Authorization: `Bearer ${MEILI_API_KEY}` } : {}),
        } as Record<string, string>;
        const appsBody: any = { q, limit: 6 };
        if (subcats.length > 0) {
          appsBody.filter = [`subcategory_slug IN [${subcats.map(s => `\"${s}\"`).join(',')}]`];
        } else if (subcatNames.length > 0) {
          appsBody.filter = [`subcategory_name IN [${subcatNames.map(s => `\"${s}\"`).join(',')}]`];
        }
        const sdksBody = { q, limit: 4 };
        const [appsRes, sdksRes] = await Promise.all([
          fetch(`${MEILI_HOST}/indexes/${INDEX_APPS}/search`, { method: 'POST', headers, body: JSON.stringify(appsBody) }),
          fetch(`${MEILI_HOST}/indexes/${INDEX_SDKS}/search`, { method: 'POST', headers, body: JSON.stringify(sdksBody) }),
        ]);
        const [appsData, sdksData] = await Promise.all([appsRes.json(), sdksRes.json()]);
        const appsHits: Hit[] = (appsData?.hits ?? []).map((h: any) => ({ ...h, __source: 'app' }));
        const sdkHits: Hit[] = (sdksData?.hits ?? []).map((h: any) => ({ ...h, __source: 'sdk' }));
        setHits([...appsHits, ...sdkHits]);
        setOpen(true);
      } catch (e) {
        console.error('meilisearch error', e);
      }
    }, 250);
    return () => window.clearTimeout(timer.current);
  }, [q, subcats, subcatNames]);

  // 将输入的 q 同步写回 URL（使用 replace，避免堆叠历史）
  useEffect(() => {
    if (!searchParams || !pathname) return;
    writeFiltersToUrl(router, pathname, searchParams, { q }, { replace: true });
    // 仅依赖 q 即可，其余 subcats 的写入由 FilterBar 负责
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [q])

  return (
    <div className="relative w-full">
      {/* 左侧放大镜图标 */}
      <span className="pointer-events-none absolute inset-y-0 left-3 flex items-center text-neutral-400 dark:text-neutral-400/80">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden>
          <circle cx="11" cy="11" r="7" stroke="currentColor" strokeWidth="1.5" />
          <path d="M20 20l-3.2-3.2" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
        </svg>
      </span>
      <input
        ref={inputRef}
        value={q}
        onChange={(e) => setQ(e.target.value)}
        onFocus={() => setOpen(true)}
        onBlur={() => setTimeout(() => setOpen(false), 150)}
        placeholder="Search apps..."
        className="w-full pl-9 pr-10 py-2.5 text-sm sm:text-base rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-neutral-darker text-text dark:text-text-dark focus:outline-none focus:ring-2 focus:ring-primary shadow-[0_1px_2px_rgba(0,0,0,0.04)]"
      />
      {/* 右侧可点的放大镜按钮（聚焦输入框） */}
      <button
        type="button"
        aria-label="Search"
        onMouseDown={(e) => e.preventDefault()}
        onClick={() => { inputRef.current?.focus(); setOpen(true); }}
        className="absolute inset-y-0 right-2 my-auto h-7 w-7 inline-flex items-center justify-center rounded-md text-neutral-500 hover:text-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden>
          <circle cx="11" cy="11" r="7" stroke="currentColor" strokeWidth="1.5" />
          <path d="M20 20l-3.2-3.2" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
        </svg>
      </button>
      {open && hits.length > 0 && (
        <div className="absolute z-[60] mt-1 w-full bg-surface-light dark:bg-surface-dark border border-gray-200 dark:border-gray-700 rounded-md shadow-lg max-h-80 overflow-auto">
          {hits.map((h, i) => (
            <div
              key={(h.id ?? i).toString()}
              className="px-3 py-2 text-sm hover:bg-neutral/50 dark:hover:bg-neutral-dark cursor-pointer"
              onClick={() => {
                if (h.__source === 'sdk') {
                  const cat = typeof h.category_slug === 'string' ? h.category_slug : undefined;
                  const sub = typeof h.subcategory_slug === 'string' ? h.subcategory_slug : undefined;
                  const sdkName = typeof h.sdk_name === 'string' ? h.sdk_name : undefined;
                  if (cat && sub && sdkName) {
                    const params = new URLSearchParams({ sdk: sdkName });
                    window.location.href = `/categories/${cat}/${sub}/apps?${params.toString()}`;
                    return;
                  }
                  // 回退：缺少 slug 时跳转到 sdk 详情
                  if (h.id != null) window.location.href = `/sdks/${h.id}`;
                  return;
                }
                const docId = (h.app_id_raw ?? h.app_id ?? h.id) as string | number | undefined;
                if (docId) window.location.href = `/apps/${docId}`;
              }}
            >
              <div className="flex items-center justify-between gap-3">
                <div className="flex items-center gap-2 min-w-0">
                  {h.icon_url ? (
                    // 图标显示（24x24）
                    <img
                      src={h.icon_url}
                      alt={typeof h.name === 'string' ? h.name : 'icon'}
                      className="h-6 w-6 rounded-sm object-cover flex-shrink-0 shadow-sm"
                      loading="lazy"
                    />
                  ) : (
                    <div className="h-6 w-6 rounded-sm bg-gray-200 dark:bg-gray-700 flex-shrink-0 shadow-sm" />
                  )}
                  <div className="truncate" title={typeof (h.name ?? h.sdk_name) === 'string' ? (h.name ?? h.sdk_name) : ''}>
                    {(h.name ?? h.sdk_name) ?? JSON.stringify(h)}
                  </div>
                </div>
                <span
                  className={
                    `ml-3 text-xs flex-shrink-0 px-1.5 py-0.5 rounded ` +
                    (h.__source === 'sdk'
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-300')
                  }
                >
                  {h.__source === 'sdk' ? 'sdk' : 'app'}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
