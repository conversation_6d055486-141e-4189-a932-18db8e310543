# syntax=docker/dockerfile:1.4

# --- deps stage: install dependencies ---
FROM node:20-alpine AS deps
WORKDIR /app
# Install libc6-compat for some native modules if needed
RUN apk add --no-cache libc6-compat

# Copy only package manifests first for better layer caching
COPY package.json package-lock.json* npm-shrinkwrap.json* ./

# Install deps (prefer npm ci when lockfile exists)
RUN if [ -f package-lock.json ] || [ -f npm-shrinkwrap.json ]; then \
      npm ci --prefer-offline --no-audit --no-fund; \
    else \
      npm install --no-audit --no-fund; \
    fi

# --- builder stage: build next in standalone mode ---
FROM node:20-alpine AS builder
WORKDIR /app
ENV NODE_ENV=production

# Copy node_modules from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy the rest of the source code
COPY . .

# Build (next.config.ts will set output: 'standalone')
RUN npm run build

# --- runner stage: minimal runtime image ---
FROM node:20-alpine AS runner
WORKDIR /app
ENV NODE_ENV=production \
    NEXT_TELEMETRY_DISABLED=1 \
    PORT=3000 \
    HOSTNAME=0.0.0.0

# Create non-root user (node) is already present in the base image

# Copy standalone server and static assets from builder
# .next/standalone contains server.js and the built app
COPY --from=builder /app/.next/standalone ./
# static and public assets
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/public ./public

# Expose default Next.js port
EXPOSE 3000

# Start the server
CMD ["node", "server.js"]
