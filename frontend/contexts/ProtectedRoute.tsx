'use client';

import { useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {usePathname } from 'next/navigation';
// import { useRouter, usePathname } from 'next/navigation';

// List of public routes that don't require authentication (exact matches)
const PUBLIC_ROUTES = [
  '/',  // landing page
  '/login', 
  '/signup', 
  '/verify-email', 
  '/reset-password', 
  '/update-password'
];

// Public route prefixes (any path starting with these is considered public)
const PUBLIC_PREFIXES = ['/categories', '/api', '/auth', '/apps'];

export default function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, isLoading } = useAuth();
  // const router = useRouter();
  const pathname = usePathname();
  // Avoid flashing the full-page skeleton after initial bootstrap
  const hasBootstrappedRef = useRef(false);

  useEffect(() => {
    if (!isLoading) {
      hasBootstrappedRef.current = true;
    }
  }, [isLoading]);

  const isPublicPath = (path: string) => {
    if (PUBLIC_ROUTES.includes(path)) return true;
    return PUBLIC_PREFIXES.some((p) => path === p || path.startsWith(p + '/'));
  };

  useEffect(() => {
    // Do nothing while loading; we'll render children on public paths below
    if (isLoading) return;
    if (!user && !isPublicPath(pathname)) {
      const redirectUrl = `/login?redirect=${encodeURIComponent(pathname)}`;
      window.location.assign(redirectUrl);
    }
  }, [user, isLoading, pathname]);

  // Always render children on public paths to avoid blank flashes during bootstrap
  if (isPublicPath(pathname)) {
    return <>{children}</>;
  }

  // For protected paths: avoid rendering until auth is determined
  if (isLoading && !hasBootstrappedRef.current) {
    return null;
  }

  if (user) return <>{children}</>;

  return null;
} 