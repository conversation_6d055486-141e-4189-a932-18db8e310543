# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem
.idea/
.vscode/
*.swp
*.swo

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env
.env*.local
.env.development
.env.test
.env.production
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Supabase
**/supabase/.temp
.supabase/

# Security and keys
*.key
*.pem
*.cert
*.crt
*.p12
*.pfx
*.keystore

# Logs
logs
*.log

# Cache directories
.cache/
.next/cache/

# Cursor
.cursor/mcp.json
