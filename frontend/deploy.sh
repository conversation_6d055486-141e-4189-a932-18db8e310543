#!/bin/bash

# Tenyy Frontend Production Deployment Script
set -e

echo "🚀 Starting Tenyy Frontend Production Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "app" ]; then
    echo -e "${RED}❌ Error: Please run this script from the frontend directory${NC}"
    exit 1
fi

echo -e "${BLUE}📦 Installing dependencies...${NC}"
npm install

echo -e "${BLUE}🔨 Building Next.js application...${NC}"
npm run build

echo -e "${BLUE}☁️ Deploying to Alibaba Cloud FC...${NC}"
s deploy

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo -e "${YELLOW}🌐 Your application is now available at:${NC}"
echo -e "  • https://test.tiktech.cn"
echo -e "  • http://tenyy-frontend.fcv3.1774752256557315.cn-hangzhou.fc.devsapp.net"

echo -e "${YELLOW}📋 Next steps:${NC}"
echo -e "  1. Test your application at the URLs above"
echo -e "  2. Monitor logs in SLS if needed"
echo -e "  3. Consider enabling Cloudflare cache rules for better performance"
