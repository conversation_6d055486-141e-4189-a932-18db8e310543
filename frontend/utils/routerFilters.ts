export function parseCSVParam(v: string | null | undefined): string[] {
  if (!v) return []
  return v
    .split(',')
    .map(s => s.trim())
    .filter(Boolean)
}

export function serializeCSV(arr: string[] | null | undefined): string {
  const uniq = Array.from(new Set((arr ?? []).map(s => String(s).trim()).filter(Boolean)))
  return uniq.join(',')
}

export function readFiltersFromUrl(searchParams: URLSearchParams): {
  q: string
  subcats: string[]
  sdk?: string
} {
  const q = searchParams.get('q') ?? ''
  const subcats = parseCSVParam(searchParams.get('subcat'))
  const sdk = searchParams.get('sdk') ?? undefined
  return { q, subcats, sdk }
}

export function writeFiltersToUrl(
  router: any,
  pathname: string,
  searchParams: URLSearchParams,
  next: { q?: string | null; subcats?: string[] | null; sdk?: string | null },
  options?: { replace?: boolean }
) {
  const params = new URLSearchParams(searchParams.toString())

  if (next.q !== undefined) {
    if (next.q && next.q.trim()) params.set('q', next.q)
    else params.delete('q')
  }
  if (next.subcats !== undefined) {
    const csv = serializeCSV(next.subcats ?? [])
    if (csv) params.set('subcat', csv)
    else params.delete('subcat')
  }
  if (next.sdk !== undefined) {
    if (next.sdk && String(next.sdk).trim()) params.set('sdk', String(next.sdk))
    else params.delete('sdk')
  }

  const url = `${pathname}${params.toString() ? `?${params.toString()}` : ''}`
  if (options?.replace) router.replace(url)
  else router.push(url)
}
