# 前端生产部署指南

## 环境要求

- Node.js 18+
- npm 或 yarn
- Serverless Framework CLI: `npm install -g @serverless-devs/s`
- 阿里云 CLI 配置（已完成）

## 部署方式

### 方式一：一键部署脚本（推荐）

```bash
cd frontend
./deploy.sh
```

### 方式二：手动部署

```bash
cd frontend
npm install
npm run build
s deploy
```

### 方式三：使用 npm 脚本

```bash
cd frontend
npm run deploy
```

## 发布流程

### 1. 开发阶段
- 本地开发：`npm run dev`
- 构建测试：`npm run build`

### 2. 生产发布
1. 切换到生产环境：
   ```bash
   cp .env.prod .env.local
   ```

2. 运行部署：
   ```bash
   npm run deploy
   ```

3. 验证部署：
   - 访问：https://test.tiktech.cn
   - 检查控制台日志（SLS）

### 3. 版本管理（推荐）

```bash
# 1. 发布版本
s version publish -d "新功能发布"

# 2. 创建别名
s alias create --alias prod --version v1

# 3. 开启预置并发（在控制台完成）
# 目标：选择别名 prod
# 预置并发：1
# 内存：512MB
```

## 故障排查

### 常见问题

1. **构建失败**
   - 检查依赖：`npm install`
   - 检查环境变量：确认 `.env.local` 配置正确

2. **部署失败**
   - 检查阿里云权限
   - 检查 `s.yaml` 配置
   - 查看详细错误：`s deploy --debug`

3. **访问异常**
   - 检查域名解析
   - 检查函数日志（SLS）
   - 检查预置并发配置

### 日志查看

```bash
# 查看函数日志
s logs -t 10

# 查看 SLS 日志
# 项目：serverless-cn-hangzhou-9e7c5122-b958-5593-8465-0ecb5abe06bf
# 日志库：default-logs
```

## 性能优化

1. **Cloudflare 缓存规则**（推荐）
   - 静态资源：`/_next/static/*` → Cache Everything, Edge TTL 7d
   - API 路由：`/api/*` → Bypass Cache

2. **预置并发**
   - 已配置：1 个实例
   - 如需更高并发，可在控制台增加到 2-3 个

3. **监控建议**
   - 首包时间：< 500ms
   - 错误率：< 1%
   - 响应时间：< 200ms
