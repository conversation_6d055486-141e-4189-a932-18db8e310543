// app/subcategories/[id]/page.tsx
// 兼容旧路由：永久重定向到新的层级 slug 路径 /categories/[categorySlug]/[subcategorySlug]
import { notFound, permanentRedirect } from 'next/navigation'
import { supabase } from '@/utils/supabase'

export const revalidate = 0

function toNumber(x: any): number {
  if (typeof x === 'number') return x
  const n = Number(x)
  return Number.isNaN(n) ? 0 : n
}

async function fetchSubcategory(id: number) {
  const { data, error } = await supabase
    .from('v_subcategories')
    .select('*')
    .eq('subcategory_id', id)
    .limit(1)
    .maybeSingle()

  if (error) throw error
  if (!data) return null
  return {
    subcategory_id: toNumber(data.subcategory_id),
    subcategory_name: data.subcategory_name as string,
    category_id: toNumber(data.category_id),
    category_name: data.category_name as string,
    sdk_count: toNumber(data.sdk_count),
  }
}

async function getSlugPairById(id: number) {
  const { data, error } = await supabase
    .from('v_subcategories_slug')
    .select('category_slug, subcategory_slug')
    .eq('subcategory_id', id)
    .maybeSingle()
  if (error) throw error
  return data
}

export default async function SubcategoryDetail({ params }: { params: { id: string } }) {
  const id = Number(params.id)
  if (!Number.isFinite(id)) return notFound()

  const slugs = await getSlugPairById(id)
  if (!slugs) return notFound()

  // 永久重定向到新的层级 slug 路由
  return permanentRedirect(`/categories/${slugs.category_slug}/${slugs.subcategory_slug}`)
}
