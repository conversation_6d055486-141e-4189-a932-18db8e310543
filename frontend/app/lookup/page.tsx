// app/lookup/page.tsx
import Link from 'next/link'

export const revalidate = 0

export default async function LookupPage({ searchParams }: { searchParams: Promise<{ url?: string }> }) {
  const { url = '' } = await searchParams

  return (
    <div className="px-6 py-8 max-w-5xl mx-auto space-y-6">
      <nav className="text-sm text-satin-700">
        <Link href="/" className="link">Home</Link>
        <span className="mx-2 text-satin-600">/</span>
        <span className="text-slate-900 font-medium">Lookup</span>
      </nav>

      <header className="card p-5">
        <h1 className="text-xl font-semibold">Lookup</h1>
        <p className="text-sm text-neutral-600 mt-1">输入的网址会以 url 查询参数的形式传递：/lookup?url=...</p>
      </header>

      <section className="card p-5 space-y-3">
        <div className="text-sm text-satin-700">当前查询：</div>
        <div className="text-base font-medium break-all">{url || '（未提供）'}</div>
        <div className="text-xs text-neutral-500">此页面为占位页面，可在此对目标站点执行识别或分析。</div>
      </section>
    </div>
  )
}
