"use client";

// import { useWebSocket } from '@/contexts/WebSocketContext';
import { useEffect, useState } from 'react';
import { supabase } from '@/utils/supabase';
import { useAuth } from '@/contexts/AuthContext';


import { useRouter } from 'next/navigation';
import { useSubscription } from '@/hooks/useSubscription';
// import { OnboardingTour } from '@/components/OnboardingTour';
import { useTrialStatus } from '@/hooks/useTrialStatus';
import { motion } from 'framer-motion';
import DashboardSidebar from '@/components/DashboardSidebar';
import { 
  BarChart3, 
  Users, 
  CreditCard, 
  Settings,
  PlusCircle,
  Clock,
  TrendingUp,
  Activity
} from 'lucide-react';

const AUTH_TIMEOUT = 15000; // 15 seconds

// Dashboard metrics data
const dashboardMetrics = [
  {
    title: "Total Users",
    value: "1,234",
    change: "+12.3%",
    icon: <Users className="h-6 w-6 text-primary" />,
    trend: "up"
  },
  {
    title: "Revenue",
    value: "$12.4k",
    change: "+8.2%",
    icon: <CreditCard className="h-6 w-6 text-primary" />,
    trend: "up"
  },
  {
    title: "Active Sessions",
    value: "432",
    change: "-3.1%",
    icon: <Activity className="h-6 w-6 text-primary" />,
    trend: "down"
  },
  {
    title: "Growth Rate",
    value: "18.2%",
    change: "+2.4%",
    icon: <TrendingUp className="h-6 w-6 text-primary" />,
    trend: "up"
  }
];

// Recent activity data
const recentActivity = [
  {
    id: 1,
    action: "New user signup",
    timestamp: "2 minutes ago",
    icon: <PlusCircle className="h-4 w-4" />
  },
  {
    id: 2,
    action: "Payment processed",
    timestamp: "15 minutes ago",
    icon: <CreditCard className="h-4 w-4" />
  },
  {
    id: 3,
    action: "Settings updated",
    timestamp: "1 hour ago",
    icon: <Settings className="h-4 w-4" />
  },
  {
    id: 4,
    action: "Session completed",
    timestamp: "2 hours ago",
    icon: <Clock className="h-4 w-4" />
  }
];

export default function Dashboard() {

  
  // const { isConnected } = useWebSocket();
  // const [fullResponse, setFullResponse] = useState('');
  const { user, isSubscriber, isLoading: isAuthLoading } = useAuth();
  const router = useRouter();
  const { subscription, isLoading: isSubLoading, fetchSubscription } = useSubscription();
  const [hasCheckedSubscription, setHasCheckedSubscription] = useState(false);
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false);
  const { isInTrial, isLoading: isTrialLoading } = useTrialStatus();
  const [authTimeout, setAuthTimeout] = useState(false);

  // Add new states for dashboard functionality
  // const [repositories, setRepositories] = useState([]);
  // const [feedbackSources, setFeedbackSources] = useState([]);
  // const [recentFeedback, setRecentFeedback] = useState([]);
  // const [pendingPRs, setPendingPRs] = useState([]);

  // First check - Subscription and trial check
  useEffect(() => {
    if (isSubLoading || isTrialLoading) return;
    
    const hasValidSubscription = ['active', 'trialing'].includes(subscription?.status || '');
    
    console.log('Access check isInTrial:', {
      hasSubscription: !!subscription,
      status: subscription?.status,
      isInTrial: isInTrial,
      validUntil: subscription?.current_period_end
    });

    // Only redirect if there's no valid subscription AND no valid trial
    if (!hasValidSubscription && !isInTrial) {
      console.log('No valid subscription or trial, redirecting');
      router.replace('/profile');
    }
  }, [subscription, isSubLoading, isTrialLoading, router, isInTrial]);

  // Second check - Auth check
  useEffect(() => {
    if (isAuthLoading || isTrialLoading) return;

    console.log('Access check:', {
      isSubscriber,
      hasCheckedSubscription,
      isInTrial: isInTrial,
      authLoading: isAuthLoading,
    });

    if (!hasCheckedSubscription) {
      setHasCheckedSubscription(true);
      
      // Allow access for both subscribers and trial users
      if (!user || (!isSubscriber && !isInTrial && !isAuthLoading)) {
        console.log('No valid subscription or trial, redirecting');
        router.replace('/profile');
      }
    }
  }, [isSubscriber, isAuthLoading, hasCheckedSubscription, router, user, subscription, isTrialLoading, isInTrial]);

  // Add refresh effect
  useEffect(() => {
    const refreshSubscription = async () => {
      await fetchSubscription();
      setHasCheckedSubscription(true);
    };
    
    if (user?.id) {
      refreshSubscription();
    }
  }, [user?.id, fetchSubscription]);

  useEffect(() => {
    if (user?.id) {
      // Check if user has completed onboarding
      const checkOnboarding = async () => {
        const { data } = await supabase
          .from('user_preferences')
          .select('has_completed_onboarding')
          .eq('user_id', user.id)
          .single();
        
        setHasCompletedOnboarding(!!data?.has_completed_onboarding);
        console.log('hasCompletedOnboarding: ', hasCompletedOnboarding)
      };
      
      checkOnboarding();
    }
  }, [user?.id, hasCompletedOnboarding]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (!user && (isAuthLoading || isTrialLoading)) {
        setAuthTimeout(true);
      }
    }, AUTH_TIMEOUT);
    
    return () => clearTimeout(timer);
  }, [user, isAuthLoading, isTrialLoading]);

  // useEffect(() => {
  //   if (!hasCompletedOnboarding) {
  //     router.push('/onboarding');
  //   }
  // }, [hasCompletedOnboarding, router]);

  // Update the loading check
  if (!user && (isAuthLoading || isTrialLoading) && !hasCheckedSubscription) {
    console.log('user: ', user)
    console.log('isAuthLoading: ', isAuthLoading)
    console.log('hasCheckedSubscription: ', hasCheckedSubscription)
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-foreground mb-4 mx-auto"></div>
          <p className="text-foreground">
            {authTimeout ? 
              "Taking longer than usual? Try refreshing the page 😊." :
              "Verifying access..."}
          </p>
        </div>
      </div>
    );
  }


  return (
    <div className="min-h-screen bg-slate-50 dark:bg-[#0B1120]">
      <div className="w-full px-4 sm:px-6 lg:px-8 py-6 flex gap-6">
        <DashboardSidebar />
        <div className="flex-1">
          <div className="max-w-7xl mx-auto">
          {/* Dashboard Header */}
          <div className="bg-white dark:bg-neutral-dark border border-slate-200 dark:border-slate-700 rounded-xl px-6 py-5 mb-6">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold text-slate-900 dark:text-white">Dashboard Overview</h1>
              <span className="text-sm text-slate-600 dark:text-slate-300">{isInTrial ? "Trial Period" : "Premium Plan"}</span>
            </div>
          </div>

          {/* Dashboard Content */}
          <div className="px-1 pb-2">
        {/* Start a new search */}
        <h2 className="text-sm font-medium text-slate-500 dark:text-slate-400 mb-3">Start a new search</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-10">
          {[
            { title: 'Search Jobs', desc: 'Across 50M+ jobs worldwide from popular sources', icon: <BarChart3 className="h-5 w-5 text-primary" /> },
            { title: 'Search Companies', desc: 'Filter by technologies, size, revenue, etc.', icon: <TrendingUp className="h-5 w-5 text-primary" /> },
            { title: 'Look up a company', desc: "Jump straight to a company's details", icon: <Users className="h-5 w-5 text-primary" /> },
            { title: 'Enrich company list', desc: 'Upload a list and enrich with metadata', icon: <Activity className="h-5 w-5 text-primary" /> },
          ].map((card, i) => (
            <motion.button
              key={card.title}
              initial={{ opacity: 0, y: 12 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: i * 0.05 }}
              className="text-left bg-white dark:bg-neutral-dark border border-slate-200 dark:border-slate-700 rounded-xl p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start gap-3">
                <div className="p-2 bg-primary/10 dark:bg-primary-light/10 rounded-lg">{card.icon}</div>
                <div>
                  <div className="font-semibold text-slate-900 dark:text-white">{card.title}</div>
                  <div className="mt-1 text-xs text-slate-500 dark:text-slate-400 leading-relaxed">{card.desc}</div>
                </div>
              </div>
            </motion.button>
          ))}
        </div>

        {/* Saved searches */}
        <h2 className="text-sm font-medium text-slate-500 dark:text-slate-400 mb-2">Your saved searches</h2>
        <div className="text-sm text-slate-400 dark:text-slate-500 mb-8">You haven't saved any searches yet</div>

        {/* Recent searches */}
        <h2 className="text-sm font-medium text-slate-500 dark:text-slate-400 mb-3">Your recent searches</h2>
        <div className="bg-white dark:bg-neutral-dark rounded-xl border border-slate-200 dark:border-slate-700 divide-y divide-slate-200/70 dark:divide-slate-700/70">
          {recentActivity.map((activity) => (
            <div key={activity.id} className="flex items-center gap-3 p-3 text-sm hover:bg-slate-50 dark:hover:bg-neutral/30 transition-colors">
              <div className="p-2 bg-primary/10 dark:bg-primary-light/10 rounded-lg">{activity.icon}</div>
              <div className="flex-1">
                <div className="text-slate-900 dark:text-white">{activity.action}</div>
                <div className="text-xs text-slate-500 dark:text-slate-400">{activity.timestamp}</div>
              </div>
            </div>
          ))}
          </div>
        </div>
        </div>
        </div>
      </div>
    </div>
  );
}
