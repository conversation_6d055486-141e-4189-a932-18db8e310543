@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* satin-linen palette from front_end.md */
  --color-satin-linen-50: #f7f6f3;
  --color-satin-linen-100: #e2ded0;
  --color-satin-linen-200: #d2cbb5;
  --color-satin-linen-300: #baaf8f;
  --color-satin-linen-400: #aa9a75;
  --color-satin-linen-500: #9d8663;
  --color-satin-linen-600: #897056;
  --color-satin-linen-700: #735b4a;
  --color-satin-linen-800: #604b40;
  --color-satin-linen-900: #503f37;
  --color-satin-linen-950: #2c221c;
}

body {
  font-family: 'Inter', sans-serif;
}

/* Base: 全站默认排版与元素风格 */
@layer base {
  html, body {
    @apply bg-satin-50 text-slate-900 dark:text-slate-100 antialiased;
  }
  h1 { @apply text-2xl font-semibold; }
  h2 { @apply text-xl font-semibold; }
  h3 { @apply text-lg font-medium; }
  a { @apply text-satin-700 hover:text-satin-800 underline-offset-2 hover:underline; }
  hr { @apply border-satin-200; }
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

/* Components: 统一的卡片、按钮、弱提示、链接样式 */
@layer components {
  .card {
    @apply rounded-xl border border-satin-200 bg-white shadow-sm dark:bg-neutral-900/40;
  }
  .btn {
    @apply inline-flex items-center justify-center px-3 py-1.5 rounded-md bg-satin-700 text-white hover:bg-satin-800 transition-colors;
  }
  .btn-secondary {
    @apply inline-flex items-center justify-center px-3 py-1.5 rounded-md bg-white text-satin-800 border border-satin-300 hover:bg-satin-50 transition-colors;
  }
  .muted { @apply text-slate-500; }
  .link { @apply text-satin-700 hover:text-satin-800 hover:underline; }

  .recipe-card {
    @apply bg-white dark:bg-neutral-dark rounded-lg shadow-sm border border-primary/10 dark:border-primary/20 transition-all duration-300 hover:shadow-md;
  }
  
  .cooking-button {
    @apply bg-primary hover:bg-primary-dark text-white rounded-lg transition-all duration-300 transform hover:scale-102 active:scale-98;
  }
  
  .accent-button {
    @apply bg-secondary hover:bg-secondary-dark text-white rounded-lg transition-all duration-300;
  }
}

/* Hide scrollbar styles */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Shimmer Skeleton theme vars */
:root {
  --skeleton-base: rgba(0,0,0,0.06);
  --skeleton-highlight: rgba(0,0,0,0.12);
}

@media (prefers-color-scheme: dark) {
  :root {
    --skeleton-base: rgba(255,255,255,0.06);
    --skeleton-highlight: rgba(255,255,255,0.12);
  }
}

@keyframes skeleton-shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

@layer components {
  .skeleton {
    @apply rounded;
    position: relative;
    overflow: hidden;
    background: linear-gradient(
      90deg,
      var(--skeleton-base) 0%,
      var(--skeleton-highlight) 20%,
      var(--skeleton-base) 40%
    );
    background-size: 200% 100%;
    animation: skeleton-shimmer 1.2s ease-in-out infinite;
  }
  .skeleton-rounded { @apply rounded-lg; }
  .skeleton-pill { @apply rounded-full; }

  /* Small inline spinner for localized loading states */
  .spinner-sm {
    width: 16px;
    height: 16px;
    border-radius: 9999px;
    border: 2px solid rgba(0,0,0,0.15);
    border-top-color: rgba(0,0,0,0.45);
    animation: spin 0.7s linear infinite;
  }
  @media (prefers-color-scheme: dark) {
    .spinner-sm {
      border: 2px solid rgba(255,255,255,0.15);
      border-top-color: rgba(255,255,255,0.45);
    }
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}