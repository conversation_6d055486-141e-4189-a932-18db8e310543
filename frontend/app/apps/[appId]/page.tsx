// app/apps/[appId]/page.tsx
import Link from 'next/link'
import { supabase } from '@/utils/supabase'
import AISummaryPanel from '@/components/AISummaryPanel'

export const revalidate = 0
export const dynamic = 'force-dynamic'

function getStoreLink(appId: string) {
  return `https://play.google.com/store/apps/details?id=${encodeURIComponent(appId)}`
}

function storeTypeToLabel(t?: string | null): string | undefined {
  if (!t) return undefined
  const m: Record<string, string> = {
    huawei: '华为应用市场',
    yinyongbao: '应用宝',
    google: 'Google Play',
    apple: 'App Store',
    xiaomi: '小米应用商店',
    oppo: 'OPPO 应用商店',
    vivo: 'vivo 应用商店',
    samsung: 'Galaxy Store',
    baidu: '百度手机助手',
  }
  return m[t] || t
}

async function getAppVersionSnapShots(appId: string): Promise<SnapshotItem[]> {
  // 本地 Supabase：public.mv_apps_with_detail.snap_shots 为 text（JSON 字符串）
  try {
    const { data, error } = await supabase
      .from('api_apps_with_detail')
      .select('snap_shots')
      .eq('app_id', appId)
      .limit(1)
      .maybeSingle()
    if (error) return []
    const rawText: string | null = (data as any)?.snap_shots ?? null
    if (!rawText) return []
    let parsed: any
    try {
      parsed = JSON.parse(rawText)
    } catch {
      // 兼容形如 {url1,url2,...} 的文本
      const trimmed = rawText.trim()
      if (trimmed.startsWith('{') && trimmed.endsWith('}')) {
        const body = trimmed.slice(1, -1)
        const parts = body.split(',').map(s => s.trim()).filter(Boolean)
        return parts.map(u => ({ image_url: u }))
      }
      // 兼容纯逗号分隔的 URL 列表（无花括号/引号）
      // 例如："http://a/1.png,http://a/2.png,..."
      // 也兼容被[]/()包裹但非 JSON 的情况
      const stripped = trimmed.replace(/^[\[\(]/, '').replace(/[\]\)]$/, '')
      if (stripped.includes('http')) {
        const parts = stripped.split(',').map(s => s.trim()).filter(Boolean)
        if (parts.length > 0) {
          return parts.map(u => ({ image_url: u.replace(/^\"|\"$/g, '') }))
        }
      }
      return []
    }
    if (Array.isArray(parsed)) {
      if (parsed.length === 0) return []
      if (typeof parsed[0] === 'string') {
        return (parsed as string[]).map(u => ({ image_url: String(u) }))
      }
      return (parsed as any[]).map((x) => ({ image_url: String(x.url || x.image_url), title: x.title ?? null }))
    }
    return []
  } catch {
    return []
  }
}

type SdkItem = { category_name: string | null, subcategory_name: string | null, sdk_name: string, match_type?: string | null }
type SnapshotItem = { image_url: string, title?: string | null, taken_at?: string | null }
type SnapShotsField = string[] | { url: string, title?: string | null }[] | null
async function getSdkList(appId: string): Promise<SdkItem[]> {
  // 期望后端提供 public.mv_app_sdks_detail 视图：
  // 列: app_id, category_name, subcategory_name, sdk_name, match_type
  try {
    const { data, error } = await supabase
      .from('api_app_sdks_detail')
      .select('category_name, subcategory_name, sdk_name, match_type')
      .eq('app_id', appId)
      .order('category_name', { ascending: true })
      .order('subcategory_name', { ascending: true })
      .order('sdk_name', { ascending: true })
    if (error) return []
    return (data ?? []) as SdkItem[]
  } catch { return [] }
}

async function getSdkGroups(appId: string) {
  // 期望后端提供 public.mv_app_sdk_count_by_category 视图：
  // 列: app_id, category_name, subcategory_name, sdk_count
  try {
    const { data, error } = await supabase
      .from('api_app_sdk_count_by_category')
      .select('category_name, subcategory_name, sdk_count')
      .eq('app_id', appId)
    if (error) return [] as Array<{category_name: string, subcategory_name: string, sdk_count: number}>
    return (data ?? []) as Array<{category_name: string, subcategory_name: string, sdk_count: number}>
  } catch {
    return []
  }
}

async function getAppById(appId: string) {
  // 详情直接读取宽表 MV：public.mv_apps_with_detail
  const { data, error } = await supabase
    .from('api_apps_with_detail')
    .select('app_id, app_name, icon_url, description, version, store_name, store_type, release_date, rating, download_count')
    .eq('app_id', appId)
    .maybeSingle()
  if (error) throw error
  return data as {
    app_id: string,
    app_name: string,
    icon_url?: string | null,
    description?: string | null,
    version?: string | null,
    store_name?: string | null,
    store_type?: string | null,
    release_date?: string | null,
    rating?: any,
    download_count?: number | null,
  } | undefined
}

export default async function AppDetailPage({ params }: { params: Promise<{ appId: string }> }) {
  const { appId } = await params
  const [app, sdkGroups, sdkList, snapshots] = await Promise.all([
    getAppById(appId),
    getSdkGroups(appId),
    getSdkList(appId),
    getAppVersionSnapShots(appId),
  ])
  if (!app) {
    return <div className="px-6 py-8 max-w-4xl mx-auto">未找到该应用（App ID: {appId}）</div>
  }
  const icon = app.icon_url || undefined
  // 将 SDK 明细按（分类/子分类）分组，便于手风琴展开
  const groupedSdk: Record<string, { cat: string, sub: string, items: SdkItem[]; count: number }> = {}
  for (const g of sdkGroups) {
    const cat = g.category_name
    const sub = g.subcategory_name
    const key = `${cat}///${sub}`
    groupedSdk[key] = { cat, sub, items: [], count: g.sdk_count }
  }
  for (const s of sdkList) {
    const cat = s.category_name || '未分类'
    const sub = s.subcategory_name || '未分子类'
    const key = `${cat}///${sub}`
    if (!groupedSdk[key]) groupedSdk[key] = { cat, sub, items: [], count: 0 }
    groupedSdk[key].items.push(s)
  }
  return (
    <div className="px-6 pt-4 pb-8 max-w-6xl mx-auto space-y-0 min-h-screen">
      <nav className="text-sm text-satin-700 mb-2">
        <Link href="/categories" className="link">分类</Link>
        <span className="mx-1">/</span>
        <span className="opacity-80">应用详情</span>
      </nav>
      {/* 主体两列：左侧应用详情(50%) + 右侧 AI 总结(50%) */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-start">
        {/* 左列：应用详情整体容器 */}
        <div className="space-y-4">
          <div className="card p-6 space-y-4">
            <div className="flex items-center gap-4">
            {icon ? (
              <img src={icon} alt="icon" className="w-12 h-12 rounded-md object-cover bg-satin-100" />
            ) : (
              <div className="w-12 h-12 rounded-md bg-satin-200 flex items-center justify-center text-satin-700 text-base font-medium">
                {(app.app_name?.[0] || '?').toUpperCase()}
              </div>
            )}
            <div>
              <h1 className="text-xl font-semibold">{app.app_name}</h1>
              <div className="text-xs text-neutral-600">App ID: {app.app_id}</div>
              <div className="text-xs text-neutral-600 mt-1 flex gap-3">
                {app.version && <span>版本：{app.version}</span>}
                {app.store_type && <span>商店：{storeTypeToLabel(app.store_type)}</span>}
                {app.release_date && <span>发布时间：{new Date(app.release_date).toLocaleDateString()}</span>}
              </div>
            </div>
            </div>
            {/* 详情内：下载/评分 */}
            <div className="text-sm text-neutral-700 flex gap-4">
              {app.download_count != null && <span>下载量：{Number(app.download_count).toLocaleString()}</span>}
              {app.rating != null && <span>评分：{String(app.rating)}</span>}
            </div>
            {/* 应用快照（来自 app_version.snap_shots），放在 description 上方 */}
            {!!snapshots?.length && (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {snapshots.map((s, i) => (
                  <a key={i} href={s.image_url} target="_blank" rel="noopener noreferrer" className="group block overflow-hidden rounded border">
                    <img src={s.image_url} alt={s.title || 'snapshot'} className="aspect-[4/3] w-full object-cover group-hover:opacity-90" />
                  </a>
                ))}
              </div>
            )}
            {/* 应用简介 */}
            {app.description && (
              <div className="prose prose-sm max-w-none text-neutral-800 whitespace-pre-wrap">{app.description}</div>
            )}
          </div>
          {/* SDK 分布移至右侧 AI 面板展示 */}
          <div>
            <a href={getStoreLink(app.app_id)} target="_blank" rel="noopener noreferrer" className="btn btn-sm">前往商店</a>
          </div>
        </div>
        {/* 右列：AI 总结（随内容自适应高度，页面整体滚动） */}
        <div className="card p-4 flex flex-col">
          <AISummaryPanel app={app} sdkList={sdkList} className="" />
        </div>
      </div>
    </div>
  )
}
