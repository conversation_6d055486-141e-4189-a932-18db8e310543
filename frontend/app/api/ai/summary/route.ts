// app/api/ai/summary/route.ts
import { streamText } from 'ai'
import { createOpenAICompatible } from '@ai-sdk/openai-compatible'

export const maxDuration = 30

const deepseek = createOpenAICompatible({
  apiKey: process.env.DEEPSEEK_API_KEY,
  baseURL: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1',
  name: 'deepseek',
})

export async function POST(req: Request) {
  const { app, sdkList, ask }: { app: any; sdkList: Array<any>; ask?: string } = await req.json()

  const appName = app?.app_name ?? '该应用'
  const description = app?.description ?? ''
  const downloads = app?.download_count
  const rating = app?.rating

  // 限制 SDK 列表长度，避免提示过长影响质量与成本
  const limitedSdks = (sdkList || []).slice(0, 120)
  const sdkText = limitedSdks
    .map((s: any) => `- ${s.category_name || '未分类'} / ${s.subcategory_name || '未分子类'} / ${s.sdk_name}${s.match_type ? ` (${s.match_type})` : ''}`)
    .join('\n')

  const system = `你是严谨的移动应用分析助手。基于给定的应用信息与其集成的 SDK 列表，请只输出 JSON（不要任何额外说明或 markdown），字段如下：
{
  "companies": [ {
    "name": string,
    "colorHint?": string,
    "services": [ { "name": string, "reason": string, "sdks": string[], "confidence": number } ]
  } ],
  "services": {
    "AI?":   [ { "name": string, "reason": string, "sdks": string[], "confidence": number } ],
    "IaaS?": [ { "name": string, "reason": string, "sdks": string[], "confidence": number } ],
    "PaaS?": [ { "name": string, "reason": string, "sdks": string[], "confidence": number } ],
    "BaaS?": [ { "name": string, "reason": string, "sdks": string[], "confidence": number } ],
    "SaaS?": [ { "name": string, "reason": string, "sdks": string[], "confidence": number } ]
  }
}
要求：
- companies 用于展示“发现的 SDK 背后的公司及其被该 App 使用的云服务”，每个服务必须给出简短且具体的 reason，并列出来自输入的证据 sdks（不可虚构、不可越权推断未出现的 SDK）。
- services 各类每类建议给出 2-6 条推断项；name 使用标准化能力名（如 对象存储、消息队列、日志与指标监控、链路追踪、CDN、图像处理、音视频点播/直播、IM、推送、鉴权/认证、支付、地图定位、A/B 测试、推荐、搜索、风控/反作弊、数据分析/埋点、广告归因 等）。
- confidence 范围 0.60-0.95，基于证据强度评估；reason 应尽量引用 SDK 线索（例如："因检测到 Firebase Analytics 与 FCM，推断使用了 数据分析/推送 能力"）。
- 严格输出合法 JSON；不要包含 markdown、不要输出除上述字段外的内容；不要输出总结性段落。`

  const prompt = `应用：${appName}\n下载量：${downloads ?? '未知'}，评分：${rating ?? '未知'}\n简介：${description}\n\n已识别的 SDK 列表：\n${sdkText || '（暂无识别到 SDK）'}\n\n请根据以上信息，严格返回符合 system 描述的 JSON。${ask ? `\n用户追问：${ask}` : ''}`

  const result = streamText({
    model: deepseek.chatModel('deepseek-chat'),
    system,
    prompt,
    temperature: 0.2,
  })

  return result.toTextStreamResponse()
}
