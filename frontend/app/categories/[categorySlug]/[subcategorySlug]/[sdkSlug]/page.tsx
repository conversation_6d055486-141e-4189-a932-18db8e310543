// app/categories/[categorySlug]/[subcategorySlug]/[sdkSlug]/page.tsx
// 框架先渲染 + Suspense 局部加载详情，减少抖动
import Link from 'next/link'
import { Suspense } from 'react'
import { supabase } from '@/utils/supabase'
import SdkDetailContent from './SdkDetailContent'

export const revalidate = 0

async function getSubcategoryName(categorySlug: string, subcategorySlug: string) {
  const { data, error } = await supabase
    .from('api_subcategories_slug')
    .select('subcategory_name')
    .eq('category_slug', categorySlug)
    .eq('subcategory_slug', subcategorySlug)
    .maybeSingle()
  if (error) throw error
  return data?.subcategory_name ?? ''
}

function slugify(input: string): string {
  if (!input) return ''
  return input.replace(/[^A-Za-z0-9]+/g, '-').replace(/^-+|-+$/g, '').toLowerCase()
}

async function getSdkNameBySlug(categorySlug: string, subcategorySlug: string, sdkSlug: string) {
  const { data: sub, error: subErr } = await supabase
    .from('api_subcategories_slug')
    .select('subcategory_id')
    .eq('category_slug', categorySlug)
    .eq('subcategory_slug', subcategorySlug)
    .maybeSingle()
  if (subErr) throw subErr
  if (!sub) return ''
  const { data: list, error: listErr } = await supabase
    .from('api_sdks')
    .select('sdk_name, subcategory_id')
    .eq('subcategory_id', sub.subcategory_id)
    .order('sdk_name')
  if (listErr) throw listErr
  const hit = (list ?? []).find((s: any) => slugify(s.sdk_name) === sdkSlug)
  return hit?.sdk_name ?? ''
}

function DetailFallback() {
  return (
    <>
      <header className="card p-5">
        <div className="space-y-2">
          <div className="h-7 w-64 skeleton skeleton-rounded" />
          <div className="h-4 w-48 skeleton skeleton-rounded" />
        </div>
      </header>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <section className="lg:col-span-2 space-y-6">
          <div className="card p-5 space-y-2">
            <div className="h-5 w-16 skeleton skeleton-rounded" />
            <div className="h-4 w-5/6 skeleton skeleton-rounded" />
            <div className="h-4 w-2/3 skeleton skeleton-rounded" />
          </div>
          <div className="card p-5 space-y-2">
            <div className="h-5 w-20 skeleton skeleton-rounded" />
            <div className="h-4 w-40 skeleton skeleton-rounded" />
          </div>
        </section>
        <aside className="space-y-4">
          <div className="card p-4 space-y-2">
            <div className="h-4 w-24 skeleton skeleton-rounded" />
            <div className="h-3 w-32 skeleton skeleton-rounded" />
            <div className="h-3 w-28 skeleton skeleton-rounded" />
          </div>
        </aside>
      </div>
    </>
  )
}

export default async function SdkDetailPage({ params }: { params: Promise<{ categorySlug: string, subcategorySlug: string, sdkSlug: string }> }) {
  const { categorySlug, subcategorySlug, sdkSlug } = await params
  const [subName, sdkName] = await Promise.all([
    getSubcategoryName(categorySlug, subcategorySlug),
    getSdkNameBySlug(categorySlug, subcategorySlug, sdkSlug)
  ])

  return (
    <div className="px-6 py-8 max-w-5xl mx-auto space-y-6">
      {/* 面包屑：直接渲染 SDK 名，避免始终骨架 */}
      <nav className="text-sm text-satin-700">
        <Link href="/categories" className="link">Categories</Link>
        <span className="mx-2 text-satin-600">/</span>
        <Link href={`/categories/${categorySlug}/${subcategorySlug}`} className="link">{subName}</Link>
        <span className="mx-2 text-satin-600">/</span>
        <span className="text-slate-900 font-medium">{sdkName || 'SDK'}</span>
      </nav>

      {/* 详情主体：Suspense 渐进加载 */}
      <Suspense fallback={<DetailFallback />}> 
        <SdkDetailContent categorySlug={categorySlug} subcategorySlug={subcategorySlug} sdkSlug={sdkSlug} />
      </Suspense>
    </div>
  )
}
