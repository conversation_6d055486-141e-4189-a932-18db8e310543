// app/categories/[categorySlug]/[subcategorySlug]/[sdkSlug]/SdkDetailContent.tsx
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { supabase } from '@/utils/supabase'

function slugify(input: string): string {
  if (!input) return ''
  return input.replace(/[^A-Za-z0-9]+/g, '-').replace(/^-+|-+$/g, '').toLowerCase()
}

async function getSubcategoryBySlugs(categorySlug: string, subcategorySlug: string) {
  const { data, error } = await supabase
    .from('api_subcategories_slug')
    .select('subcategory_id, subcategory_name')
    .eq('category_slug', categorySlug)
    .eq('subcategory_slug', subcategorySlug)
    .maybeSingle()
  if (error) throw error
  return data
}

async function getSdkListBySubcategoryId(subcategoryId: number) {
  const { data, error } = await supabase
    .from('api_sdks_lite')
    .select('id, sdk_name, package_prefix, description, official_web, company_name, subcategory_id')
    .eq('subcategory_id', subcategoryId)
    .order('sdk_name')
  if (error) throw error
  return data ?? []
}

export default async function SdkDetailContent({ categorySlug, subcategorySlug, sdkSlug }: { categorySlug: string, subcategorySlug: string, sdkSlug: string }) {
  const sub = await getSubcategoryBySlugs(categorySlug, subcategorySlug)
  if (!sub) return notFound()
  const sdkList = await getSdkListBySubcategoryId(sub.subcategory_id)
  const sdk = sdkList.find((s: any) => slugify(s.sdk_name) === sdkSlug)
  if (!sdk) return notFound()
  const initial = (sdk.sdk_name || '?').charAt(0).toUpperCase()
  const company = (sdk.company_name as string | null) ?? '未知'

  return (
    <>
      <header className="card p-5">
        <div className="flex items-start justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-slate-900">{sdk.sdk_name}</h1>
            <p className="text-sm text-satin-700 mt-1">包前缀：{sdk.package_prefix}</p>
          </div>
        </div>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <section className="lg:col-span-2 space-y-6">
          <div className="card p-5 space-y-2">
            <h2 className="text-lg font-semibold">描述</h2>
            <p className="text-sm text-neutral-700 whitespace-pre-line">{sdk.description ?? '暂无描述'}</p>
          </div>

          {/* 外部链接已按需求移除 */}
        </section>
        <aside className="space-y-4">
          <div className="card p-4 space-y-2">
            <h3 className="text-sm font-semibold">基础信息</h3>
            <div className="text-sm text-satin-700">所属公司：{company}</div>
            <div className="text-sm text-satin-700">包前缀：{sdk.package_prefix}</div>
            {/* 官网链接按需求移除 */}
          </div>
          <div className="card p-4">
            <h3 className="text-sm font-semibold mb-2">导航</h3>
            <ul className="text-sm list-disc list-inside text-satin-700">
              <li><Link href={`/categories/${categorySlug}/${subcategorySlug}`} className="link">返回子分类</Link></li>
              <li><Link href="/categories" className="link">返回分类列表</Link></li>
            </ul>
          </div>
        </aside>
      </div>
    </>
  )
}
