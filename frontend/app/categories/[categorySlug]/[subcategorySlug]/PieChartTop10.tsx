'use client'

import React, { useEffect, useMemo, useRef, useState } from 'react'

export type MarketItem = { name: string; count: number; percent: number }

function toRGB(c: string): [number, number, number] | null {
  const ctx = c.trim()
  const m = ctx.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/i)
  if (m) return [parseInt(m[1]), parseInt(m[2]), parseInt(m[3])]
  // hex #rrggbb
  const h = ctx.match(/^#?([0-9a-f]{6})$/i)
  if (h) {
    const n = parseInt(h[1], 16)
    return [(n >> 16) & 255, (n >> 8) & 255, n & 255]
  }
  return null
}
function rgbToHsl(r: number, g: number, b: number): [number, number, number] {
  r /= 255; g /= 255; b /= 255
  const max = Math.max(r, g, b), min = Math.min(r, g, b)
  let h = 0, s = 0, l = (max + min) / 2
  if (max !== min) {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break
      case g: h = (b - r) / d + 2; break
      case b: h = (r - g) / d + 4; break
    }
    h /= 6
  }
  return [Math.round(h * 360), Math.round(s * 100), Math.round(l * 100)]
}

export default function PieChartTop10({ items }: { items: MarketItem[] }) {
  const top10 = items.slice(0, 10)
  const probeRef = useRef<HTMLDivElement>(null)
  const [palette, setPalette] = useState<string[]>([])

  // 依据主题的 bg-accent 生成 10 色多色调（绕色相环旋转，保持饱和度与明度协调）
  useEffect(() => {
    const el = probeRef.current
    const base = el ? getComputedStyle(el).backgroundColor : undefined
    const baseRGB = base ? toRGB(base) : toRGB('#7c3aed')
    const [h, s, l] = rgbToHsl(baseRGB![0], baseRGB![1], baseRGB![2])
    const colors: string[] = []
    // 以基准色为起点，均匀分布 10 个色相，轻微调整饱和度/明度
    for (let i = 0; i < 10; i++) {
      const hue = (h + i * 27) % 360 // 27° * 10 ≈ 270°，避免首尾过近
      const sat = Math.min(85, Math.max(45, s + (i % 2 === 0 ? -5 : 5)))
      const lig = Math.min(70, Math.max(40, l + (i - 5) * 2))
      colors.push(`hsl(${hue} ${sat}% ${lig}%)`)
    }
    setPalette(colors)
  }, [])
  const total = top10.reduce((a, b) => a + b.count, 0)

  const gradient = useMemo(() => {
    if (!total || top10.length === 0) return 'conic-gradient(#e5e7eb 0 360deg)'
    let acc = 0
    const parts: string[] = []
    for (let i = 0; i < top10.length; i++) {
      const p = (top10[i].count / total) * 360
      const start = acc
      const end = acc + p
      const color = palette[i % Math.max(1, palette.length)] || '#7c3aed'
      parts.push(`${color} ${start}deg ${end}deg`)
      acc = end
    }
    // 填满剩余区域为灰色，避免精度缺口
    parts.push(`#e5e7eb ${acc}deg 360deg`)
    return `conic-gradient(${parts.join(', ')})`
  }, [top10, total, palette])

  return (
    <div className="w-full grid grid-cols-12 gap-4 items-start">
      {/* 用于提取主题的 bg-accent 实际颜色（隐藏） */}
      <div ref={probeRef} className="hidden bg-accent" />
      <div className="col-span-5 flex items-center justify-center">
        <div className="rounded-full" style={{ width: 180, height: 180, backgroundImage: gradient }} />
      </div>
      <div className="col-span-7">
        <div className="text-sm font-medium mb-2">Top 10</div>
        <div className="space-y-2">
          {top10.map((it, i) => (
            <div key={i} className="flex items-center gap-2 text-xs">
              <span className="inline-block w-3 h-3 rounded-sm" style={{ backgroundColor: (palette[i % Math.max(1, palette.length)] || '#7c3aed') }} />
              <span className="truncate">{i + 1}. {it.name}</span>
              <span className="text-neutral-600">{it.percent}%</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
