// app/categories/[categorySlug]/[subcategorySlug]/SdkList.tsx
import Link from 'next/link'
import { supabase } from '@/utils/supabase'

function slugify(input: string): string {
  if (!input) return ''
  return input.replace(/[^A-Za-z0-9]+/g, '-').replace(/^-+|-+$/g, '').toLowerCase()
}

async function getSubcategoryBySlugs(categorySlug: string, subcategorySlug: string) {
  const { data, error } = await supabase
    .from('v_subcategories_slug')
    .select('subcategory_id, subcategory_name, category_id, category_name, category_slug, subcategory_slug')
    .eq('category_slug', categorySlug)
    .eq('subcategory_slug', subcategorySlug)
    .maybeSingle()
  if (error) throw error
  return data
}

async function getSdkListBySubcategoryId(subcategoryId: number) {
  const { data, error } = await supabase
    .from('v_sdks')
    .select('id, sdk_name, package_prefix, description, official_web, subcategory_id')
    .eq('subcategory_id', subcategoryId)
    .order('sdk_name')
  if (error) throw error
  return data ?? []
}

export default async function SdkList({ categorySlug, subcategorySlug }: { categorySlug: string, subcategorySlug: string }) {
  const sub = await getSubcategoryBySlugs(categorySlug, subcategorySlug)
  if (!sub) return null
  const sdks = await getSdkListBySubcategoryId(sub.subcategory_id)

  return (
    <div className="card p-5">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-lg font-semibold">该子分类下的 SDK</h2>
        <span className="text-xs text-satin-600">共 {sdks.length} 个</span>
      </div>
      {sdks.length === 0 ? (
        <div className="text-sm text-neutral-600">暂无 SDK 数据</div>
      ) : (
        <ul className="divide-y divide-satin-200">
          {sdks.map((s: any) => (
            <li key={s.id} className="py-3 flex items-center justify-between">
              <div>
                <div className="font-medium text-slate-900">
                  <Link href={`/categories/${categorySlug}/${subcategorySlug}/${slugify(s.sdk_name)}`} className="link">
                    {s.sdk_name}
                  </Link>
                </div>
                <div className="text-xs text-neutral-600">{s.package_prefix}</div>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  )
}
