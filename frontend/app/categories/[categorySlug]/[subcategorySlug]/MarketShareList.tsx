'use client'

import { useMemo, useState } from 'react'
import Link from 'next/link'

export type MarketItem = { name: string; count: number; percent: number }

export default function MarketShareList({ items, pageSize = 10, categorySlug, subcategorySlug }: { items: MarketItem[]; pageSize?: number; categorySlug: string; subcategorySlug: string }) {
  const [page, setPage] = useState(1)
  const totalPages = Math.max(1, Math.ceil(items.length / pageSize))
  const pageItems = useMemo(() => {
    const start = (page - 1) * pageSize
    return items.slice(start, start + pageSize)
  }, [items, page, pageSize])

  const go = (p: number) => setPage(Math.min(totalPages, Math.max(1, p)))

  return (
    <div className="space-y-4">
      {/* 列表（分页）*/}
      <div className="space-y-3">
        {pageItems.map((it, i) => {
          const rank = (page - 1) * pageSize + i + 1
          return (
            <div key={rank} className="grid grid-cols-12 items-center gap-3">
              <div className="col-span-5 truncate text-sm">
                {rank}. {' '}
                <Link href={{ pathname: `/categories/${categorySlug}/${subcategorySlug}/apps`, query: { sdk: it.name } }} className="link">
                  {it.name}
                </Link>
              </div>
              <div className="col-span-2 text-xs text-neutral-600">{it.count}</div>
              <div className="col-span-5">
                <div className="h-2 w-full bg-satin-200 rounded-full overflow-hidden">
                  <div className="h-2 bg-accent" style={{ width: `${it.percent}%` }} />
                </div>
                <div className="text-[10px] text-neutral-600 mt-1">{it.percent}%</div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 分页控件 */}
      <div className="flex items-center justify-end gap-2 text-xs">
        <button className="btn btn-xs" onClick={() => go(1)} disabled={page === 1}>«</button>
        <button className="btn btn-xs" onClick={() => go(page - 1)} disabled={page === 1}>上一页</button>
        <span className="px-2">{page} / {totalPages}</span>
        <button className="btn btn-xs" onClick={() => go(page + 1)} disabled={page === totalPages}>下一页</button>
        <button className="btn btn-xs" onClick={() => go(totalPages)} disabled={page === totalPages}>»</button>
      </div>
    </div>
  )
}
