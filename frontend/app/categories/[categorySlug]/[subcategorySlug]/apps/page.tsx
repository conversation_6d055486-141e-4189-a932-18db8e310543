// app/categories/[categorySlug]/[subcategorySlug]/apps/page.tsx
import Link from 'next/link'
import AppsPager from './AppsPager'
import { supabase } from '@/utils/supabase'

export const revalidate = 0
export const dynamic = 'force-dynamic'

function toNumber(x: any): number {
  if (typeof x === 'number') return x
  const n = Number(x)
  return Number.isNaN(n) ? 0 : n
}

type SdkInfo = { sdk_name: string; description: string | null; official_web: string | null; company_name?: string | null; package_prefix: string | null }
type SdkInfoResult = { data: SdkInfo | null; error?: string }
function slugify(input: string): string {
  if (!input) return ''
  return input.replace(/[^A-Za-z0-9]+/g, '-').replace(/^-+|-+$/g, '').toLowerCase()
}
async function getSdkInfo(subcategoryId: number, sdk: string): Promise<SdkInfoResult> {
  // 1) 精确等值匹配（最快）
  const { data, error } = await supabase
    .from('api_sdks_lite')
    .select('sdk_name, description, official_web, company_name, package_prefix')
    .eq('subcategory_id', subcategoryId)
    .eq('sdk_name', sdk)
    .maybeSingle()
  if (!error && data) return { data: (data as any) }
  // 2) 回退：大小写/空格差异，使用 ilike 模糊匹配，再用 slug 精确比对
  const { data: list, error: e2 } = await supabase
    .from('api_sdks_lite')
    .select('sdk_name, description, official_web, company_name, package_prefix')
    .eq('subcategory_id', subcategoryId)
    .ilike('sdk_name', `%${sdk}%`)
    .limit(50)
  if (!e2 && list && list.length > 0) {
    const targetSlug = slugify(sdk)
    const hit = (list as any[]).find(row => slugify(String(row.sdk_name)) === targetSlug) || (list as any[])[0]
    return { data: (hit as any) }
  }
  // 3) 最终回退：不限定子类目，仅按名称模糊匹配（避免跨库数据差异导致的 miss）
  const { data: list2, error: e3 } = await supabase
    .from('api_sdks_lite')
    .select('sdk_name, description, official_web, company_name, package_prefix')
    .ilike('sdk_name', `%${sdk}%`)
    .limit(50)
  if (!e3 && list2 && list2.length > 0) {
    const targetSlug2 = slugify(sdk)
    const hit2 = (list2 as any[]).find(row => slugify(String(row.sdk_name)) === targetSlug2) || (list2 as any[])[0]
    return { data: (hit2 as any) }
  }
  return { data: null, error: e2?.message || e3?.message || (error?.message ?? 'not found') }
}

function getAppStoreLink(appId: string): string {
  // 以 Android 包名为例跳转到 Google Play；如果是 iOS 可在后续根据前缀再扩展
  return `https://play.google.com/store/apps/details?id=${encodeURIComponent(appId)}`
}

// track timers to avoid Node warnings and duplicate labels
const __timers = new Set<string>()
function timeStart(label: string) {
  // 如果同名计时还未结束，先安全结束后再开启，避免“Label already exists”警告
  if (__timers.has(label)) {
    try { console.timeEnd(label) } catch {}
    __timers.delete(label)
  }
  __timers.add(label)
  console.time(label)
}
function timeEndSafe(label: string) {
  if (__timers.has(label)) {
    console.timeEnd(label)
    __timers.delete(label)
  }
}

async function getSubcategoryBySlugs(categorySlug: string, subcategorySlug: string) {
  timeStart(`[apps] subcategory fetch ${categorySlug}/${subcategorySlug}`)
  const { data, error } = await supabase
    .from('api_subcategories_slug')
    .select('subcategory_id, subcategory_name')
    .eq('category_slug', categorySlug)
    .eq('subcategory_slug', subcategorySlug)
    .maybeSingle()
  timeEndSafe(`[apps] subcategory fetch ${categorySlug}/${subcategorySlug}`)
  if (error) throw error
  return data
}

type AppWithDownloads = { app_id: string; app_name: string; download_max: number; icon?: string | null; store_name?: string | null; store_type?: string | null; last_updated?: string | null }

async function fetchListFast(subcategoryId: number, sdk: string, start: number, pageSize: number): Promise<AppWithDownloads[]> {
  const base = process.env.NEXT_PUBLIC_SUPABASE_URL
  const anon = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  if (!base || !anon) throw new Error('Supabase REST env not set')
  const limit = pageSize + 1
  const url = `${base}/rest/v1/api_apps_by_sdk_with_icon?select=app_id,app_name,download_max,icon&kb_subcategory_id=eq.${encodeURIComponent(String(subcategoryId))}&kb_sdk_name=eq.${encodeURIComponent(sdk)}&order=download_max.desc,app_id.asc&limit=${limit}&offset=${start}`
  const controller = new AbortController()
  const t = setTimeout(() => controller.abort(), 1500)
  try {
    const res = await fetch(url, {
      method: 'GET',
      headers: {
        apikey: anon,
        Accept: 'application/json',
        Prefer: 'count=none',
      },
      signal: controller.signal,
    })
    if (!res.ok) throw new Error(`REST ${res.status}`)
    const text = await res.text()
    const data = text ? JSON.parse(text) : []
    // REST 已按 order 返回，但再做一次稳定排序保障
    const rows = (data as any[]).sort((a, b) => (b.download_max - a.download_max) || String(a.app_id).localeCompare(String(b.app_id)))
    return rows as AppWithDownloads[]
  } finally {
    clearTimeout(t)
  }
}

async function getAppsPage(subcategoryId: number, sdk: string, start: number, pageSize: number): Promise<{ items: AppWithDownloads[]; total: number; hasNext: boolean; fetchedCount: number }>{
  // 单次查询：使用 serving.mv_apps_by_sdk_with_downloads，数据库端排序与分页；总数用 planned 估算，性能更稳
  timeStart(`[apps] list fetch sub=${subcategoryId} sdk="${sdk}" start=${start} size=${pageSize}`)
  // 先走 supabase-js；若超过 2.5s，则快速降级到直接 REST（同权限，避开 SDK 额外开销）
  const supabasePromise = supabase
    .from('api_apps_by_sdk_with_icon')
    .select('app_id, app_name, download_max, icon', { count: 'planned' })
    .eq('kb_subcategory_id', subcategoryId)
    .eq('kb_sdk_name', sdk)
    .order('download_max', { ascending: false })
    .order('app_id', { ascending: true })
    .range(start, start + pageSize)
  const timeout = new Promise<{ data: AppWithDownloads[]; count: number | null; error: any }>(resolve => setTimeout(async () => {
    try {
      const fast = await fetchListFast(subcategoryId, sdk, start, pageSize)
      resolve({ data: fast, count: null, error: null })
    } catch (e) {
      resolve({ data: [], count: null, error: e })
    }
  }, 1500))
  const { data, error, count } = await Promise.race<any>([supabasePromise, timeout])
  timeEndSafe(`[apps] list fetch sub=${subcategoryId} sdk="${sdk}" start=${start} size=${pageSize}`)
  if (error) throw error
  const rows = (data ?? []) as AppWithDownloads[]
  const fetchedCount = rows.length
  const hasNext = fetchedCount > pageSize
  const items = hasNext ? rows.slice(0, pageSize) : rows
  return { items, total: count ?? 0, hasNext, fetchedCount }
}

// 快速计数：优先读计数 MV；否则用 count:'planned' + range(0,0)（估算且速度快，避免 head:true 触发 terminated）
async function getTotalFast(subcategoryId: number, sdk: string): Promise<number> {
  // 1) 市占口径精确计数：public.mv_app_sdk_market_share（每 App 计 1、最新版本、同名合并）
  try {
    timeStart(`[apps] count market sub=${subcategoryId} sdk="${sdk}"`)
    const { data, error } = await supabase
      .from('api_app_sdk_market_share')
      .select('cnt')
      .eq('subcategory_id', subcategoryId)
      .eq('kb_sdk_name', sdk)
      .maybeSingle()
    timeEndSafe(`[apps] count market sub=${subcategoryId} sdk="${sdk}"`)
    if (!error && data && typeof (data as any).cnt === 'number') {
      return (data as any).cnt as number
    }
  } catch {}
  // 2) 估算计数（避免 head:true，返回 1 行并依赖内容范围计数头）
  timeStart(`[apps] count planned sub=${subcategoryId} sdk="${sdk}"`)
  const { count, error: err2 } = await supabase
    .from('api_apps_by_sdk_with_icon')
    .select('app_id', { count: 'planned' })
    .eq('kb_subcategory_id', subcategoryId)
    .eq('kb_sdk_name', sdk)
    .range(0, 0)
  timeEndSafe(`[apps] count planned sub=${subcategoryId} sdk="${sdk}"`)
  if (err2) return 0
  return count ?? 0
}

// 兼容：有的 Next 版本把 params/searchParams 作为 Promise 传递，
// 有的直接是对象。这里统一做一次解析。
async function resolveParams<T>(maybe: T | Promise<T>): Promise<T> {
  // @ts-ignore
  return typeof (maybe as any)?.then === 'function' ? await (maybe as any) : (maybe as T)
}

export default async function AppsBySdkPage({ params, searchParams }: { params: { categorySlug: string, subcategorySlug: string } | Promise<{ categorySlug: string, subcategorySlug: string }>, searchParams: { sdk?: string, page?: string, debug?: string } | Promise<{ sdk?: string, page?: string, debug?: string }> }) {
  timeStart('[apps] total render')
  const { categorySlug, subcategorySlug } = await resolveParams(params)
  const { sdk, page, debug } = await resolveParams(searchParams)
  if (!sdk) return <div className="px-6 py-8 max-w-6xl mx-auto">缺少 sdk 参数</div>

  let sub: any = null
  try {
    sub = await getSubcategoryBySlugs(categorySlug, subcategorySlug)
  } catch (e: any) {
    return <div className="px-6 py-8 max-w-6xl mx-auto">加载子分类信息失败：{e?.message || '未知错误'}</div>
  }
  if (!sub) return <div className="px-6 py-8 max-w-6xl mx-auto">子分类不存在</div>

  const pageSize = 10
  const p = Math.max(1, toNumber(page || 1))
  const start = (p - 1) * pageSize

  // 构造分页链接，手动对 sdk 做 encode，避免对象 query 在特殊字符（空格、+、非 ASCII）下的问题
  const buildHref = (toPage: number) => `/categories/${categorySlug}/${subcategorySlug}/apps?sdk=${encodeURIComponent(sdk!)}&page=${toPage}${debug === '1' ? '&debug=1' : ''}`

  // 并行请求列表与总数：计数增加软超时（1.5s），避免网络慢时阻塞整页渲染
  const softTimeout = (ms: number) => new Promise<number>((resolve) => setTimeout(() => resolve(0), ms))
  const subIdNum = toNumber(sub.subcategory_id)
  const [listRes, totalRes, sdkInfoRes] = await Promise.allSettled([
    getAppsPage(subIdNum, sdk, start, pageSize),
    Promise.race([getTotalFast(subIdNum, sdk), softTimeout(1500)]),
    getSdkInfo(subIdNum, sdk),
  ])
  timeEndSafe('[apps] total render')
  if (listRes.status === 'rejected') {
    return (
      <div className="px-6 py-8 max-w-6xl mx-auto space-y-4">
        <div className="text-sm text-red-600">应用列表加载失败：{(listRes as any).reason?.message || '未知错误'}</div>
      </div>
    )
  }
  const appsPage = listRes.value
  const total = totalRes.status === 'fulfilled' ? totalRes.value : 0
  const sdkInfoData = sdkInfoRes.status === 'fulfilled' ? (sdkInfoRes.value as SdkInfoResult) : { data: null, error: 'sdkInfo promise rejected' }
  const sdkInfo = sdkInfoData.data
  const favicon = null
  const apps = appsPage.items
  const totalPages = Math.max(1, Math.ceil((total || 0) / pageSize))
  const canPrev = p > 1
  const canNext = appsPage.hasNext || p < totalPages

  return (
    <div className="px-6 py-8 max-w-6xl mx-auto space-y-6">
      <nav className="text-sm text-satin-700">
        <Link href="/categories" className="link">分类</Link>
        <span className="mx-1">/</span>
        <span>{sub.subcategory_name}</span>
        <span className="mx-1">/</span>
        <span className="opacity-80">{sdk}</span>
      </nav>

      <div className="card p-5">
        {sdkInfo && (
          <div className="mb-5 flex items-start justify-between gap-4">
            <div className="space-y-2 min-w-0">
              <div className="flex items-center gap-3 min-w-0">
                <h2 className="text-xl font-semibold truncate" title={sdkInfo.sdk_name}>{sdkInfo.sdk_name}</h2>
                {sdkInfo.company_name && (
                  <span className="text-xs px-1.5 py-0.5 rounded bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300">
                    {sdkInfo.company_name}
                  </span>
                )}
              </div>
              <div className="space-y-1">
                <h3 className="text-sm font-semibold text-neutral-800">详细介绍</h3>
                <p className="text-sm text-neutral-700 line-clamp-3 whitespace-pre-line">
                  {sdkInfo.description || '暂无介绍'}
                </p>
              </div>
            </div>
            {/* 右侧操作区域移除（官网/暂无官网按钮） */}
          </div>
        )}
        {debug === '1' && (
          <div className="mb-3 text-xs font-mono text-neutral-600">
            <div>debug: p={p}, pageSize={pageSize}, start={start}</div>
            <div>fetchedCount={appsPage.fetchedCount}, hasNext={String(appsPage.hasNext)}</div>
            <div>total={total} (exact head), totalPages={totalPages}</div>
            <div>sdkInfo.exists={String(!!sdkInfo)} err={(sdkInfoData as any).error || ''}</div>
          </div>
        )}
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold">使用 {sdk} 的应用</h2>
          <span className="text-xs text-satin-600">共 {total} 个 · 第 {p} / {totalPages} 页</span>
        </div>
        {apps.length === 0 ? (
          <div className="text-sm text-neutral-600">暂无应用</div>
        ) : (
          <>
            <ul className="divide-y divide-satin-200">
              {apps.map((a, i) => {
                const icon = (a as any).icon as string | undefined
                return (
                  <li key={`${a.app_id}-${i}`} className="py-3 flex items-center justify-between">
                    <div className="flex items-center gap-3 min-w-0">
                      {icon ? (
                        <img src={icon} alt="icon" className="w-8 h-8 rounded-md object-cover bg-satin-100" />
                      ) : (
                        <div className="w-8 h-8 rounded-md bg-satin-200 flex items-center justify-center text-satin-700 text-sm font-medium select-none">
                          {(a.app_name?.[0] || '?').toUpperCase()}
                        </div>
                      )}
                      <div className="min-w-0">
                        <Link href={`/apps/${encodeURIComponent(a.app_id)}`} className="text-sm hover:underline truncate block">
                          {i + 1}. {a.app_name}
                        </Link>
                        <div className="text-xs text-neutral-600 truncate flex items-center gap-2">
                          <span>App ID: {a.app_id}</span>
                          <a href={getAppStoreLink(a.app_id)} target="_blank" rel="noopener noreferrer" className="inline-block text-satin-600 hover:text-satin-800">
                            外链
                          </a>
                        </div>
                      </div>
                    </div>
                    <div className="text-xs text-neutral-700 shrink-0">下载量：{a.download_max.toLocaleString()}</div>
                  </li>
                )
              })}
            </ul>
            {/* 分页 */}
            <AppsPager
              categorySlug={categorySlug}
              subcategorySlug={subcategorySlug}
              sdk={sdk}
              p={p}
              totalPages={totalPages}
              canPrev={canPrev}
              canNext={canNext}
              debug={debug}
            />
          </>
        )}
      </div>
    </div>
  )
}
