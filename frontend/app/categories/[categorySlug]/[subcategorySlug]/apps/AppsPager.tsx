"use client";
import React from "react";
import Link from "next/link";

type Props = {
  categorySlug: string;
  subcategorySlug: string;
  sdk: string;
  p: number;
  totalPages: number;
  canPrev: boolean;
  canNext: boolean;
  debug?: string;
};

function buildHref(categorySlug: string, subcategorySlug: string, sdk: string, toPage: number, debug?: string) {
  const q = `sdk=${encodeURIComponent(sdk)}&page=${toPage}` + (debug === "1" ? "&debug=1" : "");
  return `/categories/${categorySlug}/${subcategorySlug}/apps?${q}`;
}

export default function AppsPager({ categorySlug, subcategorySlug, sdk, p, totalPages, canPrev, canNext, debug }: Props) {
  return (
    <>
      <div className="flex items-center justify-end gap-2 text-xs mt-3">
        {canPrev ? (
          <a className="btn btn-xs" href={buildHref(categorySlug, subcategorySlug, sdk, 1, debug)}>«</a>
        ) : (
          <span className="btn btn-xs btn-disabled">«</span>
        )}
        {canPrev ? (
          <a className="btn btn-xs" href={buildHref(categorySlug, subcategorySlug, sdk, Math.max(1, p - 1), debug)}>上一页</a>
        ) : (
          <span className="btn btn-xs btn-disabled">上一页</span>
        )}
        <span className="px-2">第 {p} / {totalPages} 页</span>
        {canNext ? (
          <a className="btn btn-xs" href={buildHref(categorySlug, subcategorySlug, sdk, p + 1, debug)}>下一页</a>
        ) : (
          <span className="btn btn-xs btn-disabled">下一页</span>
        )}
        {canNext ? (
          <a className="btn btn-xs" href={buildHref(categorySlug, subcategorySlug, sdk, totalPages, debug)}>»</a>
        ) : (
          <span className="btn btn-xs btn-disabled">»</span>
        )}
      </div>
      {debug === '1' && (
        <div className="mt-2 text-[11px] text-neutral-600">
          <div>raw hrefs:
            <a className="link ml-1" href={buildHref(categorySlug, subcategorySlug, sdk, 1, debug)}>«</a>
            <span className="mx-1">|</span>
            <a className="link" href={buildHref(categorySlug, subcategorySlug, sdk, Math.max(1, p - 1), debug)}>上一页</a>
            <span className="mx-1">|</span>
            <a className="link" href={buildHref(categorySlug, subcategorySlug, sdk, p + 1, debug)}>下一页</a>
            <span className="mx-1">|</span>
            <a className="link" href={buildHref(categorySlug, subcategorySlug, sdk, totalPages, debug)}>»</a>
          </div>
        </div>
      )}
    </>
  );
}
