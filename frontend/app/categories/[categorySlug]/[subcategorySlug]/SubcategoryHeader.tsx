// app/categories/[categorySlug]/[subcategorySlug]/SubcategoryHeader.tsx
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { supabase } from '@/utils/supabase'

async function getSubcategoryBySlugs(categorySlug: string, subcategorySlug: string) {
  const { data, error } = await supabase
    .from('api_subcategories_slug')
    .select('subcategory_id, subcategory_name, category_id, category_name')
    .eq('category_slug', categorySlug)
    .eq('subcategory_slug', subcategorySlug)
    .maybeSingle()
  if (error) throw error
  return data
}

export default async function SubcategoryHeader({ categorySlug, subcategorySlug }: { categorySlug: string, subcategorySlug: string }) {
  const sub = await getSubcategoryBySlugs(categorySlug, subcategorySlug)
  if (!sub) return notFound()

  return (
    <>
      {/* 头部信息（面包屑已移至全局 TopBar） */}
      <header className="card p-5">
        <div className="flex items-start justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-slate-900">{sub.subcategory_name}</h1>
            <p className="text-sm text-satin-700 mt-1">属于分类：{sub.category_name}</p>
          </div>
        </div>
      </header>
    </>
  )
}
