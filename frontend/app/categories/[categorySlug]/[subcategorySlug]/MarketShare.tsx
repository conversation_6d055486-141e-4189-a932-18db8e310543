// app/categories/[categorySlug]/[subcategorySlug]/MarketShare.tsx
import { supabase } from '@/utils/supabase'
import MarketShareList from './MarketShareList'
import PieChartTop10 from './PieChartTop10'

function toNumber(x: any): number {
  if (typeof x === 'number') return x
  const n = Number(x)
  return Number.isNaN(n) ? 0 : n
}

async function getSubcategoryBySlugs(categorySlug: string, subcategorySlug: string) {
  const { data, error } = await supabase
    .from('api_subcategories_slug')
    .select('subcategory_id')
    .eq('category_slug', categorySlug)
    .eq('subcategory_slug', subcategorySlug)
    .maybeSingle()
  if (error) throw error
  return data
}

async function getOverviewRows(subcategoryId: number) {
  // 仅从物化/聚合视图读取：mv_app_sdk_market_share（MV-only）
  const { data, error } = await supabase
    .from('api_app_sdk_market_share')
    .select('kb_sdk_name, cnt')
    .eq('subcategory_id', subcategoryId)
    .order('cnt', { ascending: false })
  if (error) {
    // MV 不存在或报错则返回空，不做 v_* 降级
    return []
  }
  return (data ?? []).map((r: any) => ({ kb_sdk_name: r.kb_sdk_name, app_version_sdk_key: null, is_matched_kb: true, _agg_count: toNumber(r.cnt) }))
}

export default async function MarketShare({ categorySlug, subcategorySlug }: { categorySlug: string, subcategorySlug: string }) {
  const sub = await getSubcategoryBySlugs(categorySlug, subcategorySlug)
  if (!sub) return null
  const subId = toNumber(sub.subcategory_id)
  const rows = await getOverviewRows(subId)

  // 聚合：以 kb_sdk_name 分组计数
  const counts = new Map<string, number>()
  for (const r of rows) {
    const key = r.kb_sdk_name ?? 'Unknown'
    const inc = typeof (r as any)._agg_count === 'number' ? (r as any)._agg_count : 1
    counts.set(key, (counts.get(key) ?? 0) + inc)
  }
  // 直接使用已匹配到的数据（不做零填充，不再限定“该子分类下的 SDK”名单）
  const entries = Array.from(counts.entries()).map(([name, cnt]) => ({ name, count: toNumber(cnt) }))
  const total = entries.reduce((a, b) => a + b.count, 0)
  const items = entries
    .map(({ name, count }) => ({ name, count, percent: total ? Math.round((count / total) * 100) : 0 }))
    .sort((a, b) => b.count - a.count)

  return (
    <div className="card p-5">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-lg font-semibold">Market share（基于匹配到的应用数）</h2>
        <span className="text-xs text-satin-600">总计 {total}</span>
      </div>
      {items.length === 0 ? (
        <div className="text-sm text-neutral-600">暂无统计数据</div>
      ) : (
        <>
          <div className="mb-4">
            <PieChartTop10 items={items} />
          </div>
          <MarketShareList items={items} pageSize={10} categorySlug={categorySlug} subcategorySlug={subcategorySlug} />
        </>
      )}
    </div>
  )
}
