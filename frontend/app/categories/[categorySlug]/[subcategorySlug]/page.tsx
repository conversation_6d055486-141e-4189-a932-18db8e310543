// app/categories/[categorySlug]/[subcategorySlug]/page.tsx
// 框架先渲染 + Suspense：Header 与 SDK 列表分别局部加载
import Link from 'next/link'
import { Suspense } from 'react'
import SubcategoryHeader from './SubcategoryHeader'
import MarketShare from './MarketShare'

export const revalidate = 0

function HeaderFallback() {
  return (
    <>
      {/* 极简占位：避免整页灰色骨架闪现 */}
      <nav className="text-sm text-satin-700">
        <span className="opacity-60">加载中…</span>
      </nav>
      <header className="card p-5">
        <div className="space-y-1">
          <div className="text-base text-satin-700 opacity-60">正在加载标题…</div>
        </div>
      </header>
    </>
  )
}

// 移除 SdkList 模块

function MarketShareFallback() {
  return (
    <div className="card p-5">
      <div className="h-5 w-48 skeleton skeleton-rounded mb-3" />
      <div className="space-y-2">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="h-3 w-full skeleton skeleton-rounded" />
        ))}
      </div>
    </div>
  )
}

export default async function SubcategoryBySlugsPage({ params }: { params: Promise<{ categorySlug: string, subcategorySlug: string }> }) {
  const { categorySlug, subcategorySlug } = await params

  return (
    <div className="px-6 py-8 max-w-6xl mx-auto space-y-6">
      {/* 面包屑 + 头部：Suspense 局部加载 */}
      <Suspense fallback={<HeaderFallback />}> 
        <SubcategoryHeader categorySlug={categorySlug} subcategorySlug={subcategorySlug} />
      </Suspense>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <section className="lg:col-span-2 space-y-6">
          <div className="card p-5">
            <h2 className="text-lg font-semibold mb-2">概览</h2>
            <p className="text-sm text-neutral-600">子分类简介（如需从后端补字段可替换）。</p>
          </div>

          {/* Market Share：基于 app_sdk_overview 按 KB 名称聚合 */}
          <Suspense fallback={<MarketShareFallback />}>
            <MarketShare categorySlug={categorySlug} subcategorySlug={subcategorySlug} />
          </Suspense>

          {/* SDK 列表模块已移除 */}
        </section>
        <aside className="space-y-4">
          <div className="card p-4">
            <h3 className="text-sm font-semibold mb-2">相关链接</h3>
            <ul className="text-sm list-disc list-inside text-satin-700">
              <li><Link href="/categories" className="link">返回分类列表</Link></li>
            </ul>
          </div>
        </aside>
      </div>
    </div>
  )
}
