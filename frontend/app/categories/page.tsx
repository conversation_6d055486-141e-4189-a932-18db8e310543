// RSC 页面：框架先渲染 + Suspense 局部加载，减少抖动
import { Suspense } from 'react'
import CategoriesList from './CategoriesList'

export const revalidate = 0 // 开发阶段禁用缓存，便于调试

function ListFallback() {
  // 与真实结构接近：若干分类卡片，每个卡片内若干行骨架
  return (
    <div className="space-y-6">
      {Array.from({ length: 4 }).map((_, i) => (
        <section key={i} className="rounded-xl border border-satin-200 bg-satin-50 dark:bg-neutral-900/40 shadow-sm">
          <header className="flex items-center justify-between px-4 py-3 border-b border-satin-200">
            <div className="h-5 w-48 skeleton skeleton-rounded" />
            <div className="flex items-center gap-3">
              <div className="h-4 w-16 skeleton skeleton-rounded" />
              <div className="h-4 w-16 skeleton skeleton-rounded" />
            </div>
          </header>
          <div className="p-4">
            <ul className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-2">
              {Array.from({ length: 6 }).map((_, j) => (
                <li key={j} className="flex items-center justify-between">
                  <div className="h-4 w-40 skeleton skeleton-rounded" />
                  <div className="h-3 w-10 skeleton skeleton-rounded" />
                </li>
              ))}
            </ul>
          </div>
        </section>
      ))}
    </div>
  )
}

export default function CategoriesPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-6">
      <h1 className="text-2xl font-bold">分类与子分类</h1>
      <div className="bg-white dark:bg-neutral-dark/60 border border-satin-200 dark:border-slate-700 rounded-xl px-6 py-4 shadow-sm">
        <p className="text-sm text-slate-600 dark:text-slate-400">浏览各大分类与子分类，快速进入你需要的 SDK 列表与应用集合。</p>
      </div>


      {/* 列表主体采用 Suspense，fallback 使用与真实结构接近的卡片+行骨架 */}
      <Suspense fallback={<ListFallback />}> 
        {/* 服务端组件：实际数据 */}
        <CategoriesList />
      </Suspense>
    </div>
  )
}
