// app/categories/CategoriesList.tsx
import { supabaseServing as supabaseServingClient, supabase as supabasePublicClient } from '@/utils/supabase'

function toNumber(x: any): number {
  if (typeof x === 'number') return x
  const n = Number(x)
  return Number.isNaN(n) ? 0 : n
}

type CategoryRow = {
  category_id: number
  category_name: string
  category_slug: string
  subcategory_count: number
  sdk_count: number
}

type SubcategoryRow = {
  subcategory_id: number
  subcategory_name: string
  category_id: number
  category_name: string
  category_slug: string
  subcategory_slug: string
  sdk_count: number
}

export default async function CategoriesList() {
  // 读取 serving 架构下的同名视图，符合“MV-only/serving 只读视图”的规范
  let catData, catError, subData, subError
  try {
    const r1 = await supabaseServingClient.from('api_categories_slug').select('*').order('category_id')
    catData = r1.data; catError = r1.error
  } catch (e: any) {
    catError = { message: `fetch categories failed: ${e?.message || e}` } as any
  }
  try {
    const r2 = await supabaseServingClient.from('api_subcategories_slug').select('*').order('category_id').order('subcategory_id')
    subData = r2.data; subError = r2.error
  } catch (e: any) {
    subError = { message: `fetch subcategories failed: ${e?.message || e}` } as any
  }

  // Fallback：如果 serving 失败，尝试 public 架构，避免页面完全不可用
  if ((catError || !catData) || (subError || !subData)) {
    try {
      const r1b = await supabasePublicClient.from('api_categories_slug').select('*').order('category_id')
      if (!catData && !r1b.error) { catData = r1b.data; catError = null as any }
      const r2b = await supabasePublicClient.from('api_subcategories_slug').select('*').order('category_id').order('subcategory_id')
      if (!subData && !r2b.error) { subData = r2b.data; subError = null as any }
    } catch {}
  }

  if (catError || subError) {
    return (
      <div className="p-4">
        {catError && <pre className="text-red-600">分类加载失败: {catError.message}</pre>}
        {subError && <pre className="text-red-600">子分类加载失败: {subError.message}</pre>}
      </div>
    )
  }

  const rows: CategoryRow[] = (catData ?? []).map((r: any) => ({
    category_id: toNumber(r.category_id),
    category_name: r.category_name,
    category_slug: r.category_slug,
    subcategory_count: toNumber(r.subcategory_count),
    sdk_count: toNumber(r.sdk_count),
  }))

  const subRows: SubcategoryRow[] = (subData ?? []).map((r: any) => ({
    subcategory_id: toNumber(r.subcategory_id),
    subcategory_name: r.subcategory_name,
    category_id: toNumber(r.category_id),
    category_name: r.category_name,
    category_slug: r.category_slug,
    subcategory_slug: r.subcategory_slug,
    sdk_count: toNumber(r.sdk_count),
  }))

  const byCategory = new Map<number, SubcategoryRow[]>()
  for (const s of subRows) {
    const list = byCategory.get(s.category_id) ?? []
    list.push(s)
    byCategory.set(s.category_id, list)
  }

  return (
    <div className="space-y-6">
      {rows.map((c) => {
        const subs = byCategory.get(c.category_id) ?? []
        return (
          <section id={`cat-${c.category_id}`} key={c.category_id} className="rounded-xl border border-satin-200 bg-satin-50 dark:bg-neutral-900/40 shadow-sm">
            <header className="flex items-center justify-between px-4 py-3 border-b border-satin-200">
              <h2 className="text-lg font-semibold">{c.category_name}</h2>
              <div className="text-xs text-satin-600 flex items-center gap-3">
                <span>子分类 {c.subcategory_count}</span>
                <span>SDK {c.sdk_count}</span>
              </div>
            </header>
            <div className="p-4">
              {subs.length === 0 ? (
                <div className="text-sm text-neutral-500">暂无子分类</div>
              ) : (
                <ul className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-2">
                  {subs.map((s) => (
                    <li key={s.subcategory_id} className="flex items-center justify-between">
                      <a
                        href={`/categories/${rows.find(rc=>rc.category_id===s.category_id)?.category_slug ?? ''}/${s.subcategory_slug}`}
                        className="text-satin-700 hover:text-satin-800 hover:underline truncate"
                        title={s.subcategory_name}
                      >
                        {s.subcategory_name}
                      </a>
                      <span className="text-xs text-satin-600 ml-3">{s.sdk_count}</span>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </section>
        )
      })}

      {rows.length === 0 && (
        <div className="text-neutral-500">暂无数据</div>
      )}
    </div>
  )
}
