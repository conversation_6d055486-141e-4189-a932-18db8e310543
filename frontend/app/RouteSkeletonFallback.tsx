// app/RouteSkeletonFallback.tsx
// 全站统一且最小化的骨架（仅头部），避免出现与页面不相关的大面积网格
export default function RouteSkeletonFallback() {
  return (
    <div className="w-full">
      <div className="max-w-6xl mx-auto px-6 py-10 space-y-6">
        <div className="space-y-2">
          <div className="h-8 w-2/3 skeleton skeleton-rounded" />
          <div className="h-4 w-1/3 skeleton skeleton-rounded" />
        </div>
        <div className="flex items-center gap-3">
          <div className="h-8 w-24 skeleton skeleton-pill" />
          <div className="h-8 w-24 skeleton skeleton-pill" />
          <div className="h-8 w-40 skeleton skeleton-rounded" />
        </div>
      </div>
    </div>
  )
}
