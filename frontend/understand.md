# 前端整体说明（Next.js + Supabase 模板）

本文档概述 `frontend/` 的架构、目录与文件职责、运行机制与数据流，便于快速理解与协作。

## 一分钟上手：这是怎样的一套前端

这是一套以“服务端优先、安全优先”为原则的 Next.js App Router 前端：

- __技术栈__：Next.js(App Router + RSC)、React、Tailwind、Supabase(Auth/DB/REST)、Stripe(订阅，可选)、PostHog(埋点，可选)。
- __运行模型__：页面位于 `app/`，默认用 RSC 在服务端拉取数据直出 HTML；交互型 UI 用 `'use client'` 组件承载。
- __数据访问__：客户端仅用 `NEXT_PUBLIC_SUPABASE_*` 访问只读视图；写入/敏感读统一由 RSC 或 `app/api/*` 在服务端用 Service Role Key 完成。
- __鉴权与保护__：全局在 `app/layout.tsx` 注入 `AuthProvider` 与 `ProtectedRoute`；未登录或无订阅的用户会被友好引导。
- __支付订阅__：前端只发起动作，`app/api/stripe/*` 负责订阅创建/取消/恢复/同步与 Webhook 校验，密钥永不下发到浏览器。
- __埋点__：`utils/posthog.ts` 按环境变量开关，默认最小化上报；也接入了 `@vercel/analytics`。
- __安全基线__：能在服务器做的绝不放到浏览器，减小 bundle、提升首屏、避免密钥泄露。

```mermaid
flowchart LR
  A[浏览器(用户)] -->|访问路由/交互| B[Next.js App Router]
  B -->|RSC 数据获取| C[(Supabase PostgREST/Views)]
  B -->|触发动作 fetch| D[/Server Routes app/api/*/]
  D -->|服务端密钥| C
  D -->|订阅/支付| E[(Stripe API)]
  E -->|事件回调| F[/Stripe Webhook app/api/stripe/webhook/]
  B -->|可选埋点| G[(PostHog)]
  A <-.受控跳转/保护.-> B
```

__三步启动__：

1) 复制 `frontend/.env.example` 为 `.env.local` 并填好 `NEXT_PUBLIC_SUPABASE_URL/ANON_KEY`，其他可用占位值。
2) `npm i && npm run dev`，浏览 `http://localhost:3000`。
3) 如需订阅闭环，填好 Stripe 环境变量并在本地启动 `app/api/stripe/webhook`（或使用 Stripe CLI 进行事件转发）。

> 小抉择指南：
> - 列表/详情页优先做成 RSC（服务端取数）；
> - 需要表单、动画、浏览器 API 的局部才写 `'use client'`；
> - 所有写操作或需要服务端密钥的逻辑放 `app/api/*`。

---

## 深入版（约 2000 字）：设计理念、数据与权限、性能与演进

### 1) 架构哲学：Server-first 与契约化数据

- __Server-first__：RSC 将数据获取与 HTML 直出放在服务器，提升首屏、SEO 与安全性；客户端仅负责交互。
- __契约化数据__：通过 Supabase 的只读视图（如 `v_apps`、`v_sdks`、`v_categories`、`v_app_sdks`）暴露稳定的查询契约，解耦页面与底层表结构演进。
- __清晰边界__：
  - 浏览器：只读查询 + 轻交互；
  - 服务端（RSC/Route Handler）：敏感读/写、订阅同步、Webhook 校验、批处理。

### 2) 目录与职责要点强化

- `app/`：路由与页面。`app/layout.tsx` 当前为客户端组件，挂载全局 Provider 与导航；业务页面逐步以 RSC 获取数据，客户端组件聚焦交互。
- `components/`：通用 UI（`TopBar`、`NavBar`、`LoadingSpinner` 等）；业务组件尽量无状态或最小状态。
- `contexts/`：`AuthContext`、`ProtectedRoute` 聚焦客户端保护；若迁往 RSC，可用 `createServerClient()` + `cookies()` 直接在服务端解析会话。
- `utils/`：
  - `supabase.ts`（浏览器 anon）与 `supabase-admin.ts`（服务端 service role）严格分离；
  - `env.ts` 保证关键环境变量在启动期即被校验；
  - `posthog.ts` 仅在 key 存在时初始化，避免无谓 SDK 注入。
- `types/`：与 `v_*` 对齐的类型文件（如 `types/v_models.ts`），让 RSC、组件、仓储代码共享一套强类型。

### 3) 数据访问与权限模型细化

- __视图优先__：页面读取统一走 `v_*` 视图，保证查询稳定性与性能（可在 DB 侧做聚合与索引）。
- __RLS 与最小权限__：
  - 匿名/已登录用户对公共视图只读；
  - 私有数据严格以 `auth.uid()` 限定；
  - 写操作必须由 `app/api/*` 或 RSC 的服务端逻辑代办。
- __客户端只读__：`utils/supabase.ts` 仅用于 PostgREST 只读访问；避免误把 service role 引入客户端 bundle。
- __错误与重试__：建议新增 `repositories/` 统一封装分页、筛选、错误与退避重试策略，可在服务端做缓存（如 `fetch` 缓存或 Redis）。

### 4) 认证与会话流（Supabase Auth）

- 登录/注册在 `app/login/page.tsx`，使用 `LoginForm` 触发 Supabase Auth；邮箱验证回调由 `app/auth/callback/route.ts` 处理。
- 全局保护由 `ProtectedRoute` 在客户端生效；若迁往 RSC，可在每个页面的服务端环境读取会话并直接返回对应 UI（未登录时直出登录提示或重定向）。
- 密码找回与更新：`/reset-password` 发起邮件、`/update-password` 在恢复会话后重设密码。

### 5) 支付与订阅生命周期（Stripe）

- 客户端仅展示订阅状态与触发购买/取消/恢复按钮（`StripeBuyButton`、`/pay`）。
- 变更全部在服务端：`app/api/stripe/*` 负责创建/取消/恢复、同步 Supabase 侧用户订阅状态。
- Webhook：`/api/stripe/webhook` 校验签名、解析事件、更新 DB；任何需要幂等的逻辑在此集中实现（例如基于事件 ID 去重）。
- 用户自助：`/profile` 可同步状态或发起取消/恢复，前端 fetch 命中对应 route 后由服务端完成真实操作。

### 6) 性能与体验策略

- __首屏直出__：RSC 直出 HTML，减少客户端水合压力；对长列表使用 Suspense 与骨架屏（如 `/categories`）。
- __按需客户端__：仅把需要交互的区块写为 `'use client'`，其余保持服务端渲染，控制 bundle 体积与 hydration 成本。
- __缓存与并发__：
  - RSC 内用原生 `fetch` 缓存 + 标签；
  - 业务查询可在 `repositories/` 添加二级缓存；
  - 避免在客户端重复发起等价请求。
- __可感知加载__：`RouteSkeletonFallback` 与局部骨架让加载过程可见、结构稳定。

### 7) 可观察性与故障恢复

- __埋点__：PostHog 最小化集成，关闭自动采集，手动上报关键事件；生产可加采样率与匿名化处理。
- __日志__：服务端 Route 统一结构化日志（建议封装 logger）；Webhook/支付路径需详尽日志与告警。
- __错误处理__：为 `app/api/*` 约定统一错误响应格式（包含 error_code 与 trace id），便于前端提示与排障。

### 8) 从本地到生产：环境与密钥

- `.env.local` 只放本地变量；生产变量在部署平台配置，绝不提交到仓库。
- `SUPABASE_SERVICE_ROLE_KEY` 只在服务端使用：RSC 与 `app/api/*`；任何 `import` 到客户端的路径都不应间接引用它。
- Stripe Webhook 在开发时可由 Stripe CLI 转发；注意校验 `STRIPE_WEBHOOK_SECRET` 与事件幂等。

### 9) 常见陷阱与最佳实践清单

- 不要在客户端导入 `supabase-admin.ts`；若需要服务端能力，把逻辑挪到 `app/api/*` 并提供一个轻薄的 fetch 包装。
- 如果页面需要 SEO/首屏快，尽量用 RSC；只有在必须访问浏览器 API、处理复杂交互或第三方前端 SDK 时再使用客户端组件。
- 数据契约放在视图上，尽可能不要让页面直接依赖底层表结构；重构时只替换视图实现。
- 任何涉及计费与权限的状态都以服务端为准，客户端状态仅作展示与乐观更新，失败要能回滚与重试。
- 为分页/筛选结果制定稳定的排序与索引，避免翻页抖动与慢查。

### 10) 可演进的路线图

- 引入 `repositories/` 与 `lib/`：沉淀可复用的查询/缓存/重试与第三方适配器；
- 渐进式 RSC 化：将纯数据展示页面迁移至服务端渲染；
- 统一错误码与监控面板：为关键 API 建设 SLO、阈值告警与仪表盘；
- UI 与主题：在 `tailwind.config.ts` 扩展主题色与组件库，提升一致性。

## 目录（ToC）
- [架构与运行时](#架构与运行时)
- [环境变量](#环境变量)
- [环境变量速查表](#环境变量速查表)
- [目录与关键文件](#目录与关键文件)
- [数据流与权限](#数据流与权限)
- [与 Supabase 的约定](#与-supabase-的约定)
- [认证与路由保护](#认证与路由保护)
- [路由保护矩阵](#路由保护矩阵)
- [埋点与可观察性（可选）](#埋点与可观察性可选)
- [常用脚本](#常用脚本)
- [开发建议](#开发建议)
- [文件级功能与逻辑清单（frontend/）](#文件级功能与逻辑清单frontend)
 - [深入版（约 2000 字）：设计理念、数据与权限、性能与演进](#深入版约-2000-字设计理念数据与权限性能与演进)

## 架构与运行时

- React + Next.js App Router（默认 RSC：React Server Components）。
- 数据源：Supabase（Auth + PostgREST + Database 视图）。
- 支付与埋点（可选）：Stripe、PostHog。
- 样式：Tailwind CSS。

说明：当前 `app/layout.tsx` 顶部含 `'use client'`，因此布局为客户端组件；普通页面文件在未声明 `'use client'` 时默认是 RSC（在服务端执行数据获取与渲染）。

## 环境变量

参见 `frontend/.env.example` 与本地的 `.env.local`。

- 基础
  - `NEXT_PUBLIC_APP_URL`, `NEXT_PUBLIC_API_URL`, `NEXT_PUBLIC_WS_URL`
- Supabase
  - `NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`
  - `SUPABASE_SERVICE_ROLE_KEY`（仅服务端使用）
- Stripe（可选，占位可运行）
  - `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`, `NEXT_PUBLIC_STRIPE_BUTTON_ID`
  - `STRIPE_SECRET_KEY`, `STRIPE_WEBHOOK_SECRET`
- PostHog（可选）
  - `NEXT_PUBLIC_POSTHOG_KEY`, `NEXT_PUBLIC_POSTHOG_HOST`

环境校验见 `utils/env.ts`。如果暂未启用 Stripe/埋点，可使用占位值或放宽校验。

### 环境变量速查表

- 基础
  - `NEXT_PUBLIC_APP_URL`：前端自身对外 URL，用于构建回调/跳转链接。
  - `NEXT_PUBLIC_API_URL`：后端 API 基址（如 `http://localhost:8080`）。
  - `NEXT_PUBLIC_WS_URL`：后端 WebSocket 基址（如 `ws://localhost:8080`）。
- Supabase
  - `NEXT_PUBLIC_SUPABASE_URL`：Supabase 项目 URL（客户端可见）。
  - `NEXT_PUBLIC_SUPABASE_ANON_KEY`：Supabase 匿名键（客户端可见）。
  - `SUPABASE_SERVICE_ROLE_KEY`：Service Role Key（仅服务端，绝不可在客户端暴露）。
- OpenAI（如需）
  - `OPENAI_API_KEY`：仅服务端使用。
- Stripe
  - `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`：Publishable Key（客户端可见）。
  - `NEXT_PUBLIC_STRIPE_BUTTON_ID`：Stripe Buy Button ID（客户端可见）。
  - `STRIPE_SECRET_KEY`：Secret Key（仅服务端）。
  - `STRIPE_WEBHOOK_SECRET`：Webhook 签名密钥（仅服务端）。
- PostHog（可选）
  - `NEXT_PUBLIC_POSTHOG_KEY`：前端埋点 key。
  - `NEXT_PUBLIC_POSTHOG_HOST`：PostHog 服务器地址（默认 `https://app.posthog.com`）。

## 目录与关键文件

- `app/`
  - Next.js App Router 路由与页面。
  - 现有子路由：`api/`（接口路由）、`auth/`、`dashboard/`、`login/`、`pay/`、`profile/` 等。
  - `app/layout.tsx`：根布局（当前为客户端组件，注入全局样式、顶栏、路由保护等）。
- `components/`
  - 复用的 UI 组件（如导航栏、卡片、表单片段等）。
- `contexts/`
  - React 上下文，如 `AuthContext`、`ProtectedRoute` 等客户端侧的鉴权与保护路由封装。
- `hooks/`
  - 自定义 Hook。
- `utils/`
  - `supabase.ts`：浏览器/通用环境使用的 Supabase 客户端（基于 `NEXT_PUBLIC_SUPABASE_URL/ANON_KEY`）。
  - `supabase-admin.ts`：服务端使用的 Supabase 客户端（基于 `SUPABASE_SERVICE_ROLE_KEY`，严禁在客户端导入）。
  - `env.ts`：运行前的环境变量校验。
  - `posthog.ts`：PostHog 初始化（有 key 才启用）。
  - 其它：`analytics.ts` 等工具。
- `types/`
  - TypeScript 类型定义。例如 `types/ValidateEntryTypes.ts`。
  - 可在此补充与数据库视图（`v_apps`, `v_sdks`, `v_categories`, `v_app_sdks` 等）对齐的类型。
- `public/`
  - 静态资源（图标等）。
- `initial_supabase_table_schema.sql`
  - Supabase 端的基础表/策略/触发器/视图示例脚本（用于初始化或参考）。
- `tailwind.config.ts`
  - Tailwind 配置与主题色。可按产品配色进行扩展/替换。

## 数据流与权限

- 客户端（浏览器）默认通过 `utils/supabase.ts` 使用 anon key 访问 Supabase REST/PostgREST。
- 服务端（RSC / Route Handler）可使用 `utils/supabase-admin.ts` 完成仅服务端允许的操作（如管理、批处理），避免泄露 service role key。
- 推荐模式
  - 页面数据获取优先在 RSC（服务端）执行，减少前端 bundle、提升安全与首屏性能。
  - 必须在客户端执行的逻辑（交互、浏览器 API、第三方仅前端 SDK）才使用 `'use client'` 组件。

## 与 Supabase 的约定

项目使用只读视图提供查询契约（在本地 Supabase 已存在）：
- `v_apps`：应用列表/检索视图，含 `latest_analyzed_at`、`sdk_count` 等聚合字段。
- `v_sdks`：SDK 列表/详情统计视图，含分类关联与 `app_count`。
- `v_categories`：分类聚合视图。
- `v_app_sdks`：某应用的 SDK 清单（可按 `app_id` 过滤、按 `analyzed_at` 排序）。
- （可选）`v_subcategories`：子分类聚合视图。

RLS 策略建议：公共视图设为匿名/已登录只读；涉及用户私有数据的表按 `auth.uid()` 约束。

## 认证与路由保护

- 现状：`contexts/AuthContext` + `ProtectedRoute` 客户端保护。
- 建议：在服务器组件中通过 `createServerClient()` + `cookies()` 读取会话，页面直出；客户端仅负责交互与轻量状态。

### 路由保护矩阵

- 公共（无需登录）
  - `GET /`（`app/page.tsx`）：落地页。
  - `GET /login`：登录/注册页。
  - `GET /verify-email`：邮箱验证结果页。
  - `GET /categories`、`GET /subcategories/[id]`：分类/子分类展示（示例展示用）。
  - `GET /lookup`：演示页。
- 需登录（默认被 `ProtectedRoute` 保护）
  - `GET /dashboard`：仪表盘。
  - `GET /profile`：个人中心。
  - `GET /pay`：购买页（已订阅时重定向/提示）。
- 服务端 API（仅服务端可安全使用密钥）
  - `POST /api/stripe/webhook`：接收并校验 Stripe 事件（签名必需）。
  - `POST /api/stripe/cancel`、`POST /api/stripe/reactivate`、`POST /api/stripe/sync`、`GET /api/stripe/test`。
  - `DELETE /api/user/delete`：按权限删除用户相关数据。

## 埋点与可观察性（可选）

- `utils/posthog.ts`：当 `NEXT_PUBLIC_POSTHOG_KEY` 存在时初始化 PostHog，默认关闭自动采集与 pageview，按需手动上报。
- 可以接入 `@vercel/analytics`（已在 `app/layout.tsx` 中使用）。

## 常用脚本

- 开发：`npm run dev`（Next.js 开发服务器）。
- 构建：`npm run build`；启动：`npm start`。

## 开发建议

- 数据层：将公共查询封装到 `repositories/`（可新增目录）统一处理分页、筛选、错误与 bigint→number 转换。
- 类型：在 `types/` 中为 `v_*` 视图定义 TS 类型，组件/仓储复用。
- 布局：逐步减少全局客户端依赖，将页面主体迁移为 RSC 获取数据。
- 安全：`SUPABASE_SERVICE_ROLE_KEY` 只在服务端使用；客户端仅用 anon key。

# 文件级功能与逻辑清单（frontend/）

说明：以下根据当前代码库逐一列出 `frontend/` 根目录与各子目录的文件职责、核心逻辑与注意事项，便于快速导航与协作。

## 根目录文件

- `package.json`：包管理与脚本。依赖包含 `next@15`、`react@19`、`@supabase/supabase-js`、`stripe`、`posthog-js` 等。
- `README.md`：模板说明、MCP 集成指引与项目结构。
- `.env.example` / `.env.local`：环境变量示例与本地配置。
- `next.config.{js,ts}`：Next.js 配置（如需要统一改造构建行为在此修改）。
- `postcss.config.mjs` / `tailwind.config.ts`：Tailwind 与 PostCSS 配置，含定制色板与主题扩展。
- `tsconfig.json` / `next-env.d.ts`：TS 编译与 Next 类型支持。
- `.eslintrc.json` / `eslint.config.mjs`：ESLint 配置。
- `settings.json`：本仓内编辑器相关设置。
- `initial_supabase_table_schema.sql`：Supabase 表/策略/触发器/视图初始化示例脚本。
- `.cursor/mcp.json.example`：MCP 工具配置示例。

## 目录：`config/`

- `config/api.ts`：统一 API 基址与端点常量，提供 `getApiUrl()`、`getWsUrl()` 辅助函数。依赖 `NEXT_PUBLIC_API_URL`、`NEXT_PUBLIC_WS_URL`。

## 目录：`utils/`

- `utils/supabase.ts`：浏览器侧 Supabase 客户端，使用 `NEXT_PUBLIC_SUPABASE_URL/ANON_KEY`。
- `utils/supabase-admin.ts`：服务端 Supabase 客户端，使用 `SUPABASE_SERVICE_ROLE_KEY`，仅可在 RSC/Route Handler 中引用。
- `utils/env.ts`：环境变量存在性与格式校验。
- `utils/posthog.ts`：PostHog 初始化（按 `NEXT_PUBLIC_POSTHOG_KEY` 启用）。
- `utils/analytics.ts`：Vercel/自定义埋点辅助。
- `utils/cors.ts`：API Route CORS 处理。

## 目录：`types/`

- `types/ValidateEntryTypes.ts`：校验相关类型定义。
- `types/stripe.d.ts`：Stripe 类型声明补充。
- `types/v_models.ts`：与数据库视图（如 `v_categories`/`v_subcategories`）相关的模型类型，供页面与组件复用。

## 目录：`contexts/`

- `contexts/AuthContext.tsx`：Auth 上下文，封装登录/注册/会话、`useAuth()` Hook，供页面（`/login`、`/profile` 等）调用。
- `contexts/ProtectedRoute.tsx`：客户端路由保护包装，用于 `app/layout.tsx` 中包裹页面内容。
- `contexts/PostHogContext.tsx`：PostHog Provider 封装（当前在布局中注释，可按需启用）。

## 目录：`hooks/`

- `hooks/useSubscription.ts`：读取并同步 Stripe 订阅状态（从 Supabase + Stripe），供 `/profile`、`/dashboard`、`/pay` 使用。
- `hooks/useTrialStatus.ts`：试用状态及结束时间读取，配合访问控制与 UI 提示。

## 目录：`components/`

- `TopBar.tsx`：顶部条 UI（全局布局显示）。
- `NavBar.tsx`：导航栏组件（全局布局显示）。
- `LoginForm.tsx`：登录/注册表单，支持邮箱密码与 Google 登录触发。
- `AccountManagement.tsx`：账户信息与基础设置管理。
- `ForgotPasswordModal.tsx`：找回密码弹窗。
- `StripeBuyButton.tsx`：封装 Stripe Buy Button 组件，依赖 `NEXT_PUBLIC_STRIPE_*`。
- `SubscriptionStatus.tsx`：订阅状态展示。
- `PricingSection.tsx`：定价区块。
- `TypewriterEffect.tsx`：打字机效果。
- `LoadingSpinner.tsx`：通用加载指示。
- `Delayed.tsx` / `DemoWidget.tsx` / `MetricCard.tsx` / `OnboardingTour.tsx` / `PostHogErrorBoundary.tsx` / `PostHogPageView.tsx` / `BuyMeCoffee.tsx` / `VideoModal.tsx`：辅助 UI 与演示组件。

## 目录：`public/`

- `*.svg`、`Google-Logo.png` 等静态资源。

## 目录：`app/`（路由与页面）

- `app/layout.tsx`：根布局（客户端组件）。挂载 `AuthProvider`、`ProtectedRoute`、`TopBar`、`NavBar`，引入全局样式与 `@vercel/analytics`。
- `app/globals.css`：全局样式（Tailwind 层、骨架屏样式、色板变量等）。
- `app/metadata.ts`：默认元信息。
- `app/page.tsx`：首页落地页（客户端交互、动画、锚点导航、CTA）。
- `app/RouteSkeletonFallback.tsx`：全局最小骨架屏组件。

### 认证与账户路由

- `app/login/page.tsx`：登录/注册页。使用 `LoginForm`，登录后跳转 `/dashboard`。
- `app/profile/page.tsx`：个人中心。显示订阅信息、同步 Stripe、支持取消/恢复订阅，试用态提示。
- `app/reset-password/page.tsx`：请求重置密码（根据邮箱参数自动触发发送重置邮件）。
- `app/update-password/page.tsx`：通过恢复链接设置会话后更新密码。
- `app/verify-email/page.tsx`：邮箱验证结果页（注册后引导）。
- `app/auth/callback/route.ts`：OAuth 回调处理（Supabase auth 回调）。

### 支付与订阅路由

- `app/pay/page.tsx`：购买页。若已有有效订阅则引导回 `/profile`，否则展示 `StripeBuyButton` 与订阅状态。
- `app/api/stripe/webhook/route.ts`：处理 Stripe Webhook（订阅/发票/支付等事件，长度较长，包含签名校验与事件分发）。
- `app/api/stripe/cancel/route.ts`：取消订阅。
- `app/api/stripe/reactivate/route.ts`：恢复订阅至周期末不取消。
- `app/api/stripe/sync/route.ts`：以服务端身份同步 Stripe ↔ Supabase 订阅状态。
- `app/api/stripe/test/route.ts`：测试端点（本地联调用途）。

### 用户 API 路由

- `app/api/user/delete/route.ts`：删除用户相关数据的 API（遵循 RLS 与服务端安全约束）。

### 类别与子类别

- `app/categories/page.tsx`：RSC 页面，含工具栏与 Suspense 包裹的列表骨架。
- `app/categories/CategoriesList.tsx`：服务端获取 `v_categories_slug`、`v_subcategories_slug`，规整为 map 后渲染分类卡片与子分类列表。
- `app/subcategories/[id]/page.tsx`：按 `id` 展示子分类详情或相关列表（占位/示例实现）。

### 其它页面

- `app/dashboard/page.tsx`：仪表盘页面。基于 `useAuth`、`useSubscription`、`useTrialStatus` 控制访问与展示，包含示例指标卡片和活动流。
- `app/lookup/page.tsx`：演示读取 `searchParams` 并展示查询参数 `url`。

## 运行机制与依赖交互要点补充

- 页面访问控制：
  - 全局在 `app/layout.tsx` 中以客户端组件包裹 `ProtectedRoute`，对所有页面统一施加登录/订阅/试用期检查。
  - 业务页面内部（如 `/dashboard`、`/profile`）进一步使用 `useSubscription()` 与 `useTrialStatus()` 做二次校验与提示。
- Supabase 访问：
  - RSC/Route Handler 使用 `utils/supabase-admin.ts` 进行服务端安全操作；客户端仅使用 `utils/supabase.ts`。
- Stripe：
  - 所有变更性操作在服务端 `app/api/stripe/*` 内完成；客户端通过 fetch 调用，避免暴露密钥。
- Suspense 与骨架屏：
  - 列表类页面（`/categories`）以 Suspense + 形似真实结构的骨架实现更平滑的加载体验。

## 后续可加的目录建议

- `repositories/`：统一封装对 `v_*` 视图与表的查询（分页、筛选、错误处理、类型转换）。
- `lib/`：通用的服务端工具与适配器，如 Stripe/Supabase 封装、缓存与重试策略等。

（本清单将随代码变更持续更新）
