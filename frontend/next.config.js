/** @type {import('next').NextConfig} */
const nextConfig = {
  // ... your existing config ...
  webpack: (config, { isServer }) => {
    config.ignoreWarnings = [
      { module: /node_modules\/punycode/ }
    ];
    return config;
  },
  // Ensure Docker/SAE runtime works with standalone build output
  output: 'standalone',
  // CI/CD: do not block builds on ESLint or TS errors (we'll fix them incrementally)
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
}

module.exports = nextConfig