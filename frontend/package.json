{"name": "you-can-build-anything", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "types:gen": "supabase gen types typescript --db-url \"$DATABASE_URL\" > ./types/supabase.ts"}, "dependencies": {"@ai-sdk/openai": "^1.3.24", "@ai-sdk/openai-compatible": "^1.0.15", "@ai-sdk/react": "^1.2.12", "@floating-ui/react": "^0.27.4", "@headlessui/react": "^2.2.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@radix-ui/react-use-controllable-state": "^1.2.2", "@stripe/mcp": "^0.2.1", "@stripe/stripe-js": "^5.5.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.47.10", "@types/stripe": "^8.0.416", "@vercel/analytics": "^1.4.1", "ai": "^5.0.39", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.2", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.4.3", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "nanoid": "^5.1.5", "next": "^15.5.2", "posthog-js": "^1.219.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-error-boundary": "^5.0.0", "react-icons": "^5.4.0", "react-intersection-observer": "^9.15.1", "react-scroll": "^1.9.2", "react-syntax-highlighter": "^15.6.6", "streamdown": "^1.2.0", "stripe": "^17.5.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tokenlens": "^1.2.1", "use-stick-to-bottom": "^1.1.1", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@supabase/mcp-server-supabase": "^0.3.4", "@types/lodash": "^4.17.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-scroll": "^1.8.10", "eslint": "^9", "eslint-config-next": "^15.5.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}