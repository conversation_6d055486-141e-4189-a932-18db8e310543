variable "region" {
  description = "Alibaba Cloud region, e.g. cn-hangzhou"
  type        = string
}

variable "namespace_id" {
  description = "SAE namespace ID, e.g. cn-hangzhou:prod"
  type        = string
}

variable "vpc_id" {
  description = "Target VPC ID"
  type        = string
}

variable "vswitch_id" {
  description = "Target VSwitch ID (subnet)"
  type        = string
}

variable "image_url" {
  description = "Container image URL in ACR"
  type        = string
}

variable "app_name" {
  description = "SAE application name"
  type        = string
}

variable "replicas" {
  description = "Replica count"
  type        = number
  default     = 1
}

variable "cpu" {
  description = "CPU in millicores (e.g. 500 = 0.5 vCPU, 1000 = 1 vCPU)"
  type        = number
  default     = 1000
}

variable "memory" {
  description = "Memory in MB (e.g. 2048)"
  type        = number
  default     = 2048
}

variable "container_port" {
  description = "Container HTTP port"
  type        = number
  default     = 3000
}

variable "timezone" {
  description = "Container timezone"
  type        = string
  default     = "Asia/Beijing"
}

variable "env_map" {
  description = "Environment variables for the container"
  type        = map(string)
  default     = {}
}
