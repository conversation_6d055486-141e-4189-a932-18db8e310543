# --- Fill these with your actual values before running terraform ---
region       = "cn-hangzhou"
namespace_id = "cn-hangzhou:prod"   # TODO: replace with your SAE namespace ID

vpc_id     = "vpc-bp1o3ibadjysqws6ozfxq"   # ali_hz_sae
vswitch_id = "vsw-bp1pg4dfr433rxqv5mo10"   # zone cn-hangzhou-e

app_name  = "tenyy-frontend"
image_url = "registry.cn-hangzhou.aliyuncs.com/<your_ns>/next-frontend:latest"  # TODO: replace

replicas = 1
cpu      = 1000   # 1 vCPU
memory   = 2048   # 2 GB

container_port = 3000
timezone       = "Asia/Beijing"

env_map = {
  NEXT_PUBLIC_SUPABASE_URL      = "https://YOUR-PROJECT.supabase.co"
  NEXT_PUBLIC_SUPABASE_ANON_KEY = "YOUR-ANON-KEY"
}
