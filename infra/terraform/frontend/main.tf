terraform {
  required_providers {
    alicloud = {
      source  = "aliyun/alicloud"
      version = ">= 1.215.0"
    }
  }
}

provider "alicloud" {
  region = var.region
}

# SAE 前端应用（使用已有的 VPC / vSwitch / SAE 命名空间）
resource "alicloud_sae_application" "frontend" {
  app_name        = var.app_name
  app_description = var.app_name
  namespace_id    = var.namespace_id

  package_type = "Image"
  image_url    = var.image_url

  vpc_id     = var.vpc_id
  vswitch_id = var.vswitch_id

  timezone = var.timezone
  replicas = var.replicas
  cpu      = var.cpu
  memory   = var.memory

  # 环境变量：将 map(string) 转换为 SAE 需要的 JSON 数组
  # 形如: [{"name":"KEY","value":"VAL"}, ...]
  envs = jsonencode([
    for k, v in var.env_map : {
      name  = k
      value = v
    }
  ])
}
