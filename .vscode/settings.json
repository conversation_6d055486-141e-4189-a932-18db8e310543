{"[typescript]": {"editor.defaultFormatter": "denoland.vscode-deno"}, "dbt.enableNewLineagePanel": true, "dbt.executablePath": "/Users/<USER>/dev/k8s/tenyy-dind/.venv/bin/dbt", "dbt.lineage.defaultExpansion": 1, "dbt.profilesDir": "~/.dbt", "dbt.projectDir": "data_models/tenyy_marts", "deno.enablePaths": ["supabase/functions"], "deno.lint": true, "deno.unstable": ["bare-node-builtins", "byonm", "sloppy-imports", "unsafe-proto", "webgpu", "broadcast-channel", "worker-options", "cron", "kv", "ffi", "fs", "http", "net"], "files.exclude": {"**/.DS_Store": true, "**/.git": true, "**/.hg": true, "**/.svn": true, "**/Thumbs.db": true, ".yoyo": true}, "kubernetes": {"autoRefresh": true, "context": "kind-tenyy", "kubeconfig": "~/.kube/config", "namespace": "tenyy", "showExplorer": true}, "peacock.color": "#b68a33", "peacock.remoteColor": "#1857a4", "workbench.colorCustomizations": {"activityBar.activeBackground": "#cea34e", "activityBar.background": "#cea34e", "activityBar.foreground": "#15202b", "activityBar.inactiveForeground": "#15202b99", "activityBarBadge.background": "#e6f8f2", "activityBarBadge.foreground": "#15202b", "commandCenter.border": "#15202b99", "sash.hoverBorder": "#cea34e", "statusBar.background": "#b68a33", "statusBar.foreground": "#15202b", "statusBarItem.hoverBackground": "#8e6c28", "statusBarItem.remoteBackground": "#b68a33", "statusBarItem.remoteForeground": "#15202b", "titleBar.activeBackground": "#b68a33", "titleBar.activeForeground": "#15202b", "titleBar.inactiveBackground": "#b68a3399", "titleBar.inactiveForeground": "#15202b99"}}