# Tên yy 前端登录后 App PRD（Tenyy 自主规范适配版）

---

## 1. 范围与目标
- 参考优秀行业范式的登录后信息架构与交互，但以 Tên yy 领域与术语为唯一准绳。
- 适配 Tên yy 领域：应用（Apps）、SDK、数据集、API、用量/账单等。
- 前端读取 public.api_* 只读视图；站内搜索对接 Meilisearch（索引 apps）。

## 2. 信息架构（IA）与导航
- 顶栏：Logo、全局搜索入口（⌘K）、用户头像菜单。
- 侧栏一级：Home、Search（Apps/SDKs）、App lists、Settings。
- 开发者分组：Webhooks、Datasets、API Keys。
- 右上角：Usage 概览与 Upgrade 按钮。

## 3. Home 页核心模块
- 面包屑：Home。
- 快速入口卡：Search Apps / Search SDKs / Look up an app / Enrich app list。
- Saved searches：无数据态与列表态。
- Recent searches：最近检索列表，带类型与时间戳。
- 通知面板（F8）：全局通知列表（空/有数据态）。

## 4. 搜索与列表
- 全局搜索（⌘K）弹窗：应用/SDK 关键词提示与跳转（支持 App、SDK 两类结果）。
- Apps 搜索：按技术栈（SDK）、国家/市场、下载量、评分、版本更新时间、类别等维度过滤。
- SDKs 搜索：按 SDK 名称、公司、包名前缀过滤，返回使用该 SDK 的 App 列表；支持再筛选与保存。
- App lists：命名列表、导入/导出、批量富集（Enrich）。

## 5. 设置与开发者
- Settings：Profile、API Key、Integrations、Affiliate Program、Team、Billing、Usage、Invoices。
- Developers：Webhooks、Datasets、API Keys 页面与 Tên yy 统一的表格与详情布局风格。

## 6. 用量与升级
- Usage 卡片：Credits、API credits 进度条；点击跳 Usage 详情。
- Upgrade 按钮：跳转 Billing 购买页，默认 App 套餐 Tab。

## 7. 数据与契约
- 数据读取：统一从 public.api_* 视图层获取，避免直连 marts.*。
- 站内搜索：Meilisearch（索引 apps），首页与 Apps 搜索共用。
- 刷新策略：开发期手动刷新 mv_*，上线后引入调度。
