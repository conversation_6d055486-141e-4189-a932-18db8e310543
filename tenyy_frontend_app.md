# Tên yy 前端登录后 App PRD（Tenyy 自主规范适配版）

---

## 1. 范围与目标
- 参考优秀行业范式的登录后信息架构与交互，但以 Tên yy 领域与术语为唯一准绳。
- 适配 Tên yy 领域：应用（Apps）、SDK、数据集、API、用量/账单等。
- 前端读取 public.api_* 只读视图；站内搜索对接 Meilisearch（索引 apps）。

## 2. 信息架构（IA）与导航
- 顶栏：Logo、全局搜索入口（⌘K）、用户头像菜单。
- 侧栏一级：Home、Search（Apps/SDKs）、App lists、Settings。
- 右上角：Usage 概览与 Upgrade 按钮。

## 3. Home 页核心模块
- 快速入口卡：Search Apps / Search SDKs / Look up an app / Enrich app list。
  - Saved searches：无数据态与列表态。
  - Recent searches：最近检索列表，带类型与时间戳。
  - 通知面板（F8）：全局通知列表（空/有数据态）。

  ## 4. 搜索与列表
  - 全局搜索（⌘K）弹窗：应用/SDK 关键词提示与跳转（支持 App、SDK 两类结果）。
  - Apps 搜索：按技术栖（SDK）、国家/市场、下载量、评分、版本更新时间、类别等维度过滤。
  - SDKs 搜索：按 SDK 名称、公司、包名前缀过滤，返回使用该 SDK 的 App 列表；支持再筛选与保存。
  - App lists：命名列表、导入/导出、批量富集（Enrich）。

  ## 6. 功能细化说明（首批）
  - Search Apps：围绕应用主体提供高性能检索与聚合过滤，支持按技术栖（SDK）、国家/市场、类别、版本更新时间、评分与下载量段等多维组合筛选；列表支持卡片/表格、列选择、排序与分页；可将当前筛选保存为 Saved search，便于复用与分享；支持批量导出 CSV、加入 App lists 与埋点分析；点击行进入 App 详情（基础信息、历史版本、SDK 画像、权限与 so 库摘要）；数据契约统一来自 public.api_apps_* 只读视图，结合权限与速率限制确保稳定与可观测。
  - Search SDKs：以 SDK 名称、公司、包名前缀与别名为核心检索维度，支持模糊匹配与联想；结果页展示 SDK 档案（描述、站点、隐私政策链接、被使用 App 数等），并可一键切换到“使用该 SDK 的 Apps”列表，继承 Apps 搜索的过滤器（地域、类别、评分等）；支持保存筛选与导出集合，便于销售与研究场景复用；底层字段来自 public.api_sdks_* 视图，避免直接依赖计算层，便于架构演进与回溯审计。
  - Look up an app：输入包名或名称直达单 App 详情，若多命中则展示候选列表并提供去重与排序提示；详情包含应用图标、开发者、分发市场、分类、评分、下载量段、最近更新、版本时间线、集成 SDK 及相似 App 推荐；支持收藏、加入 App lists、复制包名；当本地索引缺失时可发起“请求补齐”，后台触发刷新并在通知中心回告完成；详情页提供外链安全跳转与错误态提示，确保可控与可追踪。
  - Enrich app list：支持上传或粘贴包名/名称列表，系统异步富集为结构化结果集（名称、分类、评分、下载量段、最近更新、SDK 摘要等），提供字段勾选、异常行标注（重复、无法匹配、下架）与分页预览；任务可命名保存并在 App Lists 管理重跑/追加；显示 Credits 耗用与实时进度；导出遵循字段白名单与阈值限制；所有取数经由 public.api_* 视图，保障字段稳定、权限一致与审计合规。
  - Saved searches：保存任意搜索页的筛选表达式、排序与视图配置，支持命名、标签与团队共享；列表展示最近运行时间、命中数量与快捷入口；可一键运行、复制为新搜索、导出当前结果，并可订阅新增命中提醒（站内通知/邮件）；保存仅记录参数不缓存结果，确保每次执行都基于最新数据；提供权限粒度（私有/团队可见）与变更日志，便于协作与治理。
