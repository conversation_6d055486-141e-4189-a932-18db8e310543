# Tênyy - 自动化 APK 分析与 SDK 识别平台

---

## 目录

1.  [项目概述](#项目概述)
2.  [核心架构与设计哲学](#核心架构与设计哲学)
    -   [解耦的设计思想](#解耦的设计思想)
    -   [三大核心工作流](#三大核心工作流)
    -   [“一次学习，处处生效”的进化能力](#一次学习处处生效的进化能力)
3.  [技术栈](#技术栈)
4.  [项目结构](#项目结构)
5.  [数据库设计](#数据库设计)
    -   [核心数据模型](#核心数据模型)
    -   [实体关系图 (ERD)](#实体关系图-erd)
6.  [配置管理](#配置管理)
7.  [部署与运维](#部署与运维)
    -   [Docker 多阶段构建](#docker-多阶段构建)
    -   [Docker Swarm 微服务架构](#docker-swarm-微服务架构)
    -   [部署步骤](#部署步骤)
    -   [数据备份与恢复](#数据备份与恢复)
8.  [核心工作流详解](#核心工作流详解)
    -   [阶段一：数据采集 (Crawler Flow)](#阶段一数据采集-crawler-flow)
    -   [阶段二：APK 下载与初步分析 (Download-Extract Flow)](#阶段二apk-下载与初步分析-download-extract-flow)
    -   [阶段三：包名提纯与特征提取 (APK Analysis Flow)](#阶段三包名提纯与特征提取-apk-analysis-flow)
    -   [阶段四：SDK 匹配 (SDK Matching Flow)](#阶段四sdk-匹配-sdk-matching-flow)
    -   [阶段五：知识库自学习 (Knowledge Base Enrichment Flow)](#阶段五知识库自学习-knowledge-base-enrichment-flow)
    -   [进化闭环](#进化闭环)
9.  [如何使用](#如何使用)

---

## 项目概述

**Tênyy** 是一个高度自动化、可扩展、具备自学习能力的 **APK 分析与第三方 SDK 识别平台**。它旨在解决移动应用安全和隐私合规领域中的一个核心痛点：准确、高效、可持续地识别 Android 应用中集成的所有第三方软件开发工具包（SDK）。

平台通过一套精心设计的、由工作流引擎驱动的自动化管道，实现了从应用市场数据采集、APK 下载分析，到核心特征提取、SDK 智能匹配的全流程自动化。其最独特之处在于其 **“数据与匹配分离”** 的架构，以及利用大语言模型（LLM）进行 **“知识库自学习”** 的能力，这使得系统能够随着时间的推移不断进化，提升识别的广度和精度。

### 核心特性

*   **高度自动化**: 所有流程均由 [Prefect](https://www.prefect.io/) 工作流引擎调度，无需人工干预。
*   **可扩展性**: 基于 Docker Swarm 的微服务架构，可以轻松地横向扩展各个组件（如爬虫、分析器）以应对海量数据处理需求。
*   **高效率**: 核心匹配逻辑在数据库层面完成，效率极高。下载、解压等耗时操作被设计为独立的、可并发执行的任务。
*   **可回溯性**: 所有分析结果和中间数据均持久化存储，方便对历史数据进行重新分析和审计。
*   **自学习与进化**: 平台能够自动发现未知的 SDK 线索，并借助 AI 进行分析，持续扩充和完善核心的 SDK 知识库。

---

## 核心架构与设计哲学

本项目的灵魂在于其 **“将数据采集与数据匹配彻底解耦”** 的核心设计哲学。传统的 SDK 识别方案通常将“分析一个 APK”和“识别其 SDK”绑定在一起，当发现新的 SDK 特征时，需要重新分析所有历史 APK 文件，成本极高。

Tênyy 通过引入专门的中间数据表和优化的工作流，彻底解决了这个问题。

### 解耦的设计思想

系统将整个流程拆分为四个独立的、可异步执行的阶段：

1.  **数据采集**: 从应用市场抓取 App 的元数据和下载链接。
2.  **APK 初步分析**: 下载 APK，提取其基础信息（如 `AndroidManifest.xml` 和所有 Java 包名）。
3.  **包名提纯**: 使用智能算法从海量的 Java 包名中，过滤并提纯出高质量的“候选包名”。
4.  **SDK 匹配**: 将“候选包名”与 SDK 知识库进行高效匹配，得出最终结果。

这种分离设计确保了各个阶段都可以独立优化和扩展，并且实现了最终的“进化能力”。

### 核心工作流（推荐与兼容）

整个平台的业务逻辑由两个主要的 Prefect 工作流驱动：

1.  **`combine_analysis_flow`（提取+匹配合流，推荐/唯一保留）**：当前生产/推荐流程。第一步在内存中完成提取与过滤，第二步直接消费内存数据完成 SDK 匹配，并将结果写入 `class_app_version_sdks`。

2.  **`knowledge_base_enrichment_flow` (知识库增强流)**: 这是平台的“大脑”和自学习引擎。它会周期性地分析未被成功匹配的特征线索（来源于分析日志与结果对照），利用大语言模型（LLM）进行分析，并将分析结果提交给运营人员审核，以持续扩充和增强核心知识库。

> 说明：原先的分离式 `apk_analysis_flow` + `sdk_matching_flow` 已废弃并移除；`class_app_discovered_packages` 表已通过 Alembic 迁移删除（见下文）。

### “一次学习，处处生效”的进化能力

这是本平台架构的最大优势。当一个新的 SDK 特征（例如，一个新的包名前缀）通过人工或 AI 的方式被添加到知识库后，我们 **无需重新下载和分析任何历史 APK 文件**。只需重新触发高效的 `sdk_matching_flow`，新知识就能立刻应用于所有存量数据，实现对历史数据的全面回溯和重新识别。这大大降低了维护成本，并确保了分析结果的持续准确性。

---

## 技术栈

| 类别             | 技术/工具                                                    | 描述                                                           |
| ---------------- | ------------------------------------------------------------ | -------------------------------------------------------------- |
| **后端语言**     | Python 3.10+                                                 | 项目主要开发语言                                               |
| **工作流引擎**   | Prefect 2.x                                                  | 负责所有自动化任务的调度、监控和执行                           |
| **数据库**       | PostgreSQL 14+                                               | 主业务数据库和 Prefect 元数据存储                              |
| **ORM**          | SQLAlchemy                                                   | Python 数据库工具包，用于与 PostgreSQL 交互                    |
| **数据库迁移**   | Alembic                                                      | 管理数据库 Schema 的版本演进                                   |
| **容器化**       | Docker, Docker Swarm                                         | 应用的打包、部署和微服务编排                                   |
| **下载器**       | Aria2                                                        | 高性能的命令行下载工具，用于高速下载 APK 文件                  |
| **AI & LLM**     | Google Gemini, DeepSeek                                      | 用于知识库的自学习和增强                                       |
| **Web 框架**     | FastAPI (via Prefect UI)                                     | Prefect Server 提供基于 FastAPI 的 API 和管理界面              |

---

## 项目结构

```
.
├── alembic/                  # Alembic 数据库迁移脚本
│   └── versions/
├── tenyy/
│   ├── config/               # 项目配置文件
│   │   ├── base.py           # 基础配置
│   │   ├── production.py     # 生产环境配置
│   │   └── settings.py       # 配置加载逻辑
│   ├── container/            # 容器与部署相关文件
│   │   ├── Dockerfile        # 项目主 Dockerfile
│   │   └── docker-swarm-compose.yml # Docker Swarm 部署编排文件
│   └── src/                  # 核心源代码
│       ├── apkinfo_analysis/ # 核心：APK 分析与 SDK 识别逻辑
│       ├── backup_plan/      # 数据库备份方案
│       ├── common/           # 通用工具模块（如数据库写入、日志）
│       ├── crawler/          # 各大应用市场的爬虫实现
│       ├── download_extract/ # APK 下载与初步分析（解压、提取包名）
│       └── models/           # SQLAlchemy 数据库模型定义
├── .env.prod                 # 生产环境变量文件（需自行创建）
├── Makefile                  # 常用命令快捷方式
├── alembic.ini               # Alembic 配置文件
└── requirements.txt          # Python 依赖列表
```

---

## 数据库设计

数据库是整个平台的核心，其设计反映了业务流程的需要。

### 核心数据模型

1.  **App -> StoreApp -> AppVersion (三级核心模型)**
    *   `apps`: 全局唯一的应用表，以包名 `id` (e.g., `com.example.app`) 为主键，存储应用的基本信息。
    *   `store_apps`: 应用市场关联表。同一个 `App` 可能在多个应用市场上架，此表记录了其在特定市场的 ID 和名称。
    *   `app_versions`: 应用版本表，以 APK 文件的 `apk_hash` (MD5) 为唯一标识。这是所有分析数据的核心载体，存储了下载链接、版本号、以及初步分析的结果（如 `android_manifest`, `packages_class`）。

2.  **SDK 知识库与匹配结果**
    *   `class_sdk_knowledge_base`: **SDK 知识库**。当前实现采用 `id` 整型自增为主键，`package_prefix` 为唯一索引。并包含 `sdk_name`, `company_name`, `version`, `subcategory_id`, `description`, `brief_message`, `official_web`, `sdk_document_url`, `privacy_policy_url`, `compliance_instructions_url`, `status`, `last_checked_at`，以及用于匹配策略的字段：`detection_type`, `is_regex_rule`, `regex_pattern`, `tags`。
    *   `class_app_version_sdks`: **最终的 SDK 识别结果表**。主键为组合键 `(app_version_id, sdk_package_prefix)`；另外存在辅助列 `id` (BigInt, 自增) 便于内部引用。其余字段包含 `sdk_knowledge_base_id`（外键关联 `class_sdk_knowledge_base.id`）, `match_type`, `is_potential`, `signal_count`, `child_packages`，以及审计字段（如 `created_at`, `updated_at`）。
    
    > 注：历史上的 `class_app_discovered_packages`（候选包名中间表）已删除，不再出现在生产流程中。

### 实体关系图 (ERD)

```mermaid
erDiagram
    apps {
        string id PK "包名"
        string name
        string description
        string category
    }

    store_apps {
        int id PK
        string app_id FK "关联 apps.id"
        string store_type "应用市场类型"
        string store_id "在该市场的ID"
    }

    app_versions {
        int id PK
        int store_app_id FK "关联 store_apps.id"
        string version
        string download_url
        jsonb packages_class "提取的原始包名列表"
        text android_manifest "AndroidManifest.xml内容"
        jsonb analysis_result "分析状态标记"
    }

    class_sdk_knowledge_base {
        int id PK
        string package_prefix UNIQUE "SDK包名前缀 (核心匹配字段)"
        string sdk_name
        string company_name
        int detection_type
        bool is_regex_rule
        text regex_pattern
        text tags
    }

    class_app_version_sdks {
        int app_version_id PK, FK "关联 app_versions.id"
        string sdk_package_prefix PK "SDK包名前缀"
        bigint id "辅助自增列（非主键）"
        int sdk_knowledge_base_id FK "关联 class_sdk_knowledge_base.id"
        string match_type "匹配类型（prefix/regex等）"
        bool is_potential "是否潜在SDK"
        int signal_count "信号计数"
        text child_packages "下级包列表(JSON)"
    }

    apps ||--o{ store_apps : "has"
    store_apps ||--o{ app_versions : "has"
    app_versions ||--o{ class_app_version_sdks : "contains"
    class_sdk_knowledge_base ||--o{ class_app_version_sdks : "is_identified_by"
```

---

## 配置管理

项目的配置系统位于 `tenyy/config/`，采用了分层设计，清晰且易于管理。

*   `base.py`: 定义了所有环境共享的基础配置，如数据库表名、日志格式、资源限制、默认参数等。
*   `production.py`: 定义了生产环境特有的配置，它会覆盖 `base.py` 中的同名设置。例如，生产环境的数据库地址、日志级别等。
*   `settings.py`: 负责加载配置。它会首先加载 `base.py`，然后根据环境变量（如 `ENV=production`）加载对应的环境配置，最终生成一个全局可用的 `settings` 对象。
*   `.env.prod`: **此文件需要手动创建**，用于存储敏感信息，如数据库密码、AI API Keys 等。`docker-swarm-compose.yml` 文件会从此文件加载环境变量并注入到相应的服务容器中。

---

## 部署与运维

平台被设计为一套基于 Docker Swarm 的微服务，易于部署和扩展。

### Docker 多阶段构建

`tenyy/container/Dockerfile` 采用了多阶段构建（multi-stage build）的最佳实践：
1.  **Builder 阶段**: 在一个包含完整编译工具链的镜像中，安装所有 Python 依赖并构建虚拟环境。
2.  **Runtime 阶段**: 将上一阶段生成的虚拟环境和应用代码，复制到一个干净、轻量的 Python 运行时镜像中。

这种方式显著减小了最终生产镜像的体积，并减少了潜在的安全风险。

### Docker Swarm 微服务架构

`tenyy/container/docker-swarm-compose.yml` 定义了整个服务栈，包括：
*   `app-db`: 应用主数据库 (PostgreSQL)。
*   `prefect-db`: Prefect 自己的元数据数据库 (PostgreSQL)。
*   `prefect-server`: Prefect 的核心服务，提供 UI 和 API。
*   `prefect-worker`: Prefect 的工作程序。它会监听 `prefect-server` 的指令，并以 **按需创建 Docker 容器** 的方式来执行具体的工作流任务（如爬虫、分析等）。
*   `docker-registry`: 一个私有的 Docker 镜像仓库，用于存放项目构建的 `tenyy-unified` 镜像，供 `prefect-worker` 拉取。
*   `aria2`: 高性能下载服务。

所有服务的网络、数据卷、资源限制、健康检查都已预先配置好，确保了生产环境的稳定性和数据安全。

### 部署步骤

1.  **环境准备**:
    *   安装 Docker 和 Docker Engine。
    *   初始化 Docker Swarm 集群: `docker swarm init`。
2.  **创建配置文件**:
    *   复制或创建 `.env.prod` 文件，并填入所有必要的环境变量（数据库密码、API Keys等）。
3.  **构建镜像**:
    *   在项目根目录运行 `make build`，这将构建 `tenyy-unified` 镜像。
4.  **推送镜像到私有仓库**:
    *   运行 `make push`，将镜像推送到本地运行的 `docker-registry` 服务中。
5.  **部署服务栈**:
    *   运行 `docker stack deploy -c tenyy/container/docker-swarm-compose.yml tenyy`。

### 数据备份与恢复

项目在 `tenyy/src/backup_plan/` 中提供了完善的数据库备份和恢复方案。
*   `database_backup_flow.py`: 一个 Prefect 工作流，可以配置为每日定时任务，自动执行数据库备份。
*   `pg_backup_production.sh`: 实际执行备份的 Shell 脚本，支持全量备份、保留策略等。
*   `restore_db.sh`: 用于从备份文件中恢复数据库的脚本。

---

## 核心工作流详解

这是平台自动化能力的体现，由 Prefect 统一调度。

### 阶段一：数据采集 (Crawler Flow)

1.  **触发**: 由 Prefect 定时或手动触发一个爬虫任务。
2.  **执行**: 任务在 `prefect-worker` 中执行，启动 `tenyy/src/crawler/containerized_crawler.py` 脚本。
3.  **数据采集**: 脚本根据参数动态加载相应的爬虫（如 `YinyongbaoCrawler`），从应用市场抓取 App 的元数据。
4.  **数据入库**: 调用 `tenyy/src/common/data_writer.py` 的 `save_app_data` 函数，将采集到的数据写入数据库 `apps`, `store_apps`, `app_versions` 表。新版本的 `download_status` 为 `pending`。

### 阶段二：APK 下载与初步分析 (Download-Extract Flow)

1.  **触发**: Prefect 监控到 `app_versions` 表中出现 `download_status` 为 `pending` 的新记录。
2.  **下载**: `download_extract` 流程启动，通过 `aria2` 服务高效下载 APK 文件。
3.  **初步分析**: 下载完成后，流程解压 APK，提取 `AndroidManifest.xml` 内容和所有 Java 包名列表。
4.  **数据更新**: 将提取的信息更新回 `app_versions` 表，并将 `analyze_status` 标记为 `done`。

### 阶段三：包名提纯与特征提取 (APK Analysis Flow)

1.  **触发**: Prefect 监控到 `app_versions` 表中出现 `analyze_status` 为 `done` 且尚未进行 SDK 分析的新记录。
2.  **执行**: `tenyy/src/apkinfo_analysis/apk_analysis_flow/apk_analysis_flow.py` 脚本被调用。
3.  **特征提取与过滤**: `AppDataExtractor` 类从记录中提取所有可用特征，并对 Java 包名进行严格的 **黑名单过滤** 和 **混淆代码过滤**。
4.  **数据处理**: 仅在内存中保留候选包名（不落库）。

### 阶段四：SDK 匹配 (SDK Matching Flow)

1.  **触发**: 直接使用内存中的候选包名进入匹配步骤。
2.  **核心匹配**: 在数据库层面完成与 `class_sdk_knowledge_base` 的高效匹配（合流模式中由流程内聚合并执行）。
3.  **结果入库**: 匹配成功后，在 `class_app_version_sdks` 表中创建记录，完成 SDK 识别。

### 阶段五：知识库自学习 (Knowledge Base Enrichment Flow)

1.  **触发**: 后台周期性运行的自学习流程。
2.  **发现未知**: 流程分析未匹配的特征线索（来自分析结果与日志），找出高频出现的未知包名/特征。
3.  **AI 分析**: 将这些包名提交给大语言模型（LLM），询问其功能、归属等信息。
4.  **知识增强**: LLM 的返回结果经人工审核后，可方便地添加为新的条目到 `class_sdk_knowledge_base` 中。

### 进化闭环

当新的 SDK 知识被添加到知识库后，只需重新运行第四阶段的 `sdk_matching_flow`，新知识就能立刻应用于历史上所有的存量数据，形成完美的自学习和进化闭环。

---

## 如何使用

1.  **访问 Prefect UI**: 部署成功后，通过 `http://<your-server-ip>:4200` 访问 Prefect 的 Web 界面。
2.  **查看工作流**: 在 "Flows" 页面，可以看到本项目部署的所有工作流。
3.  **手动触发**: 可以点击任意一个 Flow，进入其详情页，然后点击 "Run" 按钮手动触发一次执行。
4.  **配置定时任务**: 在 "Deployments" 页面，可以为每个工作流配置定时执行（Schedules），实现全自动化运行。
5.  **查看结果**:
    *   工作流的执行日志和状态可以在 Prefect UI 中实时查看。
    *   最终的分析结果可以直接查询 PostgreSQL 数据库中的 `class_app_version_sdks` 表。

---

## 重要说明：数据库结构的权威来源

*   `alembic/versions/` 下的迁移脚本与 `tenyy/src/models/` 下的 ORM 模型是数据库结构的权威来源。
*   `tenyy/src/models/schema.sql` 为早期参考文件，已不完全反映最新结构（例如：`class_sdk_knowledge_base` 采用 `id` 为主键且 `package_prefix` 唯一，`class_app_version_sdks` 含 `id`、`sdk_knowledge_base_id`、`is_potential` 等字段）。

> 变更：`class_app_discovered_packages` 已被移除（Alembic 迁移 `f0b1d2a3c4e5`），推荐与唯一保留的流程为 `combine_analysis_flow`。
