-- Create stable API views mapping to existing mv_* objects (initial wiring)
-- Note: Currently point to public.mv_* to keep behavior unchanged.
-- Later you can retarget to marts.mv_* when available.

-- 1) Ensure serving schema exists (for supabaseServing client)
create schema if not exists serving;
create schema if not exists marts;

-- 2) Public API views
DO $body$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_matviews
    WHERE schemaname = 'serving' AND matviewname = 'mv_categories_slug'
  ) THEN
    EXECUTE $v$CREATE OR REPLACE VIEW public.api_categories_slug AS
      SELECT * FROM serving.mv_categories_slug$v$;
  END IF;
END
$body$ LANGUAGE plpgsql;

DO $body$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_matviews
    WHERE schemaname = 'serving' AND matviewname = 'mv_subcategories_slug'
  ) THEN
    EXECUTE $v$CREATE OR REPLACE VIEW public.api_subcategories_slug AS
      SELECT * FROM serving.mv_subcategories_slug$v$;
  END IF;
END
$body$ LANGUAGE plpgsql;

DO $body$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_matviews
    WHERE schemaname = 'serving' AND matviewname = 'mv_sdks'
  ) THEN
    EXECUTE $v$CREATE OR REPLACE VIEW public.api_sdks AS
      SELECT * FROM serving.mv_sdks$v$;
  END IF;
END
$body$ LANGUAGE plpgsql;

DO $body$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_matviews
    WHERE schemaname = 'serving' AND matviewname = 'mv_sdks_lite'
  ) THEN
    EXECUTE $v$CREATE OR REPLACE VIEW public.api_sdks_lite AS
      SELECT * FROM serving.mv_sdks_lite$v$;
  END IF;
END
$body$ LANGUAGE plpgsql;

DO $body$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_matviews
    WHERE schemaname = 'serving' AND matviewname = 'mv_apps_by_sdk_with_downloads'
  ) THEN
    EXECUTE $v$CREATE OR REPLACE VIEW public.api_apps_by_sdk_with_downloads AS
      SELECT * FROM serving.mv_apps_by_sdk_with_downloads$v$;
  END IF;
END
$body$ LANGUAGE plpgsql;

DO $body$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_matviews
    WHERE schemaname = 'serving' AND matviewname = 'mv_app_sdk_market_share'
  ) THEN
    EXECUTE $v$CREATE OR REPLACE VIEW public.api_app_sdk_market_share AS
      SELECT * FROM serving.mv_app_sdk_market_share$v$;
  END IF;
END
$body$ LANGUAGE plpgsql;

DO $body$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_matviews
    WHERE schemaname = 'serving' AND matviewname = 'mv_app_sdks_detail'
  ) THEN
    EXECUTE $v$CREATE OR REPLACE VIEW public.api_app_sdks_detail AS
      SELECT * FROM serving.mv_app_sdks_detail$v$;
  END IF;
END
$body$ LANGUAGE plpgsql;

DO $body$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_matviews
    WHERE schemaname = 'serving' AND matviewname = 'mv_app_sdk_count_by_category'
  ) THEN
    EXECUTE $v$CREATE OR REPLACE VIEW public.api_app_sdk_count_by_category AS
      SELECT * FROM serving.mv_app_sdk_count_by_category$v$;
  END IF;
END
$body$ LANGUAGE plpgsql;

DO $body$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_matviews
    WHERE schemaname = 'serving' AND matviewname = 'mv_apps_with_detail'
  ) THEN
    EXECUTE $v$CREATE OR REPLACE VIEW public.api_apps_with_detail AS
      SELECT * FROM serving.mv_apps_with_detail$v$;
  END IF;
END
$body$ LANGUAGE plpgsql;

-- New: api_apps_by_sdk_with_icon -> serving.mv_apps_by_sdk_with_icon
DO $body$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_matviews
    WHERE schemaname = 'serving' AND matviewname = 'mv_apps_by_sdk_with_icon'
  ) THEN
    EXECUTE $v$CREATE OR REPLACE VIEW public.api_apps_by_sdk_with_icon AS
      SELECT * FROM serving.mv_apps_by_sdk_with_icon$v$;
  END IF;
END
$body$ LANGUAGE plpgsql;

-- 3) Serving schema wrappers (so supabaseServing db.schema='serving' can query api_* names)
DO $body$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_views
    WHERE schemaname = 'public' AND viewname = 'api_categories_slug'
  ) THEN
    EXECUTE $v$CREATE OR REPLACE VIEW serving.api_categories_slug AS
      SELECT * FROM public.api_categories_slug$v$;
  END IF;
END
$body$ LANGUAGE plpgsql;

DO $body$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_views
    WHERE schemaname = 'public' AND viewname = 'api_subcategories_slug'
  ) THEN
    EXECUTE $v$CREATE OR REPLACE VIEW serving.api_subcategories_slug AS
      SELECT * FROM public.api_subcategories_slug$v$;
  END IF;
END
$body$ LANGUAGE plpgsql;

-- (Add more wrappers later if you decide to query via serving schema elsewhere)

-- 4) Grants
-- Make public API views readable by anon/authenticated roles
grant usage on schema public to anon, authenticated;
DO $body$ BEGIN IF EXISTS (SELECT 1 FROM pg_views WHERE schemaname='public' AND viewname='api_categories_slug') THEN EXECUTE 'GRANT SELECT ON public.api_categories_slug TO anon, authenticated'; END IF; END $body$ LANGUAGE plpgsql;
DO $body$ BEGIN IF EXISTS (SELECT 1 FROM pg_views WHERE schemaname='public' AND viewname='api_subcategories_slug') THEN EXECUTE 'GRANT SELECT ON public.api_subcategories_slug TO anon, authenticated'; END IF; END $body$ LANGUAGE plpgsql;
DO $body$ BEGIN IF EXISTS (SELECT 1 FROM pg_views WHERE schemaname='public' AND viewname='api_sdks') THEN EXECUTE 'GRANT SELECT ON public.api_sdks TO anon, authenticated'; END IF; END $body$ LANGUAGE plpgsql;
DO $body$ BEGIN IF EXISTS (SELECT 1 FROM pg_views WHERE schemaname='public' AND viewname='api_sdks_lite') THEN EXECUTE 'GRANT SELECT ON public.api_sdks_lite TO anon, authenticated'; END IF; END $body$ LANGUAGE plpgsql;
DO $body$ BEGIN IF EXISTS (SELECT 1 FROM pg_views WHERE schemaname='public' AND viewname='api_apps_by_sdk_with_downloads') THEN EXECUTE 'GRANT SELECT ON public.api_apps_by_sdk_with_downloads TO anon, authenticated'; END IF; END $body$ LANGUAGE plpgsql;
DO $body$ BEGIN IF EXISTS (SELECT 1 FROM pg_views WHERE schemaname='public' AND viewname='api_app_sdk_market_share') THEN EXECUTE 'GRANT SELECT ON public.api_app_sdk_market_share TO anon, authenticated'; END IF; END $body$ LANGUAGE plpgsql;
DO $body$ BEGIN IF EXISTS (SELECT 1 FROM pg_views WHERE schemaname='public' AND viewname='api_app_sdks_detail') THEN EXECUTE 'GRANT SELECT ON public.api_app_sdks_detail TO anon, authenticated'; END IF; END $body$ LANGUAGE plpgsql;
DO $body$ BEGIN IF EXISTS (SELECT 1 FROM pg_views WHERE schemaname='public' AND viewname='api_app_sdk_count_by_category') THEN EXECUTE 'GRANT SELECT ON public.api_app_sdk_count_by_category TO anon, authenticated'; END IF; END $body$ LANGUAGE plpgsql;
DO $body$ BEGIN IF EXISTS (SELECT 1 FROM pg_views WHERE schemaname='public' AND viewname='api_apps_with_detail') THEN EXECUTE 'GRANT SELECT ON public.api_apps_with_detail TO anon, authenticated'; END IF; END $body$ LANGUAGE plpgsql;
DO $body$ BEGIN IF EXISTS (SELECT 1 FROM pg_views WHERE schemaname='public' AND viewname='api_apps_by_sdk_with_icon') THEN EXECUTE 'GRANT SELECT ON public.api_apps_by_sdk_with_icon TO anon, authenticated'; END IF; END $body$ LANGUAGE plpgsql;

-- Serving schema grants (optional but safe)
grant usage on schema serving to anon, authenticated;
DO $body$ BEGIN IF EXISTS (SELECT 1 FROM pg_views WHERE schemaname='serving' AND viewname='api_categories_slug') THEN EXECUTE 'GRANT SELECT ON serving.api_categories_slug TO anon, authenticated'; END IF; END $body$ LANGUAGE plpgsql;
DO $body$ BEGIN IF EXISTS (SELECT 1 FROM pg_views WHERE schemaname='serving' AND viewname='api_subcategories_slug') THEN EXECUTE 'GRANT SELECT ON serving.api_subcategories_slug TO anon, authenticated'; END IF; END $body$ LANGUAGE plpgsql;
