#!/usr/bin/env python3
"""
获取Prefect API URL的工具脚本
用于Makefile中动态获取配置，避免配置加载时的输出信息
"""

import sys
import os
import socket
from urllib.parse import urlparse

def _is_host_resolvable(url: str) -> bool:
    try:
        host = urlparse(url).hostname
        if not host:
            return False
        socket.gethostbyname(host)
        return True
    except Exception:
        return False


def get_prefect_api_url(env_name):
    """获取指定环境的Prefect API URL"""
    try:
        # 添加项目路径
        sys.path.insert(0, '.')
        sys.path.insert(0, './tenyy')
        
        # 临时禁用输出
        original_stdout = sys.stdout
        original_stderr = sys.stderr
        
        # 重定向输出到null
        with open(os.devnull, 'w') as devnull:
            sys.stdout = devnull
            sys.stderr = devnull
            
            try:
                from tenyy.config import get_config
                config = get_config(env_name)
                api_url = config.PREFECT_API_URL
            finally:
                # 恢复输出
                sys.stdout = original_stdout
                sys.stderr = original_stderr
        
        # 允许通过环境变量强制覆盖
        env_override = os.environ.get("PREFECT_API_URL")
        if env_override:
            return env_override

        # k8s 环境：若默认值不可解析，回退到本地 UI（常见于未配置本地域名的场景）
        if env_name == "k8s" and (not _is_host_resolvable(api_url)):
            return "http://localhost:4200/api"

        return api_url
        
    except Exception:
        # 允许环境变量覆盖
        env_override = os.environ.get("PREFECT_API_URL")
        if env_override:
            return env_override
        # 降级到默认配置（k8s 回退到 localhost，避免不可解析域名）
        if env_name == "production":
            return "http://*************:4200/api"
        elif env_name == "k8s":
            return "http://localhost:4200/api"
        else:
            return "http://localhost:4200/api"

if __name__ == "__main__":
    env_name = sys.argv[1] if len(sys.argv) > 1 else "local"
    api_url = get_prefect_api_url(env_name)
    print(api_url, end='')  # 不输出换行符
