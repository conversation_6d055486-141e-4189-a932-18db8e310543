# Tenyy Supabase 数据库增强功能

## 1. 基于 PRD 需求的功能增强

### 1.1 全局搜索（⌘K）增强

#### 搜索建议表
```sql
-- 搜索建议和自动完成表
CREATE TABLE public.search_suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    suggestion_text TEXT NOT NULL,
    suggestion_type TEXT NOT NULL, -- 'app', 'sdk', 'category', 'company'
    target_id TEXT, -- 对应的实体 ID
    search_count INTEGER DEFAULT 0, -- 被搜索次数
    last_searched_at TIMESTAMPTZ,
    
    -- 权重和排序
    popularity_score DECIMAL(5,2) DEFAULT 0,
    is_trending BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(suggestion_text, suggestion_type)
);

-- 创建索引
CREATE INDEX idx_search_suggestions_type ON public.search_suggestions (suggestion_type);
CREATE INDEX idx_search_suggestions_popularity ON public.search_suggestions (popularity_score DESC);
CREATE INDEX idx_search_suggestions_trending ON public.search_suggestions (is_trending, popularity_score DESC);

-- 全文搜索索引
CREATE INDEX idx_search_suggestions_text ON public.search_suggestions USING GIN (to_tsvector('english', suggestion_text));
```

#### 智能搜索建议函数
```sql
-- 获取搜索建议的函数
CREATE OR REPLACE FUNCTION public.get_search_suggestions(
    p_query TEXT,
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    suggestion TEXT,
    type TEXT,
    target_id TEXT,
    popularity_score DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    WITH ranked_suggestions AS (
        -- 精确匹配
        SELECT 
            s.suggestion_text,
            s.suggestion_type,
            s.target_id,
            s.popularity_score,
            1 as match_priority
        FROM public.search_suggestions s
        WHERE s.suggestion_text ILIKE p_query || '%'
        
        UNION ALL
        
        -- 模糊匹配
        SELECT 
            s.suggestion_text,
            s.suggestion_type,
            s.target_id,
            s.popularity_score,
            2 as match_priority
        FROM public.search_suggestions s
        WHERE s.suggestion_text ILIKE '%' || p_query || '%'
        AND NOT s.suggestion_text ILIKE p_query || '%'
        
        UNION ALL
        
        -- 全文搜索匹配
        SELECT 
            s.suggestion_text,
            s.suggestion_type,
            s.target_id,
            s.popularity_score,
            3 as match_priority
        FROM public.search_suggestions s
        WHERE to_tsvector('english', s.suggestion_text) @@ plainto_tsquery('english', p_query)
        AND NOT s.suggestion_text ILIKE '%' || p_query || '%'
    )
    SELECT 
        rs.suggestion_text,
        rs.suggestion_type,
        rs.target_id,
        rs.popularity_score
    FROM ranked_suggestions rs
    ORDER BY rs.match_priority, rs.popularity_score DESC, rs.suggestion_text
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 1.2 应用富集（Enrich）功能

#### 应用富集任务表
```sql
-- 应用富集任务管理表
CREATE TABLE public.app_enrichment_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    list_id UUID REFERENCES public.app_lists(id) ON DELETE CASCADE,
    
    -- 任务信息
    task_name TEXT NOT NULL,
    app_ids JSONB NOT NULL, -- 需要富集的应用 ID 列表
    enrichment_fields JSONB DEFAULT '[]', -- 需要富集的字段
    
    -- 任务状态
    status TEXT DEFAULT 'pending', -- pending, processing, completed, failed
    progress INTEGER DEFAULT 0, -- 0-100
    total_apps INTEGER NOT NULL,
    processed_apps INTEGER DEFAULT 0,
    failed_apps INTEGER DEFAULT 0,
    
    -- 结果信息
    result_data JSONB DEFAULT '{}',
    error_message TEXT,
    
    -- 时间信息
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_enrichment_tasks_user ON public.app_enrichment_tasks (user_id);
CREATE INDEX idx_enrichment_tasks_status ON public.app_enrichment_tasks (status);
CREATE INDEX idx_enrichment_tasks_created ON public.app_enrichment_tasks (created_at DESC);

-- RLS 策略
ALTER TABLE public.app_enrichment_tasks ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own enrichment tasks" ON public.app_enrichment_tasks
    FOR ALL USING (user_id = auth.uid());
```

#### 富集数据缓存表
```sql
-- 应用富集数据缓存表
CREATE TABLE public.app_enrichment_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    app_id TEXT REFERENCES public.apps(id) ON DELETE CASCADE,
    
    -- 富集数据
    enrichment_type TEXT NOT NULL, -- 'basic_info', 'sdk_analysis', 'market_data', 'competitor_analysis'
    enrichment_data JSONB NOT NULL,
    
    -- 数据质量
    confidence_score DECIMAL(3,2) DEFAULT 1.00,
    data_source TEXT, -- 数据来源
    
    -- 缓存管理
    expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days'),
    is_stale BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(app_id, enrichment_type)
);

-- 创建索引
CREATE INDEX idx_enrichment_cache_app ON public.app_enrichment_cache (app_id);
CREATE INDEX idx_enrichment_cache_type ON public.app_enrichment_cache (enrichment_type);
CREATE INDEX idx_enrichment_cache_expires ON public.app_enrichment_cache (expires_at);
```

### 1.3 通知系统（F8 面板）

#### 用户通知表
```sql
-- 用户通知表
CREATE TABLE public.user_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- 通知内容
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    notification_type TEXT NOT NULL, -- 'info', 'success', 'warning', 'error', 'update'
    
    -- 关联数据
    related_entity_type TEXT, -- 'app', 'sdk', 'list', 'search', 'system'
    related_entity_id TEXT,
    action_url TEXT, -- 点击通知后跳转的 URL
    
    -- 状态
    is_read BOOLEAN DEFAULT FALSE,
    is_archived BOOLEAN DEFAULT FALSE,
    priority INTEGER DEFAULT 0, -- 0=normal, 1=high, 2=urgent
    
    -- 时间信息
    expires_at TIMESTAMPTZ, -- 通知过期时间
    read_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_notifications_user ON public.user_notifications (user_id);
CREATE INDEX idx_notifications_unread ON public.user_notifications (user_id, is_read, created_at DESC);
CREATE INDEX idx_notifications_type ON public.user_notifications (notification_type);
CREATE INDEX idx_notifications_priority ON public.user_notifications (priority DESC, created_at DESC);

-- RLS 策略
ALTER TABLE public.user_notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own notifications" ON public.user_notifications
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update own notifications" ON public.user_notifications
    FOR UPDATE USING (user_id = auth.uid());
```

#### 通知模板表
```sql
-- 通知模板表
CREATE TABLE public.notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_key TEXT NOT NULL UNIQUE,
    
    -- 模板内容
    title_template TEXT NOT NULL,
    message_template TEXT NOT NULL,
    notification_type TEXT NOT NULL,
    
    -- 模板变量说明
    variables JSONB DEFAULT '[]', -- 模板中可用的变量列表
    
    -- 配置
    is_active BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 插入基础通知模板
INSERT INTO public.notification_templates (template_key, title_template, message_template, notification_type, variables) VALUES
('enrichment_completed', '应用富集完成', '您的应用列表 "{{list_name}}" 富集任务已完成，共处理 {{total_apps}} 个应用。', 'success', '["list_name", "total_apps"]'),
('enrichment_failed', '应用富集失败', '您的应用列表 "{{list_name}}" 富集任务失败：{{error_message}}', 'error', '["list_name", "error_message"]'),
('quota_warning', '配额使用警告', '您的 {{quota_type}} 配额已使用 {{usage_percent}}%，请注意合理使用。', 'warning', '["quota_type", "usage_percent"]'),
('quota_exceeded', '配额已超限', '您的 {{quota_type}} 配额已用完，请升级套餐或等待下月重置。', 'error', '["quota_type"]'),
('new_app_detected', '发现新应用', '在您关注的 SDK "{{sdk_name}}" 中发现了 {{app_count}} 个新应用。', 'info', '["sdk_name", "app_count"]');
```

### 1.4 使用统计和分析

#### 用户行为跟踪表
```sql
-- 用户行为跟踪表
CREATE TABLE public.user_activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- 行为信息
    activity_type TEXT NOT NULL, -- 'search', 'view_app', 'view_sdk', 'create_list', 'export_data', etc.
    activity_details JSONB DEFAULT '{}',
    
    -- 上下文信息
    page_url TEXT,
    user_agent TEXT,
    ip_address INET,
    session_id TEXT,
    
    -- 性能数据
    response_time_ms INTEGER,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_activity_logs_user ON public.user_activity_logs (user_id);
CREATE INDEX idx_activity_logs_type ON public.user_activity_logs (activity_type);
CREATE INDEX idx_activity_logs_created ON public.user_activity_logs (created_at DESC);
CREATE INDEX idx_activity_logs_session ON public.user_activity_logs (session_id);

-- RLS 策略
ALTER TABLE public.user_activity_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own activity logs" ON public.user_activity_logs
    FOR SELECT USING (user_id = auth.uid());
```

#### 使用统计视图
```sql
-- 用户使用统计物化视图
CREATE MATERIALIZED VIEW serving.mv_user_usage_stats AS
SELECT 
    u.id as user_id,
    u.email,
    u.created_at as user_created_at,
    
    -- 基础统计
    COUNT(DISTINCT al.id) as total_activities,
    COUNT(DISTINCT DATE(al.created_at)) as active_days,
    COUNT(DISTINCT al.session_id) as total_sessions,
    
    -- 搜索统计
    COUNT(DISTINCT al.id) FILTER (WHERE al.activity_type = 'search') as search_count,
    COUNT(DISTINCT al.activity_details->>'query') FILTER (WHERE al.activity_type = 'search') as unique_searches,
    
    -- 应用查看统计
    COUNT(DISTINCT al.id) FILTER (WHERE al.activity_type = 'view_app') as app_views,
    COUNT(DISTINCT al.activity_details->>'app_id') FILTER (WHERE al.activity_type = 'view_app') as unique_apps_viewed,
    
    -- 列表统计
    COUNT(DISTINCT al.id) FILTER (WHERE al.activity_type = 'create_list') as lists_created,
    COUNT(DISTINCT al.id) FILTER (WHERE al.activity_type = 'export_data') as exports_made,
    
    -- 时间统计
    MIN(al.created_at) as first_activity_at,
    MAX(al.created_at) as last_activity_at,
    
    -- 平均性能
    AVG(al.response_time_ms) as avg_response_time_ms
    
FROM public.users u
LEFT JOIN public.user_activity_logs al ON u.id = al.user_id
WHERE u.is_deleted = FALSE
GROUP BY u.id, u.email, u.created_at;

-- 创建索引
CREATE INDEX idx_mv_user_usage_stats_user ON serving.mv_user_usage_stats (user_id);
CREATE INDEX idx_mv_user_usage_stats_activities ON serving.mv_user_usage_stats (total_activities DESC);
CREATE INDEX idx_mv_user_usage_stats_last_activity ON serving.mv_user_usage_stats (last_activity_at DESC);
```

## 2. Meilisearch 集成优化

### 2.1 搜索索引管理表
```sql
-- Meilisearch 索引管理表
CREATE TABLE public.search_indexes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    index_name TEXT NOT NULL UNIQUE,
    index_type TEXT NOT NULL, -- 'apps', 'sdks', 'categories'

    -- 索引配置
    searchable_attributes JSONB DEFAULT '[]',
    filterable_attributes JSONB DEFAULT '[]',
    sortable_attributes JSONB DEFAULT '[]',
    ranking_rules JSONB DEFAULT '[]',

    -- 同步状态
    last_sync_at TIMESTAMPTZ,
    sync_status TEXT DEFAULT 'pending', -- pending, syncing, completed, failed
    total_documents INTEGER DEFAULT 0,
    sync_error TEXT,

    -- 配置
    is_active BOOLEAN DEFAULT TRUE,
    auto_sync BOOLEAN DEFAULT TRUE,
    sync_interval_minutes INTEGER DEFAULT 60,

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 插入基础索引配置
INSERT INTO public.search_indexes (index_name, index_type, searchable_attributes, filterable_attributes, sortable_attributes, ranking_rules) VALUES
('apps', 'apps',
 '["name", "description", "developer", "category"]',
 '["category", "is_game", "is_free", "rating", "download_count"]',
 '["rating", "download_count", "created_at"]',
 '["words", "typo", "proximity", "attribute", "sort", "exactness"]'),
('sdks', 'sdks',
 '["name", "description", "company", "category"]',
 '["category", "company", "app_count"]',
 '["app_count", "popularity_score", "created_at"]',
 '["words", "typo", "proximity", "attribute", "sort", "exactness"]');
```

### 2.2 搜索分析功能
```sql
-- 搜索分析表
CREATE TABLE public.search_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- 搜索信息
    search_query TEXT NOT NULL,
    search_type TEXT NOT NULL, -- 'apps', 'sdks', 'global'
    filters_applied JSONB DEFAULT '{}',

    -- 结果信息
    result_count INTEGER DEFAULT 0,
    clicked_result_id TEXT, -- 用户点击的结果 ID
    clicked_position INTEGER, -- 点击结果在列表中的位置

    -- 用户信息
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    session_id TEXT,

    -- 性能信息
    search_time_ms INTEGER,

    -- 上下文信息
    page_url TEXT,
    user_agent TEXT,

    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_search_analytics_query ON public.search_analytics (search_query);
CREATE INDEX idx_search_analytics_type ON public.search_analytics (search_type);
CREATE INDEX idx_search_analytics_user ON public.search_analytics (user_id);
CREATE INDEX idx_search_analytics_created ON public.search_analytics (created_at DESC);
```

### 2.3 热门搜索统计视图
```sql
-- 热门搜索统计视图
CREATE MATERIALIZED VIEW serving.mv_popular_searches AS
SELECT
    search_query,
    search_type,
    COUNT(*) as search_count,
    COUNT(DISTINCT user_id) as unique_users,
    AVG(result_count) as avg_result_count,
    COUNT(*) FILTER (WHERE clicked_result_id IS NOT NULL) as click_count,
    CASE
        WHEN COUNT(*) > 0 THEN
            COUNT(*) FILTER (WHERE clicked_result_id IS NOT NULL)::FLOAT / COUNT(*)
        ELSE 0
    END as click_through_rate,

    -- 时间统计
    MIN(created_at) as first_searched_at,
    MAX(created_at) as last_searched_at,

    -- 趋势分析
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as searches_last_7_days,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as searches_last_30_days

FROM public.search_analytics
WHERE search_query IS NOT NULL
AND LENGTH(search_query) >= 2
GROUP BY search_query, search_type
HAVING COUNT(*) >= 5 -- 至少被搜索 5 次
ORDER BY search_count DESC;

-- 创建索引
CREATE INDEX idx_mv_popular_searches_count ON serving.mv_popular_searches (search_count DESC);
CREATE INDEX idx_mv_popular_searches_ctr ON serving.mv_popular_searches (click_through_rate DESC);
CREATE INDEX idx_mv_popular_searches_recent ON serving.mv_popular_searches (searches_last_7_days DESC);
```

## 3. 高级分析功能

### 3.1 应用趋势分析表
```sql
-- 应用趋势分析表
CREATE TABLE public.app_trends (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    app_id TEXT REFERENCES public.apps(id) ON DELETE CASCADE,

    -- 趋势数据
    metric_type TEXT NOT NULL, -- 'download_count', 'rating', 'sdk_count', 'search_volume'
    metric_value DECIMAL(15,2) NOT NULL,
    previous_value DECIMAL(15,2),
    change_percentage DECIMAL(5,2),

    -- 时间维度
    period_type TEXT NOT NULL, -- 'daily', 'weekly', 'monthly'
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,

    -- 排名信息
    rank_in_category INTEGER,
    rank_overall INTEGER,

    created_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(app_id, metric_type, period_type, period_start)
);

-- 创建索引
CREATE INDEX idx_app_trends_app ON public.app_trends (app_id);
CREATE INDEX idx_app_trends_metric ON public.app_trends (metric_type, period_type);
CREATE INDEX idx_app_trends_period ON public.app_trends (period_start DESC);
CREATE INDEX idx_app_trends_change ON public.app_trends (change_percentage DESC);
```

### 3.2 SDK 市场份额分析视图
```sql
-- SDK 市场份额分析视图
CREATE MATERIALIZED VIEW serving.mv_sdk_market_analysis AS
WITH sdk_stats AS (
    SELECT
        s.id,
        s.name,
        s.category,
        s.company,
        COUNT(DISTINCT asdk.app_id) as app_count,
        COUNT(DISTINCT a.category) as category_spread,
        AVG(a.rating) as avg_app_rating,
        SUM(a.download_count) as total_app_downloads,

        -- 计算市场份额
        COUNT(DISTINCT asdk.app_id)::FLOAT /
        (SELECT COUNT(DISTINCT app_id) FROM public.app_sdks WHERE is_active = TRUE) as market_share,

        -- 分类内市场份额
        COUNT(DISTINCT asdk.app_id)::FLOAT /
        NULLIF((
            SELECT COUNT(DISTINCT asdk2.app_id)
            FROM public.app_sdks asdk2
            JOIN public.sdks s2 ON asdk2.sdk_id = s2.id
            WHERE s2.category = s.category AND asdk2.is_active = TRUE
        ), 0) as category_market_share

    FROM public.sdks s
    LEFT JOIN public.app_sdks asdk ON s.id = asdk.sdk_id AND asdk.is_active = TRUE
    LEFT JOIN public.apps a ON asdk.app_id = a.id
    GROUP BY s.id, s.name, s.category, s.company
)
SELECT
    *,
    -- 竞争强度分析
    CASE
        WHEN category_market_share > 0.5 THEN 'dominant'
        WHEN category_market_share > 0.2 THEN 'leading'
        WHEN category_market_share > 0.05 THEN 'competitive'
        ELSE 'niche'
    END as market_position,

    -- 增长潜力评分
    CASE
        WHEN category_spread > 5 AND avg_app_rating > 4.0 THEN 'high'
        WHEN category_spread > 3 AND avg_app_rating > 3.5 THEN 'medium'
        ELSE 'low'
    END as growth_potential

FROM sdk_stats
WHERE app_count > 0
ORDER BY market_share DESC;
```

### 3.3 用户个性化推荐系统
```sql
-- 用户兴趣标签表
CREATE TABLE public.user_interest_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,

    -- 兴趣标签
    tag_type TEXT NOT NULL, -- 'category', 'sdk', 'company', 'keyword'
    tag_value TEXT NOT NULL,

    -- 兴趣强度
    interest_score DECIMAL(3,2) DEFAULT 0.5, -- 0.0-1.0
    interaction_count INTEGER DEFAULT 1,

    -- 时间衰减
    last_interaction_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(user_id, tag_type, tag_value)
);

-- 创建索引
CREATE INDEX idx_user_interest_tags_user ON public.user_interest_tags (user_id);
CREATE INDEX idx_user_interest_tags_score ON public.user_interest_tags (interest_score DESC);
CREATE INDEX idx_user_interest_tags_type ON public.user_interest_tags (tag_type);

-- RLS 策略
ALTER TABLE public.user_interest_tags ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own interest tags" ON public.user_interest_tags
    FOR SELECT USING (user_id = auth.uid());
```

### 3.4 个性化推荐函数
```sql
-- 获取用户个性化应用推荐
CREATE OR REPLACE FUNCTION public.get_personalized_app_recommendations(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    app_id TEXT,
    app_name TEXT,
    app_description TEXT,
    app_icon_url TEXT,
    app_rating DECIMAL,
    recommendation_score DECIMAL,
    recommendation_reason TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH user_interests AS (
        SELECT tag_type, tag_value, interest_score
        FROM public.user_interest_tags
        WHERE user_id = p_user_id
        AND interest_score > 0.3
    ),
    scored_apps AS (
        SELECT
            a.id,
            a.name,
            a.description,
            a.icon_url,
            a.rating,

            -- 计算推荐分数
            (
                -- 分类匹配
                COALESCE((
                    SELECT ui.interest_score * 0.4
                    FROM user_interests ui
                    WHERE ui.tag_type = 'category' AND ui.tag_value = a.category
                ), 0) +

                -- SDK 匹配
                COALESCE((
                    SELECT AVG(ui.interest_score) * 0.3
                    FROM user_interests ui
                    JOIN public.app_sdks asdk ON asdk.app_id = a.id
                    JOIN public.sdks s ON asdk.sdk_id = s.id
                    WHERE ui.tag_type = 'sdk' AND ui.tag_value = s.name
                ), 0) +

                -- 开发者匹配
                COALESCE((
                    SELECT ui.interest_score * 0.2
                    FROM user_interests ui
                    WHERE ui.tag_type = 'company' AND ui.tag_value = a.developer
                ), 0) +

                -- 基础质量分数
                (a.rating / 5.0) * 0.1

            ) as recommendation_score,

            -- 推荐理由
            CASE
                WHEN EXISTS (
                    SELECT 1 FROM user_interests ui
                    WHERE ui.tag_type = 'category' AND ui.tag_value = a.category
                ) THEN '基于您对 ' || a.category || ' 类应用的兴趣'
                WHEN EXISTS (
                    SELECT 1 FROM user_interests ui
                    JOIN public.app_sdks asdk ON asdk.app_id = a.id
                    JOIN public.sdks s ON asdk.sdk_id = s.id
                    WHERE ui.tag_type = 'sdk' AND ui.tag_value = s.name
                ) THEN '包含您关注的 SDK'
                ELSE '高质量应用推荐'
            END as reason

        FROM public.apps a
        WHERE a.rating >= 3.0
        AND a.id NOT IN (
            -- 排除用户已查看的应用
            SELECT DISTINCT (activity_details->>'app_id')::TEXT
            FROM public.user_activity_logs
            WHERE user_id = p_user_id
            AND activity_type = 'view_app'
            AND activity_details->>'app_id' IS NOT NULL
        )
    )
    SELECT
        sa.id,
        sa.name,
        sa.description,
        sa.icon_url,
        sa.rating,
        sa.recommendation_score,
        sa.reason
    FROM scored_apps sa
    WHERE sa.recommendation_score > 0.1
    ORDER BY sa.recommendation_score DESC, sa.rating DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 4. 实时功能支持

### 4.1 实时通知触发器
```sql
-- 创建实时通知函数
CREATE OR REPLACE FUNCTION public.notify_user_update()
RETURNS TRIGGER AS $$
BEGIN
    -- 发送实时通知到前端
    PERFORM pg_notify(
        'user_notification_' || NEW.user_id::TEXT,
        json_build_object(
            'type', 'notification',
            'data', row_to_json(NEW)
        )::TEXT
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER notify_user_notification_trigger
    AFTER INSERT ON public.user_notifications
    FOR EACH ROW EXECUTE FUNCTION public.notify_user_update();
```

### 4.2 配额实时更新触发器
```sql
-- 配额更新实时通知函数
CREATE OR REPLACE FUNCTION public.notify_quota_update()
RETURNS TRIGGER AS $$
BEGIN
    -- 检查是否接近配额限制
    IF (NEW.api_calls_used::FLOAT / NEW.api_calls_limit > 0.8) OR
       (NEW.search_used::FLOAT / NEW.search_limit > 0.8) OR
       (NEW.exports_used::FLOAT / NEW.exports_limit > 0.8) THEN

        -- 发送配额警告通知
        PERFORM pg_notify(
            'quota_warning_' || NEW.user_id::TEXT,
            json_build_object(
                'type', 'quota_warning',
                'data', row_to_json(NEW)
            )::TEXT
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER notify_quota_update_trigger
    AFTER UPDATE ON public.user_quotas
    FOR EACH ROW EXECUTE FUNCTION public.notify_quota_update();
```

这个增强功能文档为 Tenyy 数据库设计添加了：

1. **智能搜索系统**：搜索建议、自动完成、搜索分析
2. **应用富集功能**：批量数据处理、缓存管理
3. **通知系统**：F8 面板支持、模板化通知
4. **用户行为分析**：活动跟踪、使用统计
5. **Meilisearch 集成**：索引管理、搜索优化
6. **高级分析**：趋势分析、市场份额、个性化推荐
7. **实时功能**：WebSocket 通知、配额监控

所有功能都完全符合 Tenyy PRD 的需求，并提供了生产级的性能和可扩展性。
```
