# -*- coding: utf-8 -*-
"""
Tenyy 统一配置系统 - K8s 本地环境配置

用于在本地 Kubernetes 集群运行 Prefect Server 与 Worker，
并通过 Ingress 访问 Prefect 与 Registry。
"""

import os
from .base import *  # 导入所有基础配置

# ============================================================================
# 🌍 环境标识
# ============================================================================

ENVIRONMENT = "k8s"
ENVIRONMENT_NAME = "Kubernetes 本地环境"

# ============================================================================
# 🗄️ 数据库配置 - K8s
# ============================================================================

DATABASE_CONFIG = {
    "host": os.getenv("POSTGRES_HOST", "app-db"),
    "port": int(os.getenv("POSTGRES_PORT", "5432")),
    "database": os.getenv("POSTGRES_DB", "tenyy_app"),
    "username": os.getenv("POSTGRES_USER", "admin"),
    "password": os.getenv("POSTGRES_PASSWORD", "zhangdi168"),
}

# 统一数据库环境变量配置 (仅使用POSTGRES_*格式)
DB_CONFIG = {
    "POSTGRES_HOST": DATABASE_CONFIG["host"],
    "POSTGRES_PORT": str(DATABASE_CONFIG["port"]),
    "POSTGRES_DB": DATABASE_CONFIG["database"],
    "POSTGRES_USER": DATABASE_CONFIG["username"],
    "POSTGRES_PASSWORD": DATABASE_CONFIG["password"],
}

# ============================================================================
# 🔄 Prefect配置 - K8s
# ============================================================================

# CLI 部署走 Ingress；K8s 内任务访问 Service。
PREFECT_CONFIG = {
    "api_url": os.getenv("PREFECT_API_URL", "http://prefect.local/api"),
    "ui_url": "http://prefect.local",
    "work_pool_name": WORK_POOL_NAME,
    "logging_level": "INFO",
}

# ============================================================================
# 📥 Aria2配置 - K8s
# ============================================================================

ARIA2_CONFIG = {
    # 任务容器内访问集群 Service
    "rpc_url": os.getenv("ARIA2_RPC_URL", "http://aria2-service:6800/jsonrpc"),
    "rpc_token": os.getenv("ARIA2_RPC_TOKEN", "zhangdi168"),
    "download_dir": os.getenv("DOWNLOAD_DIR", "/downloads"),
    **ARIA2_COMMON_CONFIG,
}

# ============================================================================
# 🐳 镜像仓库 - K8s
# ============================================================================

# Kubelet/containerd 通常不经 Ingress 拉镜像，请使用节点可直连的 registry 地址。
DOCKER_REGISTRY = {
    # 如节点可访问宿主机 registry，请将 host:port 调整为实际可达地址
    "host": "127.0.0.1",
    "port": "5000",
}
DOCKER_REGISTRY_URL = f"{DOCKER_REGISTRY['host']}:{DOCKER_REGISTRY['port']}"

DOCKER_IMAGE_CONFIG = {
    "registry": DOCKER_REGISTRY_URL,
    "image_name": DOCKER_CONFIG["image_name"],
    "tag": DOCKER_CONFIG["image_tag"],
    "full_image": f"{DOCKER_REGISTRY_URL}/{DOCKER_CONFIG['image_name']}:{DOCKER_CONFIG['image_tag']}",
}

# ============================================================================
# 🌐 网络配置 - K8s（占位，无需 Docker 网络）
# ============================================================================

NETWORK_CONFIG = {
    "docker_network": None,
    "network_mode": None,
}

# ============================================================================
# 📁 存储路径配置 - K8s
# ============================================================================

# 如需持久化下载目录，建议通过 PVC 在 Work Pool 模板中挂载。
K8S_STORAGE_CONFIG = {
    **STORAGE_CONFIG,
    "log_file_path": "/tmp/tenyy_k8s.log",
}

# ============================================================================
# 🔧 Prefect部署配置 - K8s
# ============================================================================

# 注意：针对 K8s Worker，不设置 Docker-only 字段（networks/volumes）。
PREFECT_DEPLOYMENT_CONFIG = {
    "work_pool": {
        "name": WORK_POOL_NAME,
        "type": "kubernetes",
    },
    "job_variables": {
        "image": DOCKER_IMAGE_CONFIG["full_image"],
        "image_pull_policy": "IfNotPresent",
        "env": {
            "PREFECT_LOGGING_LEVEL": "INFO",
            "PYTHONPATH": DOCKER_CONFIG["pythonpath"],
            "K8S_ENV": "true",
            **DB_CONFIG,
            **{
                "ARIA2_RPC_URL": ARIA2_CONFIG["rpc_url"],
                "ARIA2_RPC_TOKEN": ARIA2_CONFIG["rpc_token"],
                "DOWNLOAD_DIR": ARIA2_CONFIG["download_dir"],
                # 任务容器内访问 Service 更合适
                "PREFECT_API_URL": "http://prefect-server:4200/api",
            },
        },
        # 资源（Prefect 3 K8s job variables 字段命名）
        "cpu_request": "1",
        "memory_request": "2Gi",
        "cpu_limit": "2",
        "memory_limit": "4Gi",
    },
}

# 特定模块配置（保持与其他环境一致）
CRAWLER_CONFIG = {
    "config_dir": "tenyy/src/crawler/configs",
    "yinyongbao_config": "tenyy/src/crawler/configs/yinyongbao.yaml",
    "huawei_config": "tenyy/src/crawler/configs/huawei.yaml",
    "categories_dir": "tenyy/src/crawler",
}

DOWNLOAD_EXTRACT_CONFIG = {
    "scheduler_deployment": {
        "name": "adaptive-scheduler-k8s",
        "description": "自适应调度器 - K8s",
        "work_queue": "scheduler-queue",
        "job_variables": {
            **PREFECT_DEPLOYMENT_CONFIG["job_variables"],
            **RESOURCE_LIMITS["download_extract"]["scheduler"],
        },
    },
    "processor_deployment": {
        "name": "apk-processor-k8s",
        "description": "APK处理器 - K8s",
        "work_queue": "processor-queue",
        "job_variables": {
            **PREFECT_DEPLOYMENT_CONFIG["job_variables"],
            **RESOURCE_LIMITS["download_extract"]["processor"],
        },
    },
}

# 导出统一 CONFIG
CONFIG = {
    "environment": ENVIRONMENT,
    "database": DATABASE_CONFIG,
    "prefect": PREFECT_CONFIG,
    "aria2": ARIA2_CONFIG,
    "docker": DOCKER_IMAGE_CONFIG,
    "network": NETWORK_CONFIG,
    "storage": K8S_STORAGE_CONFIG,
    "crawler": CRAWLER_CONFIG,
    "download_extract": DOWNLOAD_EXTRACT_CONFIG,
}

# ============================================================================
# 🚀 Prefect部署配置 - K8s 环境（与其他环境保持一致的导出变量）
# ============================================================================

# 供 generate_prefect_config.py 读取的简化变量
HOST_DOWNLOAD_DIR = "/mnt/ssd/tenyy/downloads"
DOCKER_IMAGE = DOCKER_IMAGE_CONFIG["full_image"]
DOCKER_NETWORK = None
PREFECT_API_URL = "http://prefect.local/api"
