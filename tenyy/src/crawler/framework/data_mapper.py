#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
DataMapper - 数据映射器

负责将不同来源的原始数据映射到标准数据格式
"""

from typing import Dict, Any, Optional
from datetime import datetime
from prefect import get_run_logger
from .config_manager import ConfigManager
from .utils import DataCleaner


class DataMapper:
    """数据映射器，将原始数据映射到标准格式"""
    
    def __init__(self, config_name: str):
        """
        初始化数据映射器

        Args:
            config_name: 配置名称
        """
        self._logger = None
        self.config = ConfigManager.load_config(config_name)
        self.data_cleaner = DataCleaner()
        self.store_type = self.config.get('store_type', config_name)

        # 获取映射配置
        self.mapping_config = self.config.get('data_mapping', {})
        self.field_mapping = self.mapping_config.get('field_mapping', {})
        self.cleaning_rules = self.mapping_config.get('cleaning_rules', {})

    @property
    def logger(self):
        """延迟初始化logger"""
        if self._logger is None:
            try:
                from prefect import get_run_logger
                self._logger = get_run_logger()
            except:
                # 如果没有Prefect上下文，使用标准logging
                import logging
                self._logger = logging.getLogger(__name__)
        return self._logger
    
    def map_to_standard(self, raw_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        将原始数据映射到标准格式
        
        Args:
            raw_data: 原始数据
            **kwargs: 额外参数（如is_game等）
            
        Returns:
            标准化数据
        """
        if self.store_type == 'huawei':
            return self._map_huawei_data(raw_data, **kwargs)
        elif self.store_type == 'yinyongbao':
            return self._map_yinyongbao_data(raw_data, **kwargs)
        else:
            return self._map_generic_data(raw_data, **kwargs)
    
    def _map_huawei_data(self, raw_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """映射华为应用商店数据"""
        is_game = kwargs.get('is_game', False)
        
        # 提取详情数据
        detail_json_data = raw_data.get('big_detail', {})
        extracted_fields = self._extract_huawei_detail_fields(detail_json_data)
        
        # 处理更新时间 - 和原有华为流程保持一致
        update_time_stamp = raw_data.get('updateTime')
        last_updated = datetime.fromtimestamp(int(update_time_stamp) / 1000) if update_time_stamp else datetime.now()

        # 处理上架时间 - 映射到 release_date
        shelves_time_stamp = raw_data.get('shelvesTime')
        release_date = None
        if shelves_time_stamp:
            try:
                release_date = datetime.fromtimestamp(int(shelves_time_stamp) / 1000)
                self.logger.info(f"Found shelvesTime for {raw_data.get('package')}: {release_date.strftime('%Y-%m-%d %H:%M:%S')}")
            except (ValueError, TypeError) as e:
                self.logger.warning(f"Failed to parse shelvesTime {shelves_time_stamp}: {e}")
        else:
            # 如果没有 shelvesTime，尝试其他可能的时间字段
            for time_field in ['publishTime', 'releaseTime', 'createTime']:
                time_value = raw_data.get(time_field)
                if time_value:
                    try:
                        release_date = datetime.fromtimestamp(int(time_value) / 1000)
                        self.logger.info(f"Using {time_field} as release_date for {raw_data.get('package')}: {release_date.strftime('%Y-%m-%d %H:%M:%S')}")
                        break
                    except (ValueError, TypeError) as e:
                        self.logger.warning(f"Failed to parse {time_field} {time_value}: {e}")

            # 如果仍然没有找到发布时间，记录警告
            if not release_date:
                self.logger.warning(f"No release date found for {raw_data.get('package')}, shelvesTime and other time fields are missing")
        
        # 处理MD5 - 如果API没有返回，生成一个基于关键信息的哈希
        md5_value = raw_data.get('md5')
        if not md5_value:
            # 生成一个基于包名、版本和商店ID的哈希作为唯一标识
            import hashlib
            unique_string = f"{raw_data.get('package')}_{raw_data.get('versionName')}_{raw_data.get('detailId')}_{self.store_type}"
            md5_value = hashlib.md5(unique_string.encode('utf-8')).hexdigest()
            self.logger.info(f"Generated MD5 for {raw_data.get('package')}: {md5_value}")

        # 构建标准数据 - 完全基于原有华为流程的字段映射
        standardized_data = {
            'pkg_name': raw_data.get('package'),
            'name': raw_data.get('name'),
            'store_id': raw_data.get('detailId'),
            'developer': extracted_fields.get('developer'),
            'operator': raw_data.get('provider') or extracted_fields.get('developer') or raw_data.get('developer'),
            'category': extracted_fields.get('category'),
            'is_game': self.data_cleaner.to_bool(is_game),
            'icon': raw_data.get('icon'),
            'md5': md5_value,
            'version_name': raw_data.get('versionName'),
            'version_description': f"{extracted_fields.get('app_intro', '')}\n\n版本更新:\n{raw_data.get('newFeatures', '')}",
            'description': extracted_fields.get('store_description_memo'),
            'download_url': raw_data.get('downurl'),
            'file_size': self.data_cleaner.to_int(raw_data.get('size')),
            'last_updated': last_updated,
            'release_date': release_date,
            'rating': extracted_fields.get('rating'),
            'download_count': extracted_fields.get('download_count'),
            'review_count': extracted_fields.get('review_count'),
            'tags': extracted_fields.get('tags'),
            'privacy_agreement': raw_data.get('privacyUrl'),
            'permissions_list': raw_data.get('appPermission', []),
            'restrict_level': extracted_fields.get('restrict_level'),
            'editor_intro': extracted_fields.get('editor_intro'),
            'snap_shots': extracted_fields.get('snap_shots', []),
            'store_metadata': {
                'safe_labels': raw_data.get('safeLabels', []),
                'label_names': extracted_fields.get('labelNames', [])
            },
            'detail_json': raw_data.get('big_detail', {}),
            'store_type': self.store_type,
            'raw_data': raw_data
        }
        
        return standardized_data
    
    def _map_yinyongbao_data(self, raw_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """映射应用宝数据 - 支持列表页和详情页数据合并"""
        is_game = kwargs.get('is_game', False)

        # 获取详情页数据（如果有的话）
        # 注意：raw_data 可能已经是合并后的数据，包含列表页和详情页的所有字段
        detail_data = kwargs.get('detail_data', raw_data)

        # 处理MD5 - 如果API没有返回，生成一个基于关键信息的哈希
        md5_value = raw_data.get('md5') or raw_data.get('md_5') or detail_data.get('md_5')
        if not md5_value:
            # 生成一个基于包名、版本和应用ID的哈希作为唯一标识
            import hashlib
            pkg_name = raw_data.get('pkg_name') or raw_data.get('pkgName')
            version_name = raw_data.get('version_name') or raw_data.get('versionName') or detail_data.get('version_name')
            app_id = raw_data.get('app_id') or raw_data.get('appId') or detail_data.get('app_id')
            unique_string = f"{pkg_name}_{version_name}_{app_id}_{self.store_type}"
            md5_value = hashlib.md5(unique_string.encode('utf-8')).hexdigest()
            self.logger.info(f"Generated MD5 for {pkg_name}: {md5_value}")

        # 处理下载量
        download_num = detail_data.get('download_num') or raw_data.get('download_num')
        download_count = self.data_cleaner.to_int(download_num) if download_num else None

        # 处理更新时间
        update_time = detail_data.get('update_time') or raw_data.get('update_time') or raw_data.get('publishTime')
        last_updated = self._parse_yinyongbao_date(update_time)

        # 处理发布时间 - 应用宝可能有多个时间字段
        # 优先使用 public_time，然后是 app_update_time，最后使用 update_time
        release_time = (detail_data.get('public_time') or
                       detail_data.get('app_update_time') or
                       raw_data.get('public_time') or
                       raw_data.get('app_update_time') or
                       update_time)  # 如果没有专门的发布时间，使用更新时间
        release_date = self._parse_yinyongbao_date(release_time)

        # 构建标准数据 - 详情页数据优先，列表页数据作为备选
        standardized_data = {
            # --- 基础应用信息 ---
            'pkg_name': raw_data.get('pkg_name') or raw_data.get('pkgName'),
            'name': detail_data.get('name') or raw_data.get('name') or raw_data.get('appName'),
            'store_id': str(detail_data.get('app_id') or raw_data.get('app_id') or raw_data.get('appId', '')),
            'developer': detail_data.get('developer') or raw_data.get('developer') or raw_data.get('authorName'),
            'operator': detail_data.get('operator') or raw_data.get('operator') or raw_data.get('authorName'),
            'category': detail_data.get('cate_name') or raw_data.get('category') or raw_data.get('categoryName'),
            'is_game': self.data_cleaner.to_bool(is_game),
            'icon': detail_data.get('icon') or raw_data.get('icon') or raw_data.get('iconUrl'),
            'description': raw_data.get('description') or detail_data.get('description') or raw_data.get('appDetailInfo'),

            # --- 版本信息 ---
            'md5': md5_value,
            'version_name': detail_data.get('version_name') or raw_data.get('version_name') or raw_data.get('versionName'),
            'version_description': detail_data.get('description') or raw_data.get('versionInfo', ''),
            'download_url': self._get_best_download_url(raw_data, detail_data),
            'file_size': self.data_cleaner.to_int(detail_data.get('apk_size') or raw_data.get('apk_size') or raw_data.get('apkSize')),
            'last_updated': last_updated,
            'release_date': release_date,
            'rating': self.data_cleaner.to_float(detail_data.get('average_rating') or raw_data.get('average_rating') or raw_data.get('score')),
            'download_count': download_count,
            'download_num': download_num,  # 保留原始下载量字符串
            'price': 0,  # 应用宝主要是免费应用
            'currency': 'CNY',
            'store_type': self.store_type,

            # --- 扩展字段（与老代码保持一致）---
            'editor_intro': detail_data.get('editor_intro'),
            'tags': detail_data.get('tags'),
            'snap_shots': detail_data.get('snap_shots'),
            'permissions_list': detail_data.get('permissions_list'),
            'icp_number': detail_data.get('icp_number'),
            'icp_entity': detail_data.get('icp_entity'),
            'privacy_agreement': detail_data.get('privacy_agreement'),
            'video': detail_data.get('video'),
            'is_cloud_game': self.data_cleaner.to_bool(detail_data.get('is_cloud_game')),
            'is_pc_yyb_available': self.data_cleaner.to_bool(detail_data.get('is_pc_yyb_available')),
            'is_booking': self.data_cleaner.to_bool(detail_data.get('is_booking')),
            'restrict_level': self.data_cleaner.to_int(detail_data.get('restrict_level')),
            'syzs_download_num': detail_data.get('syzs_download_num'),
            'booking_user_cnt': self.data_cleaner.to_int(detail_data.get('booking_user_cnt')),
            'public_time': detail_data.get('public_time'),
            'app_update_time': detail_data.get('app_update_time'),
            'channel_info': detail_data.get('channel_info'),
            'show_text': detail_data.get('show_text'),
            'detail_template': detail_data.get('detail_template'),
            'ios_app_link_info': detail_data.get('ios_app_link_info'),
            'ios_url': detail_data.get('ios_url'),
            'booking_gift': detail_data.get('booking_gift'),
            'cloud_game_info': detail_data.get('cloudGameInfo'),
            'username': detail_data.get('username'),
            'tag_alias': detail_data.get('tag_alias'),
            'game_type': detail_data.get('game_type'),
            'cp_id': detail_data.get('cp_id'),
            'ms_store_id': detail_data.get('ms_store_id'),
            'ms_store_status': detail_data.get('ms_store_status'),
            'booking_url': detail_data.get('booking_url'),
            'exe_download_url': detail_data.get('exe_download_url'),

            'raw_data': raw_data
        }

        return standardized_data

    def _get_best_download_url(self, raw_data: Dict[str, Any], detail_data: Dict[str, Any]) -> Optional[str]:
        """
        智能选择最佳的下载URL

        Args:
            raw_data: 原始数据（可能是合并后的数据）
            detail_data: 详情页数据

        Returns:
            最佳的下载URL
        """
        # 对于应用宝，优先使用列表页的download_url，因为详情页的可能是空的

        # 1. 优先使用备份的列表页download_url（如果存在）
        list_page_url = raw_data.get('_list_page_download_url')

        # 2. 尝试从原始数据中获取
        raw_url = raw_data.get('download_url')

        # 3. 尝试从详情页数据中获取
        detail_url = detail_data.get('download_url')

        # 4. 尝试其他可能的字段名
        apk_url = raw_data.get('apkUrl')

        # 智能选择：非空且有效的URL
        for url in [list_page_url, raw_url, detail_url, apk_url]:
            if url and isinstance(url, str) and url.strip() and url != 'None':
                # 检查是否是有效的URL
                if url.startswith(('http://', 'https://')):
                    return url.strip()

        return None

    def _map_generic_data(self, raw_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """通用数据映射"""
        standardized_data = {}
        
        # 使用配置中的字段映射
        for standard_field, raw_field in self.field_mapping.items():
            standardized_data[standard_field] = raw_data.get(raw_field)
        
        # 应用数据清洗规则
        self._apply_cleaning_rules(standardized_data)
        
        # 添加默认字段
        standardized_data.update({
            'store_type': self.store_type,
            'raw_data': raw_data,
            **kwargs
        })
        
        return standardized_data
    
    def _extract_huawei_detail_fields(self, detail_json: Dict[str, Any]) -> Dict[str, Any]:
        """从华为详情JSON中提取字段 - 基于原有华为流程的精确逻辑"""
        extracted = {}

        try:
            # 遍历layoutData，根据layoutName精确提取字段
            layout_data = detail_json.get('layoutData', [])
            for card in layout_data:
                card_name = card.get('layoutName')
                data_list = card.get('dataList', [])
                if not data_list:
                    continue
                item = data_list[0]

                if card_name == 'appdetailheadercard':
                    extracted['developer'] = item.get('developer')
                    extracted['category'] = item.get('tagName')
                    extracted['tags'] = item.get('tagName')
                    extracted['store_description_memo'] = item.get('memo')
                    extracted['labelNames'] = item.get('labelNames', [])
                elif card_name == 'appdetaildatacard':
                    extracted['rating'] = self.data_cleaner.to_float(item.get('stars'))
                    downloads_str = item.get('downloads', '0')
                    unit = item.get('downloadUnit', '')
                    extracted['download_count'] = self._parse_download_count(downloads_str, unit)
                    if item.get('gradeInfo'):
                        extracted['restrict_level'] = item.get('gradeInfo', {}).get('gradeDesc')
                    extracted['review_count'] = self.data_cleaner.to_int(item.get('scoredBy'))
                elif card_name == 'detaileditorrecommendcard':
                    extracted['editor_intro'] = item.get('body')
                elif card_name == 'detailscreencardv4':
                    extracted['snap_shots'] = item.get('images', [])
                elif card_name == 'appdetailintrocardv2':
                    extracted['app_intro'] = item.get('appIntro')

        except Exception as e:
            self.logger.warning(f"Failed to extract Huawei detail fields: {e}")

        return extracted

    def _parse_download_count(self, count_str: str, unit_str: str) -> int:
        """解析华为下载量字符串（更稳健，避免将“<1万”等提示性单位误乘）"""
        try:
            # 预处理
            unit_str = unit_str or ''
            raw = count_str if isinstance(count_str, (str, int, float)) else '0'
            try:
                count = float(raw)
            except Exception:
                # 从字符串中提取数字
                import re
                m = re.search(r"\d+(?:\.\d+)?", str(raw) or '')
                count = float(m.group(0)) if m else 0.0

            # 如果单位包含“<”或“约”等提示词，倾向于认为 count 已是绝对值
            hint_tokens = ['<', '约', '近', '超过', '大于', '小于', '+']
            if any(t in unit_str for t in hint_tokens):
                return int(count)

            # 单位换算（当 count 看起来是系数时才乘）
            if '亿' in unit_str:
                # 一般“downloads=1.2, unit=亿”→ 1.2e8；若 count 已经很大，则认为已是绝对值
                return int(count if count >= 10_000_000 else count * 100_000_000)
            if '万' in unit_str:
                # 常见形式“downloads=1.2, unit=万”→ 1.2e4；若 count 已经>=1万，视为绝对值
                return int(count if count >= 10_000 else count * 10_000)

            # 无单位时按绝对值返回
            return int(count)
        except (ValueError, TypeError):
            return 0

    def _parse_huawei_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """解析华为日期格式"""
        if not date_str:
            return None
        
        try:
            # 华为日期格式通常是 "2024-01-15" 或时间戳
            if date_str.isdigit():
                return datetime.fromtimestamp(int(date_str) / 1000)
            else:
                return datetime.strptime(date_str, "%Y-%m-%d")
        except Exception as e:
            self.logger.warning(f"Failed to parse Huawei date '{date_str}': {e}")
            return None
    
    def _parse_yinyongbao_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """解析应用宝日期格式"""
        if not date_str:
            return None
        
        try:
            # 应用宝日期格式通常是时间戳或 "2024-01-15"
            if date_str.isdigit():
                return datetime.fromtimestamp(int(date_str))
            else:
                return datetime.strptime(date_str, "%Y-%m-%d")
        except Exception as e:
            self.logger.warning(f"Failed to parse Yinyongbao date '{date_str}': {e}")
            return None
    
    def _apply_cleaning_rules(self, data: Dict[str, Any]) -> None:
        """应用数据清洗规则"""
        # 处理布尔值字段
        for field in self.cleaning_rules.get('boolean_fields', []):
            if field in data:
                data[field] = self.data_cleaner.to_bool(data[field])
        
        # 处理整数字段
        for field in self.cleaning_rules.get('integer_fields', []):
            if field in data:
                data[field] = self.data_cleaner.to_int(data[field])
        
        # 处理浮点数字段
        for field in self.cleaning_rules.get('float_fields', []):
            if field in data:
                data[field] = self.data_cleaner.to_float(data[field])
