-- 一览所有 App 的配对 SDK 情况视图
-- 说明：按每条 app_version_sdks 记录展开，关联应用、版本与知识库（若已配对）
-- 注意：与 KB 的配对采用 package_prefix 的 trim 精确匹配，保持与业务代码一致

CREATE OR REPLACE VIEW app_sdk_overview AS
 SELECT a.id AS app_id,
    a.name AS app_name,
    a.description AS app_description,
    av.id AS app_version_id,
    av.version AS app_version,
    md5((COALESCE(av.id, s.app_version_id)::text || '|'::text) || TRIM(BOTH FROM s.sdk_package_prefix)) AS app_version_sdk_key,
    TRIM(BOTH FROM s.sdk_package_prefix) AS package_prefix,
    s.match_type,
    s.type AS detection_type,
    s.child_packages AS child_packages_json,
    s.kb_last_ask_at,
    s.kb_last_ask_result,
    kb.id IS NOT NULL AS is_matched_kb,
    kb.id AS kb_id,
    kb.package_prefix AS kb_package_prefix,
    kb.sdk_name AS kb_sdk_name,
    kb.subcategory_id AS kb_subcategory_id,
    kb.detection_type AS kb_detection_type,
    kb.is_regex_rule AS kb_is_regex_rule,
    kb.regex_pattern AS kb_regex_pattern,
    kb.description AS kb_description,
    kb.status AS kb_status,
    kb.last_checked_at AS kb_last_checked_at
   FROM class_app_version_sdks s
     LEFT JOIN app_version av ON av.id = s.app_version_id
     LEFT JOIN store_app sa ON sa.id = av.store_app_id
     LEFT JOIN app a ON a.id::text = sa.app_id::text
     LEFT JOIN class_sdk_knowledge_base kb ON TRIM(BOTH FROM kb.package_prefix) = TRIM(BOTH FROM s.sdk_package_prefix);