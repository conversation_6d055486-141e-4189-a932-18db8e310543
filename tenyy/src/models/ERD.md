# ER Diagram (Mermaid) — public schema (logical)

> 说明：以下为“逻辑关系图”，包含 FDW 外表、视图与物化视图。FDW 在数据库层面通常无 FK，故此图用业务含义表达关联。

```mermaid
erDiagram
  %% ============= Core Entities (FDW) =============
  app {
    varchar id PK
    text name
    text description
  }

  store_app {
    int id PK
    varchar app_id FK
    text store_name
  }

  app_version {
    int id PK
    int store_app_id FK
    varchar version
  }

  class_app_version_sdks {
    int app_version_id FK
    varchar sdk_package_prefix
    int sdk_knowledge_base_id FK
    text match_type
  }

  class_sdk_knowledge_base {
    int id PK
    text package_prefix
    varchar sdk_name
    int subcategory_id FK
  }

  class_subcategory {
    int id PK
    int category_id
    varchar name
  }

  %% ============= Views =============
  app_sdk_overview {
    int app_version_id
    varchar kb_sdk_name
    int kb_subcategory_id
    boolean is_matched_kb
  }

  v_categories_slug {
    int category_id
    varchar category_name
    text category_slug
    bigint sdk_count
  }

  v_subcategories_slug {
    int subcategory_id
    varchar subcategory_name
    text subcategory_slug
    bigint sdk_count
  }

  %% ============= Materialized Views =============
  mv_app_sdk_market_share {
    int subcategory_id
    varchar kb_sdk_name
    bigint cnt
  }

  %% ============= Relationships (logical) =============
  app ||--o{ store_app : has
  store_app ||--o{ app_version : has
  app_version ||--o{ class_app_version_sdks : contains
  class_sdk_knowledge_base ||--o{ class_app_version_sdks : matched_by
  class_subcategory ||--o{ class_sdk_knowledge_base : has

  %% Views dependencies (logical)
  app ||.. app_sdk_overview : source
  store_app ||.. app_sdk_overview : source
  app_version ||.. app_sdk_overview : source
  class_app_version_sdks ||.. app_sdk_overview : source
  class_sdk_knowledge_base ||.. app_sdk_overview : source

  %% MV aggregates from overview
  app_sdk_overview }|..|{ mv_app_sdk_market_share : aggregates

  %% Slug views (logical sources)
  class_subcategory ||.. v_subcategories_slug : source
  class_sdk_knowledge_base ||.. v_subcategories_slug : counts
  v_subcategories_slug }|..|{ v_categories_slug : rollup
```

## 查询建议（TopN）
- mv_app_sdk_market_share：为 `WHERE subcategory_id = ? ORDER BY cnt DESC LIMIT N` 建索引 `(subcategory_id, cnt DESC)`；并发刷新需要唯一索引 `(subcategory_id, kb_sdk_name)`。
- 视图/FDW 权限：确保 `anon`、`authenticated` 具备所需 SELECT 与 user mapping（如使用 FDW）。
