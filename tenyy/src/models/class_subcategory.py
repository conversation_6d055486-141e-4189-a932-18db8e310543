# coding: utf-8
from sqlalchemy import Column, String, DateTime, Integer, ForeignKey, UniqueConstraint
from sqlalchemy.sql import func
from tenyy.src.models.base import Base


class class_Subcategory(Base):
    """子分类表，对应 public.class_subcategory"""
    __tablename__ = 'class_subcategory'
    __table_args__ = (
        UniqueConstraint('category_id', 'name', name='uq_category_subcategory_name'),
    )

    id = Column(Integer, primary_key=True, autoincrement=True)
    category_id = Column(Integer, ForeignKey('class_category.id', ondelete='RESTRICT'), nullable=False)
    name = Column(String(100), nullable=False)
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now())

    def __repr__(self):
        return f"<class_Subcategory(id={self.id}, category_id={self.category_id}, name='{self.name}')>"
