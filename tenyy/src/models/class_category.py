# coding: utf-8
from sqlalchemy import Column, String, DateTime, Integer
from sqlalchemy.sql import func
from tenyy.src.models.base import Base


class class_Category(Base):
    """主分类表，对应 public.class_category"""
    __tablename__ = 'class_category'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, unique=True)
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now())

    def __repr__(self):
        return f"<class_Category(id={self.id}, name='{self.name}')>"
