# coding: utf-8
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, BigInteger
from sqlalchemy.sql.sqltypes import Numeric, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB
from tenyy.src.models.base import Base

class AppVersion(Base):
    """应用版本表，存储不同版本的APK信息"""
    __tablename__ = 'app_version'

    id = Column(Integer, primary_key=True)
    store_app_id = Column(Integer, ForeignKey('store_app.id'), nullable=False)
    version = Column(String)  # 版本号
    apk_hash = Column(String)  # APK哈希
    download_url = Column(Text)  # 下载URL
    apk_size = Column(BigInteger)  # APK大小(字节)
    release_date = Column(DateTime)  # 发布日期
    min_sdk = Column(Integer)  # 最低SDK版本
    target_sdk = Column(Integer)  # 目标SDK版本
    permissions = Column(JSONB)  # 权限列表
    features = Column(JSONB)  # 硬件特性
    libraries = Column(JSONB)  # 依赖库
    analyzed_at = Column(DateTime)  # 分析时间
    # 分析结果（JSONB）。当前会写入的键：
    # - processing: bool，占坑标记。由 combine_analysis_flow 在领取任务时置为 true，任务完成后置为 false；用于防止并发重复领取。
    # - sdk_identified: bool，是否已完成 SDK 匹配并落库（class_app_version_sdks）。由 combine_analysis_flow 在成功持久化后置为 true。
    # - sdk_identified_at: str（ISO8601 时间），完成 SDK 匹配与落库的时间戳。
    analysis_result = Column(JSONB)  # 分析结果（详见上方键说明）
    is_latest = Column(Boolean)  # 是否最新版本
    description = Column(Text)  # 应用描述
    editor_intro = Column(String)  # 编辑推荐
    tags = Column(String)  # 标签
    snap_shots = Column(Text)  # 截图URL
    permissions_list = Column(JSONB)  # 权限列表
    download_num = Column(BigInteger) # 下载量
    icp_number = Column(String)  # ICP备案号
    icp_entity = Column(String)  # ICP主体
    detail_json = Column(JSONB)  # 详细信息
    store_name = Column(Text)  # 商店中的名称
    store_description = Column(Text)  # 商店中的描述
    icon_url = Column(Text)  # 商店中的图标URL
    rating = Column(Numeric)  # 商店评分
    review_count = Column(Integer)  # 评论数
    download_count = Column(BigInteger)  # 下载量
    price = Column(Numeric)  # 价格
    is_free = Column(Boolean)  # 是否免费
    last_updated = Column(DateTime)  # 最后更新时间
    developer = Column(Text)         # 开发者
    operator = Column(Text)          # 运营商
    username = Column(Text)          # 开发者账号名
    category = Column(Text)          # 分类
    tag_alias = Column(Text)         # 标签别名
    game_type = Column(Text)         # 游戏类型
    is_game = Column(Boolean)        # 是否游戏
    cp_id = Column(Text)             # CP ID
    ms_store_id = Column(Text)       # 微软商店ID
    ms_store_status = Column(Text)   # 微软商店状态
    ios_url = Column(Text)           # iOS链接
    privacy_agreement = Column(Text) # 隐私协议
    booking_url = Column(Text)       # 预约链接
    exe_download_url = Column(Text)  # PC下载链接
    video = Column(Text)             # 视频链接
    is_cloud_game = Column(Boolean)  # 是否云游戏
    is_pc_yyb_available = Column(Boolean) # 是否支持PC应用宝
    is_booking = Column(Boolean)     # 是否可预约
    restrict_level = Column(Text)    # 限制级别
    syzs_download_num = Column(Text) # 手游助手下载数
    booking_user_cnt = Column(Text)  # 预约用户数
    public_time = Column(Text)       # 发布时间
    app_update_time = Column(Text)   # 应用更新时间
    channel_info = Column(Text)      # 渠道信息
    show_text = Column(Text)         # 显示文本
    detail_template = Column(Text)   # 详情模板
    ios_app_link_info = Column(Text) # iOS应用链接信息
    booking_gift = Column(Text)      # 预约礼包
    cloud_game_info = Column(JSONB)  # 云游戏信息
    store_metadata = Column(JSONB)  # 商店特定元数据

    # 下载相关字段
    filename = Column(Text)  # 下载后的文件路径（MinIO路径）目前不需要了
    download_status = Column(String)  # 下载状态：pending, downloading, downloaded, failed
    last_attempt = Column(DateTime)  # 最后尝试下载时间
    error_message = Column(Text)  # 下载失败时的错误信息
    retry_count = Column(Integer, default=0)  # 重试次数

    # 分析相关字段
    analyze_status = Column(String)  # 分析状态：pending, analyzing, done, failed
    analyze_error_message = Column(Text)  # 分析失败时的错误信息

    # APK分析结果字段
    android_manifest = Column(Text)  # AndroidManifest.xml内容
    packages_class = Column(JSONB)  # 包名信息列表（去重排序后的包名）
    lib_files = Column(JSONB)  # so库文件列表（去重排序后的库文件名）

    # 定义与商店表的关系
    store_app = relationship("StoreApp", back_populates="versions")
    
