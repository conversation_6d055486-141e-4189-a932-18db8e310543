# coding: utf-8
"""
DeepSeek 客户端封装：
- 从 `tenyy/config/key_config.json` 读取 DEEPSEEK_API_KEY
- 基于 openai SDK 创建客户端：base_url=https://api.deepseek.com
用法：

from tenyy.src.common.deepseek_client import get_client
client = get_client()
resp = client.chat.completions.create(
    model="deepseek-chat",
    messages=[{"role": "system", "content": "You are a helpful assistant"}, {"role": "user", "content": "Hello"}],
    stream=False,
)
print(resp.choices[0].message.content)
"""
from typing import Optional
from openai import OpenAI
from tenyy.src.common.key_config import get_deepseek_key

_DEEPSEEK_BASE_URL = "https://api.deepseek.com"


def get_client(api_key: Optional[str] = None) -> OpenAI:
    key = api_key or get_deepseek_key()
    if not key:
        raise RuntimeError("DEEPSEEK_API_KEY 未配置，请在 tenyy/config/key_config.json 中填写或在代码中传入 api_key")
    return OpenAI(api_key=key, base_url=_DEEPSEEK_BASE_URL)
