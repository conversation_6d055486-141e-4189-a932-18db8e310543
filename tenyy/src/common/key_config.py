# coding: utf-8
"""
读取密钥配置：`tenyy/config/key_config.json`
提供统一方法：
- load_keys() -> dict
- get_gemini_key() -> Optional[str]
- get_deepseek_key() -> Optional[str]
"""
import json
import os
from typing import Optional, Dict

_CONFIG_CACHE: Dict[str, str] | None = None


def _config_path() -> str:
    # 定位到项目内的 tenyy/config/key_config.json
    base_dir = os.path.dirname(__file__)  # .../tenyy/src/common
    cfg_path = os.path.normpath(os.path.join(base_dir, '../../config/key_config.json'))
    return cfg_path


def load_keys() -> Dict[str, str]:
    global _CONFIG_CACHE
    if _CONFIG_CACHE is not None:
        return _CONFIG_CACHE
    path = _config_path()
    try:
        with open(path, 'r', encoding='utf-8') as f:
            data = json.load(f) or {}
            if not isinstance(data, dict):
                data = {}
    except Exception:
        data = {}
    # 统一转为 str
    data = {str(k): str(v) for k, v in data.items() if v is not None}
    _CONFIG_CACHE = data
    return data


def get_gemini_key() -> Optional[str]:
    return load_keys().get('GEMINI_API_KEY') or None


def get_deepseek_key() -> Optional[str]:
    return load_keys().get('DEEPSEEK_API_KEY') or None
