#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import logging
import os
import re
import xml.etree.ElementTree as ET
from collections import Counter
from math import log2
from typing import List, Dict, Set, Optional, Tuple, Union, Any
from datetime import datetime

# 添加数据库相关导入
from sqlalchemy import create_engine, or_
from sqlalchemy.orm import sessionmaker
# 修复导入路径
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from tenyy.src.models.app_version import AppVersion
from tenyy.src.config.settings import Settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BlackListFilter:
    """黑名单过滤器，用于过滤系统和标准库包名"""
    
    def __init__(self, config_file: str):
        self.config_file = config_file
        self.patterns = self._load_blacklist_patterns()
    
    def _load_blacklist_patterns(self) -> List[re.Pattern]:
        """加载黑名单正则表达式模式"""
        patterns = []
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    # 跳过注释和空行
                    if line and not line.startswith('#'):
                        # 直接使用配置文件中的正则表达式模式
                        # 确保模式匹配整个包名前缀
                        pattern = re.compile(line)
                        patterns.append(pattern)
                        logger.debug(f"加载黑名单模式: {line}")
        except Exception as e:
            logger.error(f"加载黑名单配置文件失败: {e}")
        logger.info(f"总共加载了 {len(patterns)} 个黑名单模式")
        return patterns
    
    def is_blacklisted(self, package_name: str) -> bool:
        """检查包名是否在黑名单中"""
        logger.debug(f"检查包名是否在黑名单中: {package_name}")
        for pattern in self.patterns:
            # 使用match确保从包名开始匹配
            if pattern.match(package_name):
                logger.debug(f"包 {package_name} 被黑名单 {pattern.pattern} 匹配")
                return True
        logger.debug(f"包 {package_name} 未被任何黑名单模式匹配")
        return False

    # SDK识别与PHG相关实现已移除（不再需要）


    # SDK识别与特征提取相关实现已移除（不再需要）


    # SDK识别相关实现已移除（不再需要）


class AppDataExtractor:
    """应用数据提取器，从APK分析结果中提取各种类型的数据"""

    # 定义9种数据类型
    TYPE_NATIVE_LIBRARIES = "0"  # Native Libraries (.so文件)
    TYPE_SERVICE = "1"           # Service 组件
    TYPE_ACTIVITY = "2"          # Activity 组件
    TYPE_RECEIVER = "3"          # Receiver 组件
    TYPE_PROVIDER = "4"          # Provider 组件
    TYPE_DEX_PACKAGES = "5"      # DEX Packages (包名) - SDK识别的包就是DEX包名
    TYPE_STATIC_LIBRARIES = "6"  # Static Libraries
    TYPE_PERMISSIONS = "7"       # Permissions
    TYPE_METADATA = "8"          # Metadata
    TYPE_INTENT_ACTIONS = "9"    # Intent Actions

    def __init__(self, blacklist_filter: Optional[BlackListFilter] = None):
        self.blacklist_filter = blacklist_filter

    # =============== packages_class 混淆检测（仅用于DEX包名） ===============
    def _is_o_zero_obfuscation(self, component: str) -> bool:
        """检测 O/o 与 0 的混合混淆模式（简化版）"""
        has_o = 'O' in component or 'o' in component
        has_zero = '0' in component
        if has_o and has_zero and len(component) <= 8:
            return True
        if len(component) >= 4:
            o_zero_chars = sum(1 for c in component if c in 'Oo0')
            if o_zero_chars / len(component) > 0.7:
                return True
        return False

    def _entropy(self, s: str) -> float:
        if not s:
            return 0.0
        freq = Counter(s)
        total = len(s)
        ent = 0.0
        for c in freq.values():
            p = c / total
            if p > 0:
                ent -= p * log2(p)
        return ent

    def _is_meaningless_short_component(self, component: str) -> bool:
        if len(component) <= 4:
            if '_' in component and len(component) <= 6:
                return True
            if len(component) == 2 and component[0] == component[1] and component.lower() not in {
                'mm', 'qq', 'tt', 'cc', 'ss', 'pp', 'll'
            }:
                return True
            vowels = set('aeiouAEIOU')
            if len(component) >= 3 and not any(c in vowels for c in component):
                consonant_ratio = sum(1 for c in component if c.isalpha() and c not in vowels) / len(component)
                if consonant_ratio > 0.8:
                    return True
        if len(component) <= 6 and '_' in component:
            parts = component.split('_')
            if len(parts) >= 2 and len(parts[0]) <= 3 and len(parts[1]) <= 2:
                return True
        return False

    def _is_obfuscated_package_name(self, package_name: str) -> bool:
        """判断包名是否疑似混淆（仅用于 packages_class 过滤）"""
        if not package_name:
            return False
        components = package_name.split('.')
        for comp in components:
            if not comp:
                continue
            if len(comp) == 1:
                return True
            if comp.isdigit():
                return True
            digit_ratio = sum(c.isdigit() for c in comp) / len(comp)
            if digit_ratio > 0.5:
                return True
            if len(set(comp)) == 1 and len(comp) > 1:
                return True
            if self._is_o_zero_obfuscation(comp):
                return True
            if self._is_meaningless_short_component(comp):
                return True
            if len(comp) > 2:
                ent = self._entropy(comp)
                entropy_threshold = 1.5 if len(comp) <= 4 else 1.0
                if ent < entropy_threshold:
                    return True
        # 若大半组件看似混淆，则整体视为混淆
        suspicious = 0
        for comp in components:
            if (len(comp) <= 3 or self._is_meaningless_short_component(comp) or self._is_o_zero_obfuscation(comp)):
                suspicious += 1
        if components and suspicious / len(components) >= 0.5:
            return True
        return False

    def extract_all_data(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """从AppVersion中提取所有类型的数据"""
        all_data = []

        # 1. 提取Native Libraries (类型0)
        native_libs = self._extract_native_libraries(app_version)
        all_data.extend(native_libs)

        # 2. 提取Android组件 (类型1-4)
        components = self._extract_components(app_version)
        all_data.extend(components)

        # 3. 提取权限 (类型7)
        permissions = self._extract_permissions(app_version)
        all_data.extend(permissions)

        # 4. 提取Intent Actions (类型9)
        intent_actions = self._extract_intent_actions(app_version)
        all_data.extend(intent_actions)

        # 5. 提取元数据 (类型8)
        metadata = self._extract_metadata(app_version)
        all_data.extend(metadata)

        # 6. 提取DEX包名 (类型5)
        dex_packages = self._extract_dex_packages(app_version)
        all_data.extend(dex_packages)

        return all_data

    def _extract_dex_packages(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """提取DEX包名"""
        packages_list = []
        try:
            packages_class = getattr(app_version, 'packages_class', None)
            if not packages_class:
                return packages_list

            # 兼容多种数据格式：str(JSON字符串)/dict/list
            packages: List[str] = []
            if isinstance(packages_class, str):
                try:
                    parsed = json.loads(packages_class)
                except Exception:
                    # 若不是合法JSON，尝试按换行/逗号分隔
                    candidates = re.split(r"[\n,\s]+", packages_class.strip())
                    packages = [p for p in candidates if p]
                else:
                    if isinstance(parsed, dict):
                        packages = list(parsed.keys())
                    elif isinstance(parsed, list):
                        packages = [p for p in parsed if isinstance(p, str)]
                    else:
                        packages = []
            elif isinstance(packages_class, dict):
                packages = list(packages_class.keys())
            elif isinstance(packages_class, list):
                packages = [p for p in packages_class if isinstance(p, str)]
            else:
                packages = []

            # 统一包名格式（将斜杠替换为点号）
            formatted_packages = [pkg.replace('/', '.') for pkg in packages]
            
            # 仅对 DEX 包名做过滤：黑名单 + 混淆
            filtered_packages = []
            blacklisted_count = 0
            obfuscated_count = 0
            for package in formatted_packages:
                if self.blacklist_filter and self.blacklist_filter.is_blacklisted(package):
                    blacklisted_count += 1
                    logger.debug(f"DEX包名过滤掉黑名单包: {package}")
                    continue
                if self._is_obfuscated_package_name(package):
                    obfuscated_count += 1
                    logger.debug(f"DEX包名过滤掉疑似混淆包: {package}")
                    continue
                filtered_packages.append(package)
            if blacklisted_count or obfuscated_count:
                logger.info(
                    f"从DEX包名中过滤掉 黑名单: {blacklisted_count} 个，混淆: {obfuscated_count} 个"
                )
            formatted_packages = filtered_packages
            
            # 转换为标准格式
            for package in formatted_packages:
                packages_list.append({
                    'app_version_id': app_version.id,
                    'package_name': package,
                    'type': self.TYPE_DEX_PACKAGES
                })
            
        except json.JSONDecodeError:
            logger.warning(f"记录 {app_version.id} 的 packages_class JSON解析失败")
        except Exception as e:
            logger.error(f"提取DEX包名时出错: {e}")
            
        return packages_list

    def _extract_native_libraries(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """提取Native Libraries (类型0)"""
        libraries = []
        try:
            lib_files = getattr(app_version, 'lib_files', None)
            if lib_files:
                if isinstance(lib_files, str):
                    lib_files = json.loads(lib_files)
                if isinstance(lib_files, list):
                    for lib_name in lib_files:
                        libraries.append({
                            'app_version_id': app_version.id,
                            'package_name': lib_name,
                            'type': self.TYPE_NATIVE_LIBRARIES
                        })
        except Exception as e:
            logger.warning(f"提取Native Libraries失败 (app_version_id={app_version.id}): {e}")
        return libraries

    def _extract_components(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """提取Android组件 (类型1-4)"""
        components = []
        try:
            android_manifest = getattr(app_version, 'android_manifest', None)
            if not android_manifest:
                return components

            # 解析AndroidManifest.xml
            root = ET.fromstring(android_manifest)

            # 查找application节点
            app_node = root.find('application')
            if app_node is None:
                return components

            # 提取各种组件
            component_types = [
                ('service', self.TYPE_SERVICE),
                ('activity', self.TYPE_ACTIVITY),
                ('receiver', self.TYPE_RECEIVER),
                ('provider', self.TYPE_PROVIDER)
            ]

            for component_tag, component_type in component_types:
                for component in app_node.findall(component_tag):
                    name = component.get('{http://schemas.android.com/apk/res/android}name')
                    if name:
                        components.append({
                            'app_version_id': app_version.id,
                            'package_name': name,
                            'type': component_type
                        })

        except Exception as e:
            logger.warning(f"提取Android组件失败 (app_version_id={app_version.id}): {e}")
        return components

    def _extract_permissions(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """提取权限 (类型7)"""
        permissions = []
        try:
            android_manifest = getattr(app_version, 'android_manifest', None)
            if not android_manifest:
                return permissions

            root = ET.fromstring(android_manifest)

            # 提取uses-permission
            for perm in root.findall('uses-permission'):
                name = perm.get('{http://schemas.android.com/apk/res/android}name')
                if name:
                    permissions.append({
                        'app_version_id': app_version.id,
                        'package_name': name,
                        'type': self.TYPE_PERMISSIONS
                    })

        except Exception as e:
            logger.warning(f"提取权限失败 (app_version_id={app_version.id}): {e}")
        return permissions

    def _extract_intent_actions(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """提取Intent Actions (类型9)"""
        actions = []
        try:
            android_manifest = getattr(app_version, 'android_manifest', None)
            if not android_manifest:
                return actions

            root = ET.fromstring(android_manifest)

            # 查找所有intent-filter中的action
            for intent_filter in root.findall('.//intent-filter'):
                for action in intent_filter.findall('action'):
                    name = action.get('{http://schemas.android.com/apk/res/android}name')
                    if name:
                        actions.append({
                            'app_version_id': app_version.id,
                            'package_name': name,
                            'type': self.TYPE_INTENT_ACTIONS
                        })

        except Exception as e:
            logger.warning(f"提取Intent Actions失败 (app_version_id={app_version.id}): {e}")
        return actions

    def _extract_metadata(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """提取元数据 (类型8)"""
        metadata = []
        try:
            android_manifest = getattr(app_version, 'android_manifest', None)
            if not android_manifest:
                return metadata

            root = ET.fromstring(android_manifest)

            # 查找所有meta-data
            for meta in root.findall('.//meta-data'):
                name = meta.get('{http://schemas.android.com/apk/res/android}name')
                if name:
                    metadata.append({
                        'app_version_id': app_version.id,
                        'package_name': name,
                        'type': self.TYPE_METADATA
                    })

        except Exception as e:
            logger.warning(f"提取元数据失败 (app_version_id={app_version.id}): {e}")
        return metadata


def process_database_records(limit: int = 10) -> bool:
    """[DEPRECATED]
    分离式流程的数据库落表逻辑已废弃：不再向 `class_app_discovered_packages` 写入。
    合流请使用 `combine_analysis_flow.py`，在内存中完成提取并直接进入匹配。
    """
    logger.warning("[DEPRECATED] process_database_records 已废弃：不再写入 class_app_discovered_packages；请改用 combine_analysis_flow")
    raise NotImplementedError("Deprecated: writing to class_app_discovered_packages is removed. Use combine_analysis_flow instead.")


def process_txt_files(folder_path: str) -> bool:
    """处理文件夹中的txt文件"""
    try:
        # 查找所有txt文件
        txt_files = [f for f in os.listdir(folder_path) if f.endswith('.txt') and f.startswith('class_example')]

        if len(txt_files) != 3:
            logger.error(f"期望找到3个txt文件，实际找到{len(txt_files)}个")
            return False

        logger.info(f"找到{len(txt_files)}个txt文件: {txt_files}")

        # 初始化黑名单过滤器与数据提取器（仅用于混淆检测复用）
        blacklist_filter = BlackListFilter(os.path.join(folder_path, 'class_black_list.config'))
        data_extractor = AppDataExtractor(blacklist_filter)

        results = []

        # 处理每个文件
        for txt_file in sorted(txt_files):
            file_path = os.path.join(folder_path, txt_file)
            logger.info(f"处理文件: {txt_file}")

            try:
                # 读取包名列表
                with open(file_path, 'r', encoding='utf-8') as f:
                    packages = json.load(f)

                logger.info(f"从{txt_file}读取到{len(packages)}个包名")

                # 统一包名格式（将斜杠替换为点号）
                formatted_packages = [pkg.replace('/', '.') for pkg in packages]

                # 仅进行黑名单 + 混淆过滤（与数据库流程一致）
                filtered_packages = []
                blacklisted_count = 0
                obfuscated_count = 0
                for package in formatted_packages:
                    if blacklist_filter.is_blacklisted(package):
                        blacklisted_count += 1
                        logger.debug(f"TXT包名过滤掉黑名单包: {package}")
                        continue
                    if data_extractor._is_obfuscated_package_name(package):
                        obfuscated_count += 1
                        logger.debug(f"TXT包名过滤掉疑似混淆包: {package}")
                        continue
                    filtered_packages.append(package)

                logger.info(
                    f"{txt_file}: 黑名单过滤 {blacklisted_count} 个，混淆过滤 {obfuscated_count} 个，最终 {len(filtered_packages)} 个"
                )

                # 记录结果（保持简单：仅输出过滤后的包名）
                file_result = {
                    'file': txt_file,
                    'total_packages': len(packages),
                    'filtered_packages': filtered_packages,
                }
                results.append(file_result)

                logger.info(f"{txt_file} 处理完成，保留 {len(filtered_packages)} 个包名")

            except Exception as e:
                logger.error(f"处理文件{txt_file}时出错: {e}")
                return False

        # 写入结果文件
        result_file = os.path.join(folder_path, 'result.txt')
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump({
                'status': 'success',
                'processed_files': len(txt_files),
                'timestamp': datetime.now().isoformat(),
                'results': results
            }, f, ensure_ascii=False, indent=2)

        logger.info(f"处理完成，结果已写入: {result_file}")
        return True

    except Exception as e:
        logger.error(f"处理过程中出现错误: {e}")
        return False


def main():
    """主函数"""
    folder_path = "/home/<USER>/dev/tenyy-dind/tenyy/src/apkinfo_analysis/apk_analysis_flow"

    logger.info("开始APK分析流程")
    
    # 优先处理数据库记录
    success_db = process_database_records()
    
    # 然后处理txt文件
    success_txt = process_txt_files(folder_path)

    if success_db and success_txt:
        logger.info("APK分析流程成功完成")
    else:
        logger.error("APK分析流程失败")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())