# -*- coding: utf-8 -*-
"""
combine_analysis_flow.py

将 apk 分析提取与 sdk 匹配合并为一个 Prefect 本地 Flow：
- 第一步（提取）：仅在内存中提取各类信号（含 DEX 包名过滤），不写库；
- 第二步（匹配）：消费内存数据，执行 SDK 匹配与潜在 SDK 分析，并写入数据库。

默认每批处理 1000 条记录；每批完成后自动继续，直到数据库没有待处理任务为止。
"""
import os
import sys
import json
import logging
from datetime import datetime
from dataclasses import dataclass
from typing import List, Dict, Optional, Any

from sqlalchemy import create_engine, or_, and_, func, cast
from sqlalchemy.types import String
from sqlalchemy.orm import sessionmaker

# Prefect 本地 Flow/Task
from prefect import flow, task, get_run_logger
from prefect.artifacts import create_markdown_artifact, create_table_artifact

# 修正路径，使得可直接导入项目内部模块
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

# 复用现有组件
from tenyy.src.apkinfo_analysis.apk_analysis_flow.apk_analysis_flow import (
    BlackListFilter,
    AppDataExtractor,
)
from tenyy.src.models.app_version import AppVersion
from tenyy.src.models.class_app_version_sdks import class_AppVersionSDK
from tenyy.src.config.settings import Settings
from tenyy.src.apkinfo_analysis.apk_analysis_flow.sdk_match_flow import SDKMatcher


logger = logging.getLogger(__name__)

# 全局 Engine / Session 工厂，避免每个任务创建独立连接池
_ENGINE = None
_SESSION_FACTORY = None


@dataclass
class InMemoryDiscoveredPackage:
    """轻量的内存对象，用于承载提取到的特征（字段与历史中间表的常用属性对齐）。"""
    app_version_id: int
    package_name: str
    type: str  # 与现有实现保持字符串类型（默认 '5' 为 DEX 包）


# 不再传递 ORM 对象或 Stub，统一传递 ID，在任务内部重新查询 ORM 实例


def _get_engine():
    global _ENGINE
    if _ENGINE is not None:
        return _ENGINE
    settings = Settings()
    # 可通过环境变量覆盖连接池参数
    pool_size = int(os.getenv('SQL_POOL_SIZE', '5'))
    max_overflow = int(os.getenv('SQL_MAX_OVERFLOW', '5'))
    pool_recycle = int(os.getenv('SQL_POOL_RECYCLE', '1800'))
    pool_timeout = int(os.getenv('SQL_POOL_TIMEOUT', '30'))
    _ENGINE = create_engine(
        settings.DATABASE_URL,
        pool_size=pool_size,
        max_overflow=max_overflow,
        pool_pre_ping=True,
        pool_recycle=pool_recycle,
        pool_timeout=pool_timeout,
        future=True,
    )
    return _ENGINE


def _build_session_factory():
    global _SESSION_FACTORY
    if _SESSION_FACTORY is not None:
        return _SESSION_FACTORY
    engine = _get_engine()
    _SESSION_FACTORY = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return _SESSION_FACTORY


@task
def claim_app_version_ids(limit: int = 10) -> List[int]:
    """占坑式获取待处理 AppVersion ID（行级锁 + processing 标记）。

    使用 FOR UPDATE SKIP LOCKED 防止多进程重复领取；同时将 analysis_result.processing 置为 true。
    """
    plog = get_run_logger()
    SessionLocal = _build_session_factory()
    with SessionLocal() as session:
        plog.info(f"占坑领取最多 {limit} 条记录（packages_class 非空，未标记 sdk_identified=true）…")
        # 构造基础查询
        q = (
            session.query(AppVersion)
            .filter(
                AppVersion.analyze_status.in_(["done", "completed"]),
                AppVersion.packages_class.isnot(None),
                func.length(cast(AppVersion.packages_class, String)) > 2,
                cast(AppVersion.packages_class, String) != '',
                cast(AppVersion.packages_class, String) != '[]',
                cast(AppVersion.packages_class, String) != '{}',
                or_(
                    AppVersion.analysis_result.is_(None),
                    AppVersion.analysis_result['sdk_identified'].astext != 'true',
                ),
                # 非正在处理（processing != true）
                or_(
                    AppVersion.analysis_result.is_(None),
                    AppVersion.analysis_result['processing'].astext != 'true',
                ),
            )
            .order_by(func.length(cast(AppVersion.packages_class, String)).desc(), AppVersion.id.desc())
        )

        # 加行级锁并跳过已被其他事务锁住的行
        q = q.with_for_update(skip_locked=True)
        rows: List[AppVersion] = q.limit(limit).all()
        ids: List[int] = []
        for av in rows:
            ids.append(int(av.id))
            ar = av.analysis_result or {}
            if not isinstance(ar, dict):
                try:
                    ar = json.loads(ar) if ar else {}
                except Exception:
                    ar = {}
            ar['processing'] = True
            av.analysis_result = ar
        session.commit()
        plog.info(f"成功占坑 {len(ids)} 条记录: {ids}")
        return ids


@task
def extract_discovered_packages(app_version_id: int) -> List[InMemoryDiscoveredPackage]:
    """使用 AppDataExtractor 提取各类信号（在任务内按 ID 查询 ORM），返回内存中的包清单。"""
    plog = get_run_logger()
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # 调试开关：允许临时禁用黑名单与混淆过滤
    disable_blacklist = os.getenv('COMBINE_DISABLE_BLACKLIST', '0') == '1'
    disable_obf = os.getenv('COMBINE_DISABLE_OBFUSCATION', '0') == '1'

    if disable_blacklist:
        blacklist_filter = None
        plog.warning("[DEBUG] 已禁用黑名单过滤 (COMBINE_DISABLE_BLACKLIST=1)")
    else:
        blacklist_filter = BlackListFilter(os.path.join(script_dir, 'class_black_list.config'))

    extractor = AppDataExtractor(blacklist_filter)
    if disable_obf:
        try:
            extractor._is_obfuscated_package_name = lambda _name: False  # type: ignore
            plog.warning("[DEBUG] 已禁用混淆检测 (COMBINE_DISABLE_OBFUSCATION=1)")
        except Exception:
            pass

    # 在任务内重新查询 ORM 实例
    SessionLocal = _build_session_factory()
    with SessionLocal() as session:
        app_version: AppVersion = session.query(AppVersion).get(app_version_id)  # type: ignore
        if app_version is None:
            plog.warning(f"AppVersion {app_version_id} 不存在，跳过。")
            return []

        # 关键字段快照
        try:
            pkc = app_version.packages_class
            pkc_len = len(pkc) if isinstance(pkc, (list, dict, str)) else -1
        except Exception:
            pkc_len = -1
        try:
            mani = app_version.android_manifest
            mani_len = len(mani) if isinstance(mani, str) else -1
        except Exception:
            mani_len = -1
        try:
            libs = app_version.lib_files
            libs_len = len(libs) if isinstance(libs, (list, str)) else -1
        except Exception:
            libs_len = -1
        plog.info(f"[SNAPSHOT] id={app_version_id} packages_class_len={pkc_len} manifest_len={mani_len} lib_files_len={libs_len}")

        extracted = extractor.extract_all_data(app_version)
    # 转换为 InMemoryDiscoveredPackage 列表
    pkgs: List[InMemoryDiscoveredPackage] = []
    type_counter = {}
    for item in extracted:
        pkgs.append(
            InMemoryDiscoveredPackage(
                app_version_id=int(item.get('app_version_id')),
                package_name=str(item.get('package_name') or ''),
                type=str(item.get('type', '5')),
            )
        )
        t = str(item.get('type', '5'))
        type_counter[t] = type_counter.get(t, 0) + 1
    plog.info(
        f"AppVersion {app_version_id} 内存提取完成，共 {len(pkgs)} 条，类型分布={type_counter}"
    )
    return pkgs


@task
def match_and_persist(app_version_id: int, packages: List[InMemoryDiscoveredPackage]) -> Dict[str, Any]:
    """调用 SDKMatcher 的匹配能力并写入数据库。"""
    plog = get_run_logger()
    matcher = SDKMatcher()

    # 获取 SDK 知识库
    kb = matcher.get_sdk_knowledge_base()

    # 直接使用内存数据进行匹配
    matched_results, unmatched_packages = matcher.match_packages_with_sdk_knowledge(packages, kb)

    # 过滤下级包并分析潜在 SDK
    filtered_packages = matcher.filter_child_packages(matched_results, unmatched_packages)
    potential_sdks = matcher.analyze_potential_sdks(filtered_packages)

    # 写入数据库（仅第二步持久化）；空结果也需要清理 processing 标记
    if not matched_results and not potential_sdks:
        plog.info(f"AppVersion {app_version_id} 匹配与潜在均为空，跳过写库，仅清理 processing 标记。")
        try:
            SessionLocal = _build_session_factory()
            with SessionLocal() as session:
                av: AppVersion = session.query(AppVersion).get(app_version_id)  # type: ignore
                if av is not None:
                    ar = av.analysis_result or {}
                    if not isinstance(ar, dict):
                        try:
                            ar = json.loads(ar) if ar else {}
                        except Exception:
                            ar = {}
                    ar['processing'] = False
                    av.analysis_result = ar
                    session.commit()
        except Exception as e:
            plog.warning(f"清理 AppVersion {app_version_id} processing 标记失败: {e}")
        return {"app_version_id": app_version_id, "matched": 0, "potential": 0}
    matcher.save_results(app_version_id, matched_results, potential_sdks)
    plog.info(f"AppVersion {app_version_id} 已保存结果：matched={len(matched_results)}, potential={len(potential_sdks)}")

    # 成功写库后，标记 AppVersion.analysis_result.sdk_identified = true，并记录时间戳
    try:
        SessionLocal = _build_session_factory()
        with SessionLocal() as session:
            av: AppVersion = session.query(AppVersion).get(app_version_id)  # type: ignore
            if av is None:
                plog.warning(f"AppVersion {app_version_id} 不存在，无法更新 analysis_result 标记。")
                # 仍返回统计（退化为内存估计）
                return {"app_version_id": app_version_id, "matched": len(matched_results), "potential": len(potential_sdks)}
            ar = av.analysis_result or {}
            # 兼容非字典场景（理论上 JSONB 应该是 dict）
            if not isinstance(ar, dict):
                try:
                    ar = json.loads(ar) if ar else {}
                except Exception:
                    ar = {}
            # 清理 processing 标记并设置 identified
            ar['processing'] = False
            ar['sdk_identified'] = True
            ar['sdk_identified_at'] = datetime.utcnow().isoformat()
            av.analysis_result = ar
            session.commit()
            plog.info(f"AppVersion {app_version_id} 已更新 analysis_result.sdk_identified=true")

            # 以数据库最终写入为准统计计数
            db_matched = session.query(class_AppVersionSDK).filter(
                class_AppVersionSDK.app_version_id == app_version_id,
                class_AppVersionSDK.is_potential.is_(False),
            ).count()
            db_potential = session.query(class_AppVersionSDK).filter(
                class_AppVersionSDK.app_version_id == app_version_id,
                class_AppVersionSDK.is_potential.is_(True),
            ).count()
            return {"app_version_id": app_version_id, "matched": int(db_matched), "potential": int(db_potential)}
    except Exception as e:
        plog.error(f"更新 AppVersion {app_version_id} 的 analysis_result 失败: {e}")
    # 兜底：若上方统计失败，退化为内存估计
    return {"app_version_id": app_version_id, "matched": len(matched_results), "potential": len(potential_sdks)}


@flow(name="combine_apk_analysis_and_sdk_match")
def combine_analysis_flow(limit: int = 1000, max_workers: int = 4):
    """本地 Prefect Flow：提取(内存) + 匹配(落库)。

    将以“占坑领取 limit 条”的方式循环处理，直至没有可处理记录。
    """
    rlog = get_run_logger()
    rlog.info("启动 combine_analysis_flow")

    # 1) 循环获取并处理 AppVersion（占坑式 + 分批并发）
    summary_rows: List[Dict[str, Any]] = []
    total_claimed = 0
    while True:
        # 领取一批待处理记录
        app_version_ids = claim_app_version_ids.submit(limit)
        ids = app_version_ids.result()
        if not ids:
            rlog.info("数据库没有更多待处理任务，流程结束。")
            break

        total_claimed += len(ids)
        rlog.info(f"本批将处理 {len(ids)} 条记录（累计领取 {total_claimed}），分批并发 max_workers={max_workers}")

        for i in range(0, len(ids), max_workers):
            batch = ids[i:i + max_workers]
            futures = []
            for av_id in batch:
                pkgs_fut = extract_discovered_packages.submit(av_id)
                res = match_and_persist.submit(av_id, pkgs_fut)
                futures.append(res)
            # 等待该批完成并收集结果
            for f in futures:
                try:
                    item = f.result()
                    if isinstance(item, dict):
                        summary_rows.append(item)
                except Exception as e:
                    rlog.error(f"收集结果出错: {e}")

    # 3) 发布总览 Artifact（表格 + Markdown 汇总）
    try:
        create_table_artifact(
            key="combine-summary",
            table=summary_rows,
            description=f"combine_analysis_flow 总览（共 {len(summary_rows)} 条）"
        )
        total_matched = sum(int(r.get("matched", 0)) for r in summary_rows)
        total_potential = sum(int(r.get("potential", 0)) for r in summary_rows)
        create_markdown_artifact(
            key="combine-summary-md",
            markdown=(
                f"## Combine 分析汇总\n\n"
                f"- 处理记录数: {len(summary_rows)}\n\n"
                f"- 总匹配SDK数: {total_matched}\n\n"
                f"- 总潜在SDK数: {total_potential}\n\n"
                f"- 生成时间: {datetime.utcnow().isoformat()}Z\n"
            )
        )
    except Exception:
        pass

    rlog.info("combine_analysis_flow 完成")


def main():
    import argparse
    parser = argparse.ArgumentParser(description="Combine APK analysis and SDK match flow (local Prefect)")
    parser.add_argument("--limit", type=int, default=1000, help="每批领取处理条数，默认 1000")
    args = parser.parse_args()

    # 直接本地执行 Prefect Flow
    combine_analysis_flow(args.limit)


if __name__ == "__main__":
    sys.exit(main() or 0)
