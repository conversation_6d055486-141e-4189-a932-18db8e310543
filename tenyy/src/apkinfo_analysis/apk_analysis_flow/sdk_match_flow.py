# coding: utf-8
"""
SDK匹配流程
"""
import os
import sys
import json
import logging
import re
from typing import List, Dict, Tuple, Any
from collections import defaultdict
import xml.etree.ElementTree as ET

# 添加项目根目录到sys.path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
    logging.info(f"添加项目根目录到sys.path: {project_root}")

from sqlalchemy.orm import sessionmaker, joinedload
from sqlalchemy import create_engine, and_
from tenyy.src.config.settings import settings
from tenyy.src.models.class_sdk_knowledge_base import class_SDKKnowledgeBase
from tenyy.src.models.class_app_version_sdks import class_AppVersionSDK
from tenyy.src.models.app_version import AppVersion
from tenyy.src.models.store_app import StoreApp
from tenyy.src.models.app import App

logger = logging.getLogger(__name__)

# 模块级单例 Engine/Session，避免并发创建过多连接池
_ENGINE = None
_SESSION_FACTORY = None

def _get_engine():
    global _ENGINE
    if _ENGINE is not None:
        return _ENGINE
    # 连接池参数支持通过环境变量覆盖
    pool_size = int(os.getenv('SQL_POOL_SIZE', '5'))
    max_overflow = int(os.getenv('SQL_MAX_OVERFLOW', '5'))
    pool_recycle = int(os.getenv('SQL_POOL_RECYCLE', '1800'))
    pool_timeout = int(os.getenv('SQL_POOL_TIMEOUT', '30'))
    _ENGINE = create_engine(
        settings.DATABASE_URL,
        pool_size=pool_size,
        max_overflow=max_overflow,
        pool_pre_ping=True,
        pool_recycle=pool_recycle,
        pool_timeout=pool_timeout,
        future=True,
    )
    return _ENGINE

def _get_session_factory():
    global _SESSION_FACTORY
    if _SESSION_FACTORY is not None:
        return _SESSION_FACTORY
    engine = _get_engine()
    _SESSION_FACTORY = sessionmaker(bind=engine)
    return _SESSION_FACTORY

# 进程内缓存（不跨进程共享）
_KB_CACHE: List[class_SDKKnowledgeBase] = []
_RULE_BUCKETS_CACHE: Dict[str, Dict[str, list]] = {}
_BLACKLIST_SINGLETON = None

def _get_blacklist_filter():
    global _BLACKLIST_SINGLETON
    if _BLACKLIST_SINGLETON is None:
        blacklist_config_path = os.path.join(os.path.dirname(__file__), 'class_black_list.config')
        from tenyy.src.apkinfo_analysis.apk_analysis_flow.apk_analysis_flow import BlackListFilter
        _BLACKLIST_SINGLETON = BlackListFilter(blacklist_config_path)
    return _BLACKLIST_SINGLETON

def _load_kb_once(engine):
    global _KB_CACHE
    if _KB_CACHE:
        return _KB_CACHE
    Session = sessionmaker(bind=engine)
    with Session() as s:
        _KB_CACHE = s.query(class_SDKKnowledgeBase).all()
    logger.info(f"[CACHE] SDK知识库加载完成: {_KB_CACHE and len(_KB_CACHE)} 条")
    return _KB_CACHE

def _build_rule_buckets_once(kb: List[class_SDKKnowledgeBase]) -> Dict[str, Dict[str, list]]:
    """按 detection_type 分桶的规则缓存：{'prefix': {type: [(prefix,sdk)]}, 'regex': {type: [(compiled, sdk, raw_pattern)]}}"""
    global _RULE_BUCKETS_CACHE
    if _RULE_BUCKETS_CACHE:
        return _RULE_BUCKETS_CACHE
    prefix_buckets: Dict[str, list] = defaultdict(list)
    regex_buckets: Dict[str, list] = defaultdict(list)
    invalid_regex_count = 0
    for sdk in kb:
        try:
            is_regex = bool(getattr(sdk, 'is_regex_rule', False))
        except Exception:
            is_regex = False
        dtype = str(getattr(sdk, 'detection_type', '5') or '5')
        if is_regex:
            raw_pattern = getattr(sdk, 'package_prefix', '') or ''
            if not raw_pattern:
                invalid_regex_count += 1
                continue
            try:
                compiled = re.compile(raw_pattern)
            except Exception:
                invalid_regex_count += 1
                continue
            regex_buckets[dtype].append((compiled, sdk, raw_pattern))
        else:
            prefix = getattr(sdk, 'package_prefix', '') or ''
            if prefix:
                prefix_buckets[dtype].append((prefix, sdk))
    # 各类型前缀按长度降序
    for t, arr in prefix_buckets.items():
        arr.sort(key=lambda x: len(x[0]), reverse=True)
    _RULE_BUCKETS_CACHE = {"prefix": prefix_buckets, "regex": regex_buckets}
    logger.info(f"[CACHE] 规则分桶完成: prefix_types={len(prefix_buckets)}, regex_types={len(regex_buckets)}, invalid_regex={invalid_regex_count}")
    return _RULE_BUCKETS_CACHE


class SDKMatcher:
    """SDK匹配器"""
    
    def __init__(self):
        """初始化SDK匹配器"""
        # 复用模块级数据库引擎
        self.engine = _get_engine()
        # 复用进程内黑名单
        self.blacklist_filter = _get_blacklist_filter()
    
    def _get_app_package_name(self, app_version_id: int) -> str:
        """获取应用自身包名（优先 DB 关系链，其次 Manifest）。
        优先通过 AppVersion -> StoreApp -> App.id（Bundle ID）获取；若失败则解析 AndroidManifest 根 package 属性。
        失败返回空字符串。
        """
        Session = _get_session_factory()
        with Session() as s:
            # 1) DB 关系链
            av: AppVersion = s.query(AppVersion).get(app_version_id)  # type: ignore
            if not av:
                return ""
            try:
                if getattr(av, 'store_app_id', None) is not None:
                    sa: StoreApp = s.query(StoreApp).get(av.store_app_id)  # type: ignore
                    # 仅使用 store_app.app_id 作为私有前缀
                    if sa and getattr(sa, 'app_id', None):
                        app_pkg = str(sa.app_id)
                        logger.debug(f"[APP-PKG] av={app_version_id} store_app_id={av.store_app_id} app_id={app_pkg}")
                        return app_pkg
                    else:
                        logger.debug(f"[APP-PKG] av={app_version_id} store_app_id={av.store_app_id} but no app_id on store_app")
                else:
                    logger.debug(f"[APP-PKG] av={app_version_id} has no store_app_id")
            except Exception as e:
                logger.debug(f"[APP-PKG] av={app_version_id} resolve error: {e}")
                return ""
            # 无可用 app_id 则返回空
            return ""
    
    def get_database_session(self):
        """获取数据库会话"""
        Session = _get_session_factory()
        return Session()
    
    def get_sample_apps(self, count: int = 10) -> List[int]:
        """获取示例应用版本ID列表（不依赖 discovered_packages 表）。"""
        session = self.get_database_session()
        try:
            rows = (
                session.query(AppVersion.id)
                .filter(AppVersion.analyze_status.in_(["done", "completed"]))
                .order_by(AppVersion.id.desc())
                .limit(count)
                .all()
            )
            ids = [int(r[0]) for r in rows]
            logger.info(f"获取到 {len(ids)} 个示例应用版本ID")
            return ids
        finally:
            session.close()
    
    def get_discovered_packages(self, app_version_id: int) -> List[Any]:
        """[DEPRECATED] 分离式流程遗留接口。合流流程不再从表读取候选包。
        请直接传入内存中的候选包对象列表（需包含 package_name、type、app_version_id 属性）。"""
        raise NotImplementedError("Deprecated: combine flow does not read class_app_discovered_packages. Pass in-memory packages instead.")
    
    def get_sdk_knowledge_base(self) -> List[class_SDKKnowledgeBase]:
        """获取SDK知识库（进程内缓存）"""
        return _load_kb_once(self.engine)
    
    def match_packages_with_sdk_knowledge(self, 
                                        packages: List[Any], 
                                        sdk_knowledge_base: List[class_SDKKnowledgeBase]) -> Tuple[List[Dict], List[Any]]:
        """将包与SDK知识库进行匹配（前缀优先，支持正则规则）"""
        matched_results = []
        unmatched_packages = []

        # 使用缓存的规则分桶
        buckets = _build_rule_buckets_once(sdk_knowledge_base)
        prefix_buckets = buckets["prefix"]
        regex_buckets = buckets["regex"]

        # 统计规则数量用于诊断
        prefix_rules_count = sum(len(v) for v in prefix_buckets.values())
        regex_rules_count = sum(len(v) for v in regex_buckets.values())
        logger.info(
            f"知识库规则统计: total={len(sdk_knowledge_base)}, prefix_rules={prefix_rules_count}, regex_rules={regex_rules_count}"
        )

        prefix_hit = 0
        regex_hit = 0

        # 统计包类型分布（用于诊断 regex 未命中的原因）
        pkg_type_counter = defaultdict(int)
        for p in packages:
            t = getattr(p, 'type', None)
            t = str(t) if t is not None else '5'
            pkg_type_counter[t] += 1
        logger.info(f"待匹配包类型分布: {dict(pkg_type_counter)}")

        for package in packages:
            # 显式获取对象属性值
            package_name = getattr(package, 'package_name', '')
            package_name = str(package_name) if package_name is not None else ""
            # 统一获取包类型用于类型对齐（默认 '5'）
            pkg_type = getattr(package, 'type', None)
            pkg_type = str(pkg_type) if pkg_type is not None else '5'

            # 跳过空包名
            if not package_name:
                continue

            matched = False

            # 1) 前缀匹配（仅扫描相同类型桶）
            for prefix, sdk in prefix_buckets.get(pkg_type, []):
                if package_name.startswith(prefix):
                    sdk_type = getattr(sdk, 'detection_type', '5')
                    matched_results.append({
                        'app_version_id': package.app_version_id,
                        'sdk_package_prefix': prefix,
                        'sdk_knowledge_base_id': sdk.id,
                        'match_type': 'prefix',
                        'type': str(sdk_type) if sdk_type is not None else '5'
                    })
                    prefix_hit += 1
                    matched = True
                    break

            # 2) 若未命中，再尝试正则匹配（仅扫描相同类型桶）
            if not matched:
                for compiled, sdk, raw_pattern in regex_buckets.get(pkg_type, []):
                    try:
                        if compiled.search(package_name):
                            sdk_type = getattr(sdk, 'detection_type', '5')
                            matched_results.append({
                                'app_version_id': package.app_version_id,
                                'sdk_package_prefix': getattr(sdk, 'package_prefix', '') or '',
                                'sdk_knowledge_base_id': sdk.id,
                                'match_type': 'regex',
                                'type': str(sdk_type) if sdk_type is not None else '5'
                            })
                            regex_hit += 1
                            matched = True
                            break
                    except Exception as e:
                        logger.debug(f"正则匹配异常(id={getattr(sdk,'id',None)}): pattern={raw_pattern}, pkg={package_name}, err={e}")

            if not matched:
                unmatched_packages.append(package)

        logger.info(
            f"匹配到 {len(matched_results)} 个已知SDK包（prefix={prefix_hit}, regex={regex_hit}），{len(unmatched_packages)} 个未匹配包"
        )

        # 调试：环境变量开启详细正则排查
        try:
            debug_regex = os.getenv('SDK_MATCH_DEBUG_REGEX', '0') == '1'
        except Exception:
            debug_regex = False
        if debug_regex:
            # 抽样打印各类型前若干包名
            try:
                type_samples: Dict[str, List[str]] = defaultdict(list)
                for p in packages:
                    if len(type_samples) > 10:
                        break
                    t = str(getattr(p, 'type', '5') or '5')
                    name = str(getattr(p, 'package_name', '') or '')
                    if name and len(type_samples[t]) < 5:
                        type_samples[t].append(name)
                logger.info(f"[DEBUG] 包名抽样(每类型<=5): {dict(type_samples)}")
            except Exception as e:
                logger.debug(f"[DEBUG] 采样包名出错: {e}")

            # 若未命中正则，尝试对前5条正则规则进行小规模扫描
            try:
                if regex_hit == 0 and regex_rules:
                    logger.info("[DEBUG] 开始对前5条正则规则做小规模扫描以定位未命中原因…")
                    # 构建按类型分组的候选集（每类最多500个）
                    candidates_by_type: Dict[str, List[str]] = defaultdict(list)
                    for p in packages:
                        t = str(getattr(p, 'type', '5') or '5')
                        name = str(getattr(p, 'package_name', '') or '')
                        if name and len(candidates_by_type[t]) < 500:
                            candidates_by_type[t].append(name)

                    for idx, (compiled, sdk, raw_pattern) in enumerate(regex_rules[:5]):
                        rule_type = getattr(sdk, 'detection_type', None)
                        tkey = str(rule_type) if rule_type is not None else None
                        pool = []
                        if tkey is None:
                            # 无类型限制则取所有类型的前若干候选
                            for lst in candidates_by_type.values():
                                pool.extend(lst[:50])
                        else:
                            pool = candidates_by_type.get(tkey, [])[:200]
                        hit = None
                        for name in pool:
                            try:
                                if compiled.search(name):
                                    hit = name
                                    break
                            except Exception:
                                pass
                        if hit:
                            logger.info(f"[DEBUG] 正则规则#{idx+1} 命中: pattern='{raw_pattern}', type={tkey}, example='{hit}'")
                        else:
                            logger.info(f"[DEBUG] 正则规则#{idx+1} 未命中样本: pattern='{raw_pattern}', type={tkey}, pool_size={len(pool)}")
            except Exception as e:
                logger.debug(f"[DEBUG] 正则小规模扫描出错: {e}")

        return matched_results, unmatched_packages
    
    def filter_child_packages(self, 
                            matched_results: List[Dict], 
                            unmatched_packages: List[Any]) -> List[Any]:
        """过滤掉已匹配SDK的下级包"""
        # 构建已匹配的包名前缀集合
        matched_prefixes = {result['sdk_package_prefix'] for result in matched_results}
        
        filtered_packages = []
        for package in unmatched_packages:
            # 显式获取对象属性值
            package_name = getattr(package, 'package_name', '')
            package_name = str(package_name) if package_name is not None else ""
            
            # 跳过空包名
            if not package_name:
                continue
            
            # 检查是否为已匹配包的下级包
            should_filter = False
            for prefix in matched_prefixes:
                if package_name.startswith(prefix + '.'):
                    should_filter = True
                    break
            
            if not should_filter:
                filtered_packages.append(package)
                
        return filtered_packages
    
    def analyze_potential_sdks(self, unmatched_packages: List[Any]) -> List[Dict]:
        """
        分析潜在的SDK特征
        
        Args:
            unmatched_packages: 未匹配的包列表
            
        Returns:
            潜在SDK特征列表
        """
        if not unmatched_packages:
            return []
        
        # 直接纳入的本地库潜在项（type == '0'）
        # 说明：未被知识库识别的本地库无需层级合并，直接作为 potential 输出
        native_candidates = defaultdict(lambda: {'count': 0, 'packages': []})
        try:
            for p in unmatched_packages:
                t = str(getattr(p, 'type', '5') or '5')
                if t == '0':  # Native Libraries
                    name = str(getattr(p, 'package_name', '') or '')
                    if not name:
                        continue
                    native_candidates[name]['count'] += 1
                    native_candidates[name]['packages'].append(p)
        except Exception as e:
            logger.debug(f"收集本地库潜在项出错: {e}")
        native_potentials: List[Dict] = []
        for name, data in native_candidates.items():
            native_potentials.append({
                'sdk_package_prefix': name,
                'signal_count': int(data.get('count', 0) or 0),
                'child_packages': json.dumps([], ensure_ascii=False),
                'packages': data.get('packages', []),
                'type': '0'
            })

        # 层级分解和频率统计
        level_count = defaultdict(int)  # 各层级出现次数
        level_children = defaultdict(set)  # 各层级的直接子级
        
        for package in unmatched_packages:
            # 显式获取对象属性值
            package_name = getattr(package, 'package_name', '')
            package_name = str(package_name) if package_name is not None else ""
            
            parts = package_name.split('.')
            # 生成所有可能的父级前缀
            for i in range(1, len(parts) + 1):
                prefix = '.'.join(parts[:i])
                level_count[prefix] += 1
                
                # 记录直接子级
                if i < len(parts):
                    child = parts[i]
                    level_children[prefix].add(child)
        
        # 提取候选特征（出现3次或以上），且统一排除仅有一段的前缀（避免单一包名）
        candidate_features = {}
        for level, count in level_count.items():
            if count < 3:
                continue
            segs = level.split('.')
            if len(segs) == 1:
                continue
            candidate_features[level] = count
        
        # 合并父级特征（同一父级下有3个或以上子级）
        potential_sdks = []
        for level, children in level_children.items():
            if len(children) >= 3 and level in candidate_features:
                # 记录该潜在SDK下的所有包
                sdk_packages = [p for p in unmatched_packages 
                              if (getattr(p, 'package_name', '') or "").startswith(level + '.')]
                
                # 获取类型信息，如果所有包类型相同则使用该类型，否则默认为DEX包类型(5)
                types = [getattr(p, 'type', '5') for p in sdk_packages]
                sdk_type = types[0] if len(set(types)) == 1 and types[0] is not None else '5'
                
                potential_sdks.append({
                    'sdk_package_prefix': level,
                    'signal_count': len(sdk_packages),
                    'child_packages': json.dumps(list(children), ensure_ascii=False),
                    'packages': sdk_packages,
                    'type': sdk_type
                })

        # 合并：自适应阈值 + 随机后缀识别 的层级合并
        # 思路：
        # 1) 统计 2..5 层前缀的直接子级特征，构建全局 child 特征分布
        # 2) 自适应判定“随机子级”（regex/长度/数字占比，基于全局分位数）
        # 3) 给每个前缀节点打分，score>=P75(score) 的节点作为合并点；更深者优先
        try:
            def quantile(arr: List[float], q: float) -> float:
                if not arr:
                    return 0.0
                a = sorted(arr)
                pos = q * (len(a) - 1)
                lo = int(pos)
                hi = min(lo + 1, len(a) - 1)
                if lo == hi:
                    return float(a[lo])
                frac = pos - lo
                return float(a[lo] * (1 - frac) + a[hi] * frac)

            def build_groups(depth: int) -> Dict[str, Dict]:
                groups: Dict[str, Dict] = {}
                for p in unmatched_packages:
                    name = str(getattr(p, 'package_name', '') or '')
                    if not name:
                        continue
                    parts = name.split('.')
                    if len(parts) < depth:
                        continue
                    key = '.'.join(parts[:depth])
                    g = groups.setdefault(key, {
                        'packages': [],
                        'types': [],
                        'children_counter': defaultdict(int),
                        'children_examples': defaultdict(list)
                    })
                    g['packages'].append(p)
                    g['types'].append(getattr(p, 'type', '5'))
                    if len(parts) >= depth + 1:
                        child = parts[depth]
                        g['children_counter'][child] += 1
                        if len(g['children_examples'][child]) < 3:
                            g['children_examples'][child].append(name)
                return groups

            # 统计 2..5 层
            all_groups_by_depth: Dict[int, Dict[str, Dict]] = {}
            for d in (2, 3, 4, 5):
                all_groups_by_depth[d] = build_groups(d)

            # 收集全局 child 特征
            all_child_lengths: List[int] = []
            all_child_digit_ratios: List[float] = []
            all_children: List[str] = []
            for d, groups in all_groups_by_depth.items():
                for _, g in groups.items():
                    for child in g['children_counter'].keys():
                        all_children.append(child)
                        L = len(child)
                        digits = sum(1 for ch in child if ch.isdigit())
                        ratio = (digits / L) if L > 0 else 0.0
                        all_child_lengths.append(L)
                        all_child_digit_ratios.append(ratio)

            # 全局分位数
            P_len80 = quantile([float(x) for x in all_child_lengths], 0.80)
            P_len70 = quantile([float(x) for x in all_child_lengths], 0.70)
            P_digit20 = quantile(all_child_digit_ratios, 0.20)
            P_digit80 = quantile(all_child_digit_ratios, 0.80)

            rand_patterns = [
                re.compile(r'^[a-z0-9]{6,}$'),
                re.compile(r'^[a-z]{3,}\d{2,}$'),
                re.compile(r'^[a-z0-9]+\d[a-z0-9]*$')
            ]

            def is_random_child(token: str) -> bool:
                if not token:
                    return False
                for pat in rand_patterns:
                    try:
                        if pat.match(token):
                            return True
                    except Exception:
                        pass
                L = len(token)
                digits = sum(1 for ch in token if ch.isdigit())
                ratio = (digits / L) if L > 0 else 0.0
                # 自适应：长度达到全局上分位，且数字占比处于中间区间，趋向随机
                if (L >= P_len80 and P_digit20 <= ratio <= P_digit80):
                    return True
                # 次优判定：长度至少达到 P70 也可视作弱随机
                if (L >= P_len70 and ratio > 0.15):
                    return True
                return False

            # 为每个节点计算分数
            node_infos: List[Dict] = []
            for depth, groups in all_groups_by_depth.items():
                # 不考虑合并到单段
                if depth < 2:
                    continue
                for key, g in groups.items():
                    unique_names = {str(getattr(p, 'package_name', '') or '') for p in g['packages']}
                    children = list(g['children_counter'].keys())
                    if len(unique_names) < 2 or len(children) < 2:
                        continue
                    random_flags = [is_random_child(c) for c in children]
                    random_ratio = (sum(1 for f in random_flags if f) / len(children)) if children else 0.0
                    # 评分：随机比例 + 规模奖励 + 轻微的 children 数奖励（避免引入 math.log）
                    score = (random_ratio * len(children)) + 0.3 * min(20, len(unique_names)) + (len(children) / 10.0)
                    node_infos.append({
                        'key': key,
                        'depth': depth,
                        'score': score,
                        'unique_names': unique_names,
                        'children': children,
                        'types': [t for t in g['types'] if t is not None],
                        'groups': g
                    })

            scores = [n['score'] for n in node_infos]
            if scores:
                threshold = quantile(scores, 0.75)
            else:
                threshold = 999999.0

            # 自适应强制阈值（针对“海量随机尾段”的场景）
            children_counts = [len(n['children']) for n in node_infos]
            forced_children_cut = max(8, int(quantile([float(c) for c in children_counts], 0.75))) if children_counts else 8

            # 选择候选：
            # 1) score >= P75
            # 2) 或者 children>=P75 并且随机子级占比>=0.6（强制选择）
            selected: List[Dict] = []
            node_infos.sort(key=lambda x: (x['depth'], x['score']), reverse=True)

            def is_ancestor(a: str, b: str) -> bool:
                return b.startswith(a + '.')

            chosen: Dict[str, Dict] = {}
            for n in node_infos:
                children = n['children']
                # 估算随机占比（复用 is_random_child）
                rand_ratio = 0.0
                if children:
                    rand_ratio = sum(1 for c in children if is_random_child(c)) / len(children)
                pass_score = (n['score'] >= threshold)
                pass_forced = (len(children) >= forced_children_cut and rand_ratio >= 0.6)
                if not (pass_score or pass_forced):
                    continue
                # 祖先/后代冲突解决：保留更深层；若同属祖先链且深度更浅但 signal 明显大（>2x）则替换
                to_remove = []
                conflict = False
                for ck, cv in list(chosen.items()):
                    if is_ancestor(ck, n['key']):
                        # 已选祖先，当前更深 -> 替换祖先
                        # 但若祖先的 uniques > 当前2倍，则保留祖先
                        if len(cv['unique_names']) > 2 * len(n['unique_names']):
                            conflict = True
                            break
                        to_remove.append(ck)
                    elif is_ancestor(n['key'], ck):
                        # 已选后代，当前更浅 -> 仅在当前 uniques > 后代2倍时替换
                        if len(n['unique_names']) > 2 * len(cv['unique_names']):
                            to_remove.append(ck)
                        else:
                            conflict = True
                            break
                if conflict:
                    continue
                for k in to_remove:
                    chosen.pop(k, None)
                chosen[n['key']] = n
                if pass_forced and not pass_score:
                    logger.info(f"[POT-MERGE/FORCED] prefix={n['key']} depth={n['depth']} children={len(children)} rand_ratio={rand_ratio:.2f} forced_cut={forced_children_cut}")

            selected = list(chosen.values())

            # 生成合并候选
            merged_candidates: List[Dict] = []
            for n in selected:
                key = n['key']
                g = n['groups']
                unique_names = list(n['unique_names'])
                child_keys = n['children']
                norm_types = [t for t in n['types']]
                sdk_type = norm_types[0] if len(set(norm_types)) == 1 and norm_types else '5'
                merged_candidates.append({
                    'sdk_package_prefix': key,
                    'signal_count': len(unique_names),
                    'child_packages': json.dumps(child_keys, ensure_ascii=False),
                    'packages': [p for p in g['packages']],
                    'type': sdk_type
                })
                logger.info(f"[POT-MERGE] prefix={key} depth={n['depth']} score={n['score']:.2f} children={len(child_keys)} uniques={len(unique_names)} threshold={threshold:.2f}")

            # 去重与裁剪：选择节点的祖先与后代都要去除
            merged_keys = {c['sdk_package_prefix'] for c in merged_candidates}
            def is_child_of_any(prefix: str, keys: set) -> bool:
                for mk in keys:
                    if prefix.startswith(mk + '.'):
                        return True
                return False
            def is_ancestor_of_any(prefix: str, keys: set) -> bool:
                for mk in keys:
                    if mk.startswith(prefix + '.'):
                        return True
                return False
            pruned = []
            for c in potential_sdks:
                pk = c['sdk_package_prefix']
                if is_child_of_any(pk, merged_keys):
                    continue
                if is_ancestor_of_any(pk, merged_keys):
                    continue
                pruned.append(c)

            final_list: List[Dict] = []
            final_list.extend(merged_candidates)
            for c in pruned:
                if c['sdk_package_prefix'] not in merged_keys:
                    final_list.append(c)

            # 族谱收敛：同一祖先链仅保留一个代表，优先深度3-4层、其次signal_count
            def depth_of(prefix: str) -> int:
                return len(prefix.split('.')) if prefix else 0

            # 建立快速查找
            by_prefix: Dict[str, Dict] = {c['sdk_package_prefix']: c for c in final_list}
            prefixes = list(by_prefix.keys())

            # 构造不相交的链集合：将存在祖先/后代关系的前缀聚成一组
            def is_related(a: str, b: str) -> bool:
                return a.startswith(b + '.') or b.startswith(a + '.')

            groups: List[List[str]] = []
            used = set()
            for p in sorted(prefixes, key=lambda x: (depth_of(x), x)):
                if p in used:
                    continue
                group = [p]
                used.add(p)
                for q in prefixes:
                    if q in used:
                        continue
                    if is_related(p, q):
                        group.append(q)
                        used.add(q)
                # 同组内还需互相扩展（传递闭包）
                changed = True
                while changed:
                    changed = False
                    for q in prefixes:
                        if q in used:
                            continue
                        if any(is_related(q, r) for r in group):
                            group.append(q)
                            used.add(q)
                            changed = True
                groups.append(group)

            def depth_weight(d: int) -> float:
                if 3 <= d <= 4:
                    return 1.0
                if d == 5:
                    return 0.8
                if d == 2:
                    return 0.6
                if d >= 6:
                    return 0.5
                return 0.4

            selected_prefixes: set = set()
            for grp in groups:
                # 选代表：rank = w(depth) * signal_count；若并列，选更接近[3,4]的
                best = None
                best_rank = -1.0
                for pf in grp:
                    item = by_prefix.get(pf)
                    if not item:
                        continue
                    d = depth_of(pf)
                    w = depth_weight(d)
                    sc = int(item.get('signal_count', 0) or 0)
                    rank = w * sc
                    if rank > best_rank:
                        best_rank = rank
                        best = pf
                    elif abs(rank - best_rank) < 1e-6:
                        # 平手：更接近目标深度3.5
                        def closeness(x):
                            return abs(depth_of(x) - 3.5)
                        if best is None or closeness(pf) < closeness(best):
                            best = pf
                if best:
                    selected_prefixes.add(best)

            final_compact = [by_prefix[p] for p in selected_prefixes]
            # 为了稳定输出，按前缀排序
            final_compact.sort(key=lambda x: x['sdk_package_prefix'])

            # 并入本地库潜在项（避免被覆盖丢失）
            existed = {x['sdk_package_prefix'] for x in final_compact}
            added_native = 0
            for n in native_potentials:
                if n['sdk_package_prefix'] not in existed:
                    final_compact.append(n)
                    added_native += 1
            # 为了稳定输出，按前缀排序
            final_compact.sort(key=lambda x: x['sdk_package_prefix'])

            logger.info(f"[POT-MERGE/COMPACT] 从 {len(final_list)} 收敛到 {len(final_compact)} 条")
            if added_native:
                logger.info(f"[POT-MERGE/NATIVE] 直接加入未识别本地库 {added_native} 条")
            potential_sdks = final_compact
        except Exception as e:
            logger.debug(f"潜在SDK合并阶段出错: {e}")

        return potential_sdks
    
    def save_results(self, 
                    app_version_id: int,
                    matched_results: List[Dict],
                    potential_sdks: List[Dict]):
        """
        保存匹配结果到class_AppVersionSDK表
        
        Args:
            app_version_id: 应用版本ID
            matched_results: 匹配结果
            potential_sdks: 潜在SDK特征
        """
        session = self.get_database_session()
        try:
            # 删除该应用版本已有的记录（更快）
            deleted_count = session.query(class_AppVersionSDK).filter(
                class_AppVersionSDK.app_version_id == app_version_id
            ).delete(synchronize_session=False)
            logger.info(f"删除了应用版本 {app_version_id} 的 {deleted_count} 条旧记录")

            inserted_combinations = set()
            to_insert: List[class_AppVersionSDK] = []

            # 已匹配SDK（去重）
            for result in matched_results:
                comb = (result['app_version_id'], result['sdk_package_prefix'] or "")
                if comb in inserted_combinations:
                    continue
                inserted_combinations.add(comb)
                to_insert.append(class_AppVersionSDK(
                    app_version_id=result['app_version_id'],
                    sdk_package_prefix=result['sdk_package_prefix'] or "",
                    sdk_knowledge_base_id=result['sdk_knowledge_base_id'],
                    match_type=result.get('match_type') or "",
                    is_potential=False,
                    type=result.get('type', '5'),
                    created_at=None,
                    updated_at=None,
                ))

            # 潜在SDK（黑名单过滤 + 去重）
            app_pkg = self._get_app_package_name(app_version_id)
            logger.debug(f"[SAVE] av={app_version_id} using app_pkg='{app_pkg}' for private check; potentials={len(potential_sdks)}")
            private_cnt = 0
            for potential in potential_sdks:
                prefix = potential['sdk_package_prefix'] or ""
                comb = (app_version_id, prefix)
                if comb in inserted_combinations:
                    continue
                if self.blacklist_filter.is_blacklisted(prefix):
                    logger.debug(f"过滤掉黑名单潜在SDK: {prefix}")
                    continue
                inserted_combinations.add(comb)
                # 判断是否属于本应用命名空间（仅基于 store_app.app_id 完整前缀）
                is_private = False
                if app_pkg:
                    if prefix == app_pkg or prefix.startswith(app_pkg + '.'):
                        is_private = True
                mt = 'private' if is_private else 'potential'
                if is_private:
                    private_cnt += 1
                    logger.debug(f"[PRIVATE] av={app_version_id} app_pkg='{app_pkg}' prefix='{prefix}' match_type=private")
                to_insert.append(class_AppVersionSDK(
                    app_version_id=app_version_id,
                    sdk_package_prefix=prefix,
                    sdk_knowledge_base_id=None,
                    match_type=mt,
                    is_potential=True,
                    signal_count=potential.get('signal_count'),
                    child_packages=potential.get('child_packages'),
                    type=potential.get('type', '5'),
                    created_at=None,
                    updated_at=None,
                ))

            if to_insert:
                session.bulk_save_objects(to_insert, preserve_order=False)
            session.commit()
            logger.info(f"应用版本 {app_version_id} 的匹配结果已保存：matched={sum(1 for r in to_insert if not r.is_potential)}, potential={sum(1 for r in to_insert if r.is_potential)}, private={private_cnt}")
        except Exception as e:
            logger.error(f"保存应用版本 {app_version_id} 的匹配结果时出错: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            session.rollback()
            raise
        finally:
            session.close()
    
    def process_app_version(self, app_version_id: int):
        """
        处理单个应用版本的SDK匹配
        
        Args:
            app_version_id: 应用版本ID
        """
        logger.info(f"开始处理应用版本 {app_version_id}")
        
        # 获取该应用版本的所有包
        packages = self.get_discovered_packages(app_version_id)
        logger.info(f"应用版本 {app_version_id} 共有 {len(packages)} 个包")
        
        if not packages:
            logger.info(f"应用版本 {app_version_id} 没有包数据")
            return
        
        # 获取SDK知识库
        sdk_knowledge_base = self.get_sdk_knowledge_base()
        logger.info(f"共获取到 {len(sdk_knowledge_base)} 条SDK知识库记录")
        
        # 包与SDK知识库匹配
        matched_results, unmatched_packages = self.match_packages_with_sdk_knowledge(packages, sdk_knowledge_base)
        logger.info(f"匹配到 {len(matched_results)} 个已知SDK包，{len(unmatched_packages)} 个未匹配包")
        
        # 过滤下级包
        filtered_packages = self.filter_child_packages(matched_results, unmatched_packages)
        logger.info(f"过滤后剩余 {len(filtered_packages)} 个包用于潜在SDK分析")
        
        # 分析潜在SDK
        potential_sdks = self.analyze_potential_sdks(filtered_packages)
        logger.info(f"发现 {len(potential_sdks)} 个潜在SDK特征")
        
        # 保存结果
        self.save_results(app_version_id, matched_results, potential_sdks)
        
        # 输出统计信息
        logger.info(f"应用版本 {app_version_id} 处理完成:")
        logger.info(f"  - 已识别SDK包: {len(matched_results)} 个")
        logger.info(f"  - 潜在SDK特征: {len(potential_sdks)} 个")
        for potential in potential_sdks:
            logger.info(f"    * {potential['sdk_package_prefix']} (信号数: {potential['signal_count']})")
    
    def run(self, app_count: int = 10):
        """
        运行SDK匹配流程
        
        Args:
            app_count: 处理的应用数量，默认10个
        """
        logger.info("开始SDK匹配流程")
        
        # 获取示例应用
        app_version_ids = self.get_sample_apps(app_count)
        logger.info(f"获取到 {len(app_version_ids)} 个示例应用")
        
        # 处理每个应用版本
        for app_version_id in app_version_ids:
            try:
                self.process_app_version(app_version_id)
            except Exception as e:
                logger.error(f"处理应用版本 {app_version_id} 时出错: {e}")
                import traceback
                logger.error(f"详细错误信息: {traceback.format_exc()}")
        
        logger.info("SDK匹配流程完成")

def main():
    """主函数"""
    matcher = SDKMatcher()
    matcher.run(10)

if __name__ == "__main__":
    main()