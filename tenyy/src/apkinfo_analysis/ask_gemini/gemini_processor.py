# -*- coding: utf-8 -*-
"""
轻量 JSON 解析工具：从 LLM 返回文本中尽力提取 JSON（对象或数组）。
- 兼容纯 JSON、Markdown 代码块（```json ... ```/``` ... ```）、
  以及带有前后解释性文本的场景。
- 仅提供 extract_json_from_response() 以满足现有调用方需求。
"""
from __future__ import annotations

import json
import re
from typing import Any, Optional

_CODE_FENCE_RE = re.compile(r"```(?:json)?\s*([\s\S]*?)\s*```", re.IGNORECASE)
_BRACE_OBJECT_RE = re.compile(r"\{[\s\S]*\}")
_BRACE_ARRAY_RE = re.compile(r"\[[\s\S]*\]")


def _try_json_loads(text: str) -> Optional[Any]:
    try:
        return json.loads(text)
    except Exception:
        return None


def _strip_bom(s: str) -> str:
    # 处理可能存在的 BOM
    if s and s[0] == "\ufeff":
        return s[1:]
    return s


def _extract_from_code_fence(text: str) -> Optional[Any]:
    for m in _CODE_FENCE_RE.finditer(text or ""):
        inner = (m.group(1) or "").strip()
        inner = _strip_bom(inner)
        data = _try_json_loads(inner)
        if data is not None:
            return data
    return None


def _extract_from_first_braces(text: str) -> Optional[Any]:
    # 优先尝试对象 {...}
    m = _BRACE_OBJECT_RE.search(text or "")
    if m:
        data = _try_json_loads(_strip_bom(m.group(0)))
        if data is not None:
            return data
    # 再尝试数组 [...]
    m = _BRACE_ARRAY_RE.search(text or "")
    if m:
        data = _try_json_loads(_strip_bom(m.group(0)))
        if data is not None:
            return data
    return None


def extract_json_from_response(text: Optional[str]) -> Optional[Any]:
    """尽力从 LLM 文本中提取 JSON（dict/list）。失败返回 None。

    策略（按顺序）：
    1) 直接 json.loads
    2) Markdown 代码块 ```json ... ``` 或 ``` ... ```
    3) 截取第一个 {...} 或 [...] 片段
    """
    if not text:
        return None

    s = _strip_bom(text.strip())

    # 1) 直接解析
    data = _try_json_loads(s)
    if isinstance(data, (dict, list)):
        return data

    # 2) 解析 Markdown 代码块
    data = _extract_from_code_fence(s)
    if isinstance(data, (dict, list)):
        return data

    # 3) 从首个括号对中提取
    data = _extract_from_first_braces(s)
    if isinstance(data, (dict, list)):
        return data

    return None
