# coding: utf-8
"""
DeepSeek 调用封装（就近读取 ask_gemini/key_config.json）

示例：
from tenyy.src.apkinfo_analysis.ask_gemini.deepseek_client import get_client
client = get_client()
resp = client.chat.completions.create(
    model="deepseek-chat",
    messages=[{"role": "system", "content": "You are a helpful assistant"}, {"role": "user", "content": "Hello"}],
    stream=False,
)
print(resp.choices[0].message.content)
"""
import json
import os
from typing import Optional

from openai import OpenAI

_BASE_URL = "https://api.deepseek.com"


def _read_key_from_local() -> Optional[str]:
    here = os.path.dirname(__file__)
    cfg_path = os.path.join(here, 'key_config.json')
    try:
        if os.path.exists(cfg_path):
            with open(cfg_path, 'r', encoding='utf-8') as f:
                data = json.load(f) or {}
                if isinstance(data, dict):
                    k = data.get('DEEPSEEK_API_KEY')
                    if k:
                        return str(k)
    except Exception:
        pass
    return None


def get_client(api_key: Optional[str] = None) -> OpenAI:
    key = api_key or _read_key_from_local()
    if not key:
        raise RuntimeError("DEEPSEEK_API_KEY 未配置，请在 ask_gemini/key_config.json 中填写或在代码中传入 api_key")
    return OpenAI(api_key=key, base_url=_BASE_URL)
