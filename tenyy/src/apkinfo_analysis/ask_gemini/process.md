# ask_gemini_potential 流程说明（含示例）

本文档描述 `tenyy/src/apkinfo_analysis/ask_gemini/ask_gemini_potential.py` 的整体处理流程，并通过一个具体示例解释“当前条直连”和“正则回填”的区别与执行顺序。

## 1. 入口与批次输入
- 入口：`main()`（见 `tenyy/src/apkinfo_analysis/ask_gemini/ask_gemini_potential.py`）
- 从数据库筛选候选 App（`_fetch_candidate_apps()`），再为每个 App 抽取其待处理包前缀（`_fetch_app_all_pkgs()`），合并成本批 `combined_pkgs` 列表后调用：
  - `process_batch(session, combined_pkgs, subcategories, ...)`

筛选条件核心（`_fetch_app_all_pkgs()`）：
- `match_type = 'potential'`
- `sdk_knowledge_base_id IS NULL`
- 冷却期判断（`kb_last_ask_at`）
- 该前缀不在 KB 中（NOT EXISTS 到 `class_sdk_knowledge_base`）

## 2. 命中 KB 的直接回填
- `process_batch()` 首先对 `combined_pkgs` 中的每个包检查是否已存在 KB（`_find_kb_by_pkg()`）。
- 命中则直接回填（`_backfill_kb_id()`），更新全库里“相同前缀 + potential + 未绑定 KB”的所有记录。

回填 SQL（摘自 `_backfill_kb_id()`）：
```sql
UPDATE class_app_version_sdks s
SET sdk_knowledge_base_id = :kb_id,
    kb_last_ask_at = now(),
    kb_last_ask_result = NULL
WHERE trim(s.sdk_package_prefix) = :pkg
  AND s.match_type = 'potential'
  AND s.sdk_knowledge_base_id IS NULL
```

## 3. 未命中的构造批量 Prompt 并提交 LLM
- 对未命中的包，收集以下上下文组成 `to_query`：
  - `package`（当前包前缀）
  - `child_packages`（该前缀下的子包名集合）
  - `detection_types`（类型去重列表）
  - `apps`（部分 App id/name 作为参考）
- 构造批量 Prompt（`_build_batch_prompt()`），提交 LLM（Gemini 优先，DeepSeek 兜底），期望返回形如 `{ "items": [...] }` 的 JSON。

## 4. 逐条处理：当前包与返回项的配对
- 核心遍历：`for itm, res in zip(to_query, parsed)`。
  - `pkg_in = itm["package"]` 定义了“当前包”。
  - 解析 `res` 中的字段：`sdk_name`、`subcategory_id`、`is_regex_rule`、`regex_pattern` 等。
  - 校验：缺字段、`subcategory_id` 有效性、描述清洗等。

如果 `sdk_name` 缺失或 `subcategory_id` 无效：
- 标记为 unknown/error（`_mark_unknown()`），给该前缀的所有待关联记录写冷却标记（不回填 `sdk_knowledge_base_id`）。

## 5. KB Upsert 与两种回填
1) Upsert KB（`_upsert_kb()`）
- 若 `is_regex_rule=True` 且提供了 `regex_pattern`，会保存正则相关字段；必要时 `package_prefix` 使用 `regex_pattern`。

2) 当前条直连（点对点）
- 定义：只对“当前包 `pkg_in`”这一个前缀做回填。
- 触发：
  - 非正则：必触发直连。
  - 正则：只有 `re.fullmatch(regex_pattern, pkg_in)` 为真才触发；否则跳过并打印告警：
    `[batch:{pkg_in}] skip direct link: pkg not fullmatch regex {regex_pattern}`。
- 动作：调用 `_backfill_kb_id(session, pkg_in, kb_row.id)`，一次性更新全库中等于该前缀的所有 potential 未绑定记录。

3) 正则回填（批量）
- 触发：当 `is_regex_rule=True` 且正则合法并有 `^...$` 锚点。
- 范围：对候选集合（默认=全库未绑定的 potential 去重前缀）做 `fullmatch`，命中的每个前缀分别调用 `_backfill_kb_id()` 批量绑定。
- 即使当前包没有匹配该正则，正则回填也会更新其它被命中的前缀。
- 还包含安全线：
  - 正则非法/未锚定 -> 跳过回填并告警。
  - 过多命中（命中比例 >= 60%） -> 认为模式过宽，跳过回填并统计计数。

## 6. 示例走一遍
假设本批有三个前缀：
```
P1 = com.foo.bar
P2 = com.foo.ads.sdk
P3 = onecloud.cn.powerbabe.mail.model
```

- 步骤 A：命中 KB 的先回填（假设都未命中 KB，进入 LLM 阶段）
- 步骤 B：LLM 返回（合并示意）：
```json
{
  "items": [
    {
      "package": "com.foo.bar",
      "sdk_name": "Foo 核心 SDK",
      "subcategory_id": 101,
      "is_regex_rule": false
    },
    {
      "package": "com.foo",
      "sdk_name": "Foo 广告 SDK",
      "subcategory_id": 202,
      "is_regex_rule": true,
      "regex_pattern": "^com\\.foo\\.[a-z0-9_]+$"
    }
  ]
}
```
- 步骤 C：逐条处理（`zip(to_query, parsed)`，仅示意）：
  1) 处理 `P1=com.foo.bar` 与第一条结果：
     - 非正则 -> 触发“当前条直连”。
     - 执行 `_backfill_kb_id(..., pkg='com.foo.bar')`：更新全库所有 `com.foo.bar` 的 potential 未绑定记录。
  2) 处理 `P2=com.foo.ads.sdk` 与第二条（正则）结果：
     - 正则，检查 `^com\.foo\.[a-z0-9_]+$` 是否 fullmatch `com.foo.ads.sdk` -> 成立。
     - 触发“当前条直连”：更新全库所有 `com.foo.ads.sdk` 的 potential 未绑定记录。
     - 触发“正则回填”：对全库未绑定 potential 去重前缀做 `fullmatch`，命中的如 `com.foo.push`, `com.foo.track`, `com.foo.ads.sdk` 等都被批量绑定。
  3) 处理 `P3=onecloud.cn.powerbabe.mail.model`：
     - 由于返回列表长度与输入长度可能不一致（合并为正则项），`P3` 可能没有对应的返回项；或即使有正则返回，但 `P3` 不匹配该正则。
     - 若没有对应返回项或字段缺失 -> 走 unknown/error：仅写冷却标记，不回填。
     - 若有正则返回但 `P3` 不匹配 -> 跳过“当前条直连”，但仍执行“正则回填”去更新其它匹配到的前缀；`P3` 本身不会被回填。

## 7. 统计与日志
- 统计：`total_pkgs/linked/unknown/regex_kb_created/nonregex_kb_created/regex_backfill_*` 等（`_init_stats()`）。
- 关键日志：
  - 批量 Prompt 与 LLM 原始文本/JSON（便于调试）。
  - “当前条直连”被跳过时的告警（便于识别哪些包未被直接绑定）。
  - 正则回填的匹配包数与受影响行数（蓝色日志）。

## 8. 常见问题
- Q：为何“当前包”不被回填，但日志显示进行了正则回填？
  - A：因为“当前条直连”与“正则回填”相互独立。当前包没匹配该正则就不会直连，但正则回填仍会更新其他命中的前缀。
- Q：非正则一定直连吗？
  - A：是，只要 `sdk_name` 与有效 `subcategory_id` 存在，则会对该 `pkg_in` 做直连回填。
- Q：匹配范围是“本批数据”还是“全库”？
  - A：直连与回填均更新“全库”中满足条件的记录（见 `_backfill_kb_id()` 的 SQL 条件）。

---

附：若要快速验证某个前缀的当前状态，可用查询（示例）：
```sql
SELECT DISTINCT trim(s.sdk_package_prefix) AS pkg, s.sdk_knowledge_base_id, s.kb_last_ask_at, s.kb_last_ask_result
FROM class_app_version_sdks s
WHERE trim(s.sdk_package_prefix) = 'onecloud.cn.powerbabe.mail.model';
```

## 9. P3 情况详解（更细颗粒）

以 `P3 = onecloud.cn.powerbabe.mail.model` 为例，结合代码路径详细说明：

- __[A] P3 没有对应返回项__（发生于“合并为正则项”导致 `len(parsed) < len(to_query)` 且 `has_regex_merge_items=True`）
  - 代码：`len(parsed) != len(to_query)` 且 `has_regex_merge_items=True` 时放宽长度校验（`ask_gemini_potential.py` 第 1002-1007 行），随后 `for itm, res in zip(to_query, parsed)` 只会处理前 `len(parsed)` 条；尾部未配对的输入项（可能包含 P3）本批不处理。
  - 结果：
    - 不会对 P3 执行 `_mark_unknown()`，也不会执行“当前条直连”。
    - 若本批其它返回项中存在正则项，“正则回填”仍会执行，它针对全库候选匹配；如果这些正则模式 `fullmatch('onecloud.cn.powerbabe.mail.model')`，P3 仍然会被回填（见 [C]/[D]）。

- __[B] P3 有返回项，但关键字段缺失/非法__（例如 `sdk_name` 缺失或 `subcategory_id` 不在提供列表）
  - 代码：`sdk_name` 为空 -> `_mark_unknown(result_code=0)`；`subcategory_id` 无效 -> `_mark_unknown(result_code=2)`（第 1071-1094 行）。
  - 结果：
    - 对应 SQL 会为 P3 的所有“potential 且未绑定 KB”的记录写入冷却标记（`kb_last_ask_result=0/2`），但不会写入 `sdk_knowledge_base_id`。
    - 若同一批次其它返回项包含正则并执行了“正则回填”，这些回填也可能覆盖 P3（如果正则命中 P3），从而把 P3 的 `sdk_knowledge_base_id` 写上，覆盖之前仅写冷却标记的状态。

- __[C] P3 有返回项，且是正则，但 P3 本身不 fullmatch 该正则__
  - 代码：设置 `do_link_current=False`，因 `re.fullmatch(regex, pkg_in)` 失败而“跳过当前条直连”，打印告警：`skip direct link`（第 1156-1181 行）。
  - 同时，若该正则合法并带 `^...$` 锚点，会执行“正则回填”（第 1183-1211 行）。
  - 结果：
    - P3 本身不会因“当前条直连”被回填。
    - 但“正则回填”会对全库候选做 `fullmatch`，凡是命中的前缀都会被回填。如果恰好该正则也命中了 P3（虽然前述判定不匹配，多见于多个正则/多个返回项场景），则 P3 仍可能被回填；若没命中，P3 不变。

- __[D] P3 没有返回项，但被其他返回项的正则回填覆盖__
  - 代码：每个正则返回项在其处理分支里都会执行 `_backfill_kb_by_regex()`（第 1183-1211 行），且未提供 candidates 时会“全库收集候选”并匹配（第 393-421、422-453 行）。
  - 结果：
    - 即便 P3 未被逐条处理（无返回项），只要某个正则返回项的模式 `fullmatch(P3)`，P3 也会在该正则项的回填阶段被统一更新（写入相应 `sdk_knowledge_base_id`）。

- __[E] 没有任何正则命中 P3，且 P3 无有效返回项__
  - 结果：
    - 若 P3 完全无返回项（[A]）且本批没有其它正则命中它，则本批对 P3 不做更改（既不回填也不写冷却）。
    - 若 P3 有返回项但缺字段（[B]），则仅写冷却标记，不回填。

### 小结（P3 最终状态可能性）
- __被直连回填__：仅当 P3 有返回项且非正则，或正则且 `fullmatch(P3)`。
- __被正则回填__：即使 P3 无返回项，也可能被其它返回项的合法正则命中并统一回填。
- __仅写冷却标记__：P3 有返回项但关键信息缺失/非法，且没有任何正则命中 P3。
- __保留原状__：P3 无返回项，且没有任何正则命中 P3（本批对 P3 不产生更改）。

### 10. P3 操作手册（实操与日志对照）

- __[决策树：从输入到输出]__（以 `P3=onecloud.cn.powerbabe.mail.model` 为例）
  1) `P3` 是否在 `zip(to_query, parsed)` 的配对范围内？（看返回是否合并）
     - 否：本批不直接处理 P3（参考 9.A）。进入步骤 3。
     - 是：进入步骤 2。
  2) P3 的返回项是否有效？
     - `sdk_name` 为空 -> `_mark_unknown(result_code=0)`；日志含 `[batch:P3]` 且无回填 SQL（参考 9.B）。
     - `subcategory_id` 非法 -> `_mark_unknown(result_code=2)`；日志含 `[batch:P3] missing_subcategory_id`（参考 9.B）。
     - 有效：Upsert KB 后判断是否直连：
       - 非正则 -> 直连 `_backfill_kb_id(..., pkg=P3)`，一次性更新全库该前缀（参考 5.2）。
       - 正则 -> 仅当 `fullmatch(P3)` 成立才直连；否则日志出现 `skip direct link`（参考 9.C）。随后若正则合法则执行“正则回填”。
  3) 是否有其它返回项的正则回填命中 P3？
     - 是：P3 在该正则项的回填阶段被统一更新（参考 9.D），即使 2) 中 P3 未被直连或被标记 unknown。
     - 否：P3 状态维持步骤 1/2 的结果（参考 9.E）。

- __[关键日志样例]__（`ask_gemini_potential.py` 行号以当前版本为准）
  - 跳过直连当前包（P3 未 fullmatch）：
    ```
    [batch:onecloud.cn.powerbabe.mail.model] skip direct link: pkg not fullmatch regex ^com\.foo\.[a-z0-9_]+$
    ```
  - 执行正则回填（无论是否命中 P3）：
    ```
    [batch:onecloud.cn.powerbabe.mail.model] 正则回填：匹配包数=K, 受影响行数总和=N; 示例: pkgA, pkgB ...
    ```
  - P3 被标记为 unknown（缺少 sdk_name）：
    ```
    pkg=onecloud.cn.powerbabe.mail.model -> unknown, affected=0
    ```
  - P3 被标记为 error（subcategory_id 无效）：
    ```
    pkg=onecloud.cn.powerbabe.mail.model -> error, affected=M, reason=missing_subcategory_id
    ```
  - P3 被直连回填（非正则或正则 fullmatch）：
    ```
    pkg=onecloud.cn.powerbabe.mail.model -> linked, affected=T
    ```

- __[SQL 影响范围对照]__
  - 直连/回填实际执行的是 `_backfill_kb_id()` 内的 SQL：
    ```sql
    UPDATE class_app_version_sdks s
    SET sdk_knowledge_base_id = :kb_id,
        kb_last_ask_at = now(),
        kb_last_ask_result = NULL
    WHERE trim(s.sdk_package_prefix) = :pkg
      AND s.match_type = 'potential'
      AND s.sdk_knowledge_base_id IS NULL
    ```
  - 含义：一旦触发，对“全库中等于该前缀的所有 potential 且未绑定记录”统一更新，而非仅限当前批次。

- __[快速排查清单：如何判断本次 P3 发生了什么]__
  - 看是否出现 `[batch:P3] skip direct link`（出现则“当前条直连”未发生）。
  - 看本批“正则回填”蓝色日志是否包含 P3（样例包列表仅前若干，必要时查 SQL）。
  - 查看结果汇总行：`pkg=onecloud.cn.powerbabe.mail.model -> <status>, affected=...`
    - `linked` -> 直连或被某正则回填命中。
    - `unknown`/`error` -> 本条被冷却标记；仍可能被其它正则回填覆盖（若命中）。
  - 如需最终状态，以数据库为准：
    ```sql
    SELECT DISTINCT trim(s.sdk_package_prefix) AS pkg, s.sdk_knowledge_base_id, s.kb_last_ask_at, s.kb_last_ask_result
    FROM class_app_version_sdks s
    WHERE trim(s.sdk_package_prefix) = 'onecloud.cn.powerbabe.mail.model';
    ```

