def _init_stats() -> dict[str, int]:
    return {
        'total_pkgs': 0,
        'linked': 0,
        'unknown': 0,
        'regex_kb_created': 0,
        'nonregex_kb_created': 0,
        'regex_backfill_matched_pkgs': 0,
        'regex_backfill_affected_rows': 0,
        'regex_overmatch_skipped': 0,
        'regex_too_broad': 0,
        'direct_link_regex_pkg_mismatch_skipped': 0,
    }

# coding: utf-8
"""
基于 Plan A 的最小实现：
- 从 class_app_version_sdks 提取 DISTINCT 的 potential 包名前缀（规范化：仅 trim），仅针对
  sdk_knowledge_base_id IS NULL 且 (kb_last_ask_at IS NULL 或 <= 冷却阈值) 的记录。
- 命中 KB（按规范化包名，仅 trim 匹配）则直接批量回填 sdk_knowledge_base_id；
- 未命中则询问 LLM（默认 Gemini，可选 DeepSeek 兜底），若给出 sdk_name（可带 subcategory_id），
  则 Upsert 到 KB 并批量回填；否则标记 unknown 冷却。
- 仅新增两个字段：kb_last_ask_at, kb_last_ask_result（0=unknown, 2=error）。命中 KB 不必写 1。

运行示例：
  python -m tenyy.src.apkinfo_analysis.ask_gemini.ask_gemini_potential \
    --batch-size 20 --rpm 8 --cooldown-days 7 --max-batches 0 --dry-run 0 \
    --provider auto --model-name gemini-2.5-flash
"""
import os
import json
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional, List, Tuple

from sqlalchemy import create_engine, text, func
from sqlalchemy.orm import sessionmaker

from tenyy.src.config.settings import settings
from tenyy.src.models.class_sdk_knowledge_base import class_SDKKnowledgeBase
from tenyy.src.models.class_app_version_sdks import class_AppVersionSDK

# 复用现有工具
from tenyy.src.apkinfo_analysis.ask_gemini.ask_gemini_knowledge import (
    _ensure_genai_configured,
    rate_limit_guard,
    fetch_subcategories,
)
from tenyy.src.apkinfo_analysis.ask_gemini.deepseek_client import get_client as get_ds_client
from tenyy.src.apkinfo_analysis.ask_gemini import gemini_processor

import google.generativeai as genai
import re

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='[%(asctime)s] %(levelname)s - %(message)s')

# 需要尽量补全的元数据字段
_META_FIELDS = [
    'brief', 'description', 'company_name', 'official_web',
    'sdk_document_url', 'privacy_policy_url', 'compliance_instructions_url', 'version'
]

# 常见/知名 SDK 关键词（用于更强告警）
_WELL_KNOWN_HINTS = [
    '高德', '百度', '友盟', '极光', 'JPush', 'Firebase', 'Facebook', 'Google',
    'AdMob', 'AppsFlyer', 'Bugly', 'Umeng', 'TalkingData', 'Sentry', 'Adjust',
    'Branch', 'Flurry'
]


def get_engine():
    return create_engine(settings.DATABASE_URL)


def _normalize_pkg(s: Optional[str]) -> str:
    return (s or '').strip()


def _fetch_distinct_pkgs(session, cutoff_ts: datetime, limit: int) -> List[str]:
    """抓取去重后的 potential 包名（规范化），限制数量。"""
    sql = text(
        """
        SELECT DISTINCT trim(sdk_package_prefix) AS pkg
        FROM class_app_version_sdks
        WHERE match_type = 'potential'
          AND sdk_knowledge_base_id IS NULL
          AND (kb_last_ask_at IS NULL OR kb_last_ask_at <= :cutoff)
          AND NOT EXISTS (
                SELECT 1
                FROM class_sdk_knowledge_base kb
                WHERE trim(kb.package_prefix) = trim(class_app_version_sdks.sdk_package_prefix)
          )
        ORDER BY pkg ASC
        LIMIT :lim
        """
    )
    rows = session.execute(sql, {"cutoff": cutoff_ts, "lim": int(limit)}).fetchall()
    return [r[0] for r in rows if r and r[0]]


def _fetch_candidate_apps(session, cutoff_ts: datetime, limit: int) -> List[Tuple[str, Optional[str]]]:
    """抓取去重后的待处理 App 列表：返回 [(app_id, app_name)]。
    条件与原 potential 选择一致：仅 class_app_version_sdks 未绑定 KB、冷却期已过，且该前缀不在 KB 中。
    """
    sql = text(
        """
        SELECT DISTINCT a.id AS app_id, a.name AS app_name
        FROM class_app_version_sdks s
        JOIN app_version av ON av.id = s.app_version_id
        JOIN store_app sa ON sa.id = av.store_app_id
        JOIN app a ON a.id = sa.app_id
        WHERE s.match_type = 'potential'
          AND s.sdk_knowledge_base_id IS NULL
          AND (s.kb_last_ask_at IS NULL OR s.kb_last_ask_at <= :cutoff)
          AND NOT EXISTS (
                SELECT 1
                FROM class_sdk_knowledge_base kb
                WHERE trim(kb.package_prefix) = trim(s.sdk_package_prefix)
          )
        ORDER BY a.id
        LIMIT :lim
        """
    )
    rows = session.execute(sql, {"cutoff": cutoff_ts, "lim": int(limit)}).fetchall()
    out: List[Tuple[str, Optional[str]]] = []
    for r in rows:
        try:
            out.append((str(r[0]), r[1]))
        except Exception:
            continue
    return out


def _fetch_app_all_pkgs(session, app_id: str, cutoff_ts: datetime) -> List[str]:
    """获取某个 App 下所有待处理 potential 包前缀（去重 + trim）。"""
    sql = text(
        """
        SELECT DISTINCT trim(s.sdk_package_prefix) AS pkg
        FROM class_app_version_sdks s
        JOIN app_version av ON av.id = s.app_version_id
        JOIN store_app sa ON sa.id = av.store_app_id
        WHERE sa.app_id = :app_id
          AND s.match_type = 'potential'
          AND s.sdk_knowledge_base_id IS NULL
          AND (s.kb_last_ask_at IS NULL OR s.kb_last_ask_at <= :cutoff)
          AND NOT EXISTS (
                SELECT 1
                FROM class_sdk_knowledge_base kb
                WHERE trim(kb.package_prefix) = trim(s.sdk_package_prefix)
          )
        ORDER BY pkg ASC
        """
    )
    rows = session.execute(sql, {"app_id": app_id, "cutoff": cutoff_ts}).fetchall()
    return [r[0] for r in rows if r and r[0]]


def _fetch_child_packages(session, pkg_norm: str, max_count: int = 500) -> List[str]:
    """汇总该包名下潜在记录的子包名（JSON数组并集），做去重，最多返回 max_count 项。"""
    sql = text(
        """
        SELECT child_packages
        FROM class_app_version_sdks
        WHERE trim(sdk_package_prefix) = :pkg
          AND match_type = 'potential'
          AND child_packages IS NOT NULL
        """
    )
    rows = session.execute(sql, {"pkg": pkg_norm}).fetchall()
    seen = set()
    for r in rows:
        try:
            arr = json.loads(r[0]) if r and r[0] else []
            if isinstance(arr, list):
                for it in arr:
                    if isinstance(it, str):
                        s = it.strip()
                        if s:
                            seen.add(s)
        except Exception:
            continue
    out = sorted(seen)
    if len(out) > max_count:
        return out[:max_count]
    return out


def _fetch_detection_types(session, pkg_norm: str) -> List[int]:
    """汇总该包名下潜在记录的类型(type)去重列表，用于提示 LLM 决策。"""
    sql = text(
        """
        SELECT DISTINCT type
        FROM class_app_version_sdks
        WHERE trim(sdk_package_prefix) = :pkg
          AND match_type = 'potential'
          AND type IS NOT NULL
        ORDER BY 1
        """
    )
    rows = session.execute(sql, {"pkg": pkg_norm}).fetchall()
    out = []
    for r in rows:
        try:
            v = int(r[0])
            out.append(v)
        except Exception:
            continue
    return out


def _find_kb_by_pkg(session, pkg_norm: str) -> Optional[class_SDKKnowledgeBase]:
    """按规范化包名（仅 trim）精确匹配 KB。"""
    return (
        session.query(class_SDKKnowledgeBase)
        .filter(func.trim(class_SDKKnowledgeBase.package_prefix) == pkg_norm)
        .one_or_none()
    )


def _fetch_app_context(session, pkg_norm: str, limit: int = 5) -> List[Dict[str, Any]]:
    """为给定前缀收集关联的 App 上下文（app.id 与 app.name），用于提示 LLM 提高准确性。
    仅抽样去重前若干条以避免 prompt 过长。
    """
    sql = text(
        """
        SELECT DISTINCT a.id AS app_id, a.name AS app_name
        FROM class_app_version_sdks s
        JOIN app_version av ON av.id = s.app_version_id
        JOIN store_app sa ON sa.id = av.store_app_id
        JOIN app a ON a.id = sa.app_id
        WHERE trim(s.sdk_package_prefix) = :pkg
          AND s.match_type = 'potential'
        LIMIT :lim
        """
    )
    rows = session.execute(sql, {"pkg": pkg_norm, "lim": int(limit)}).fetchall()
    out: List[Dict[str, Any]] = []
    for r in rows:
        try:
            out.append({"id": r[0], "name": r[1]})
        except Exception:
            continue
    return out


def _upsert_kb(session, pkg_norm: str, sdk_name: Optional[str], subcategory_id: Optional[int],
               description: Optional[str] = None, company_name: Optional[str] = None,
               official_web: Optional[str] = None, sdk_document_url: Optional[str] = None,
               privacy_policy_url: Optional[str] = None, compliance_instructions_url: Optional[str] = None,
               version: Optional[str] = None, brief: Optional[str] = None,
               asked_by: Optional[str] = None,
               detection_type: Optional[int] = None,
               is_regex_rule: Optional[bool] = None,
               regex_pattern: Optional[str] = None,
               stats: Optional[Dict[str, int]] = None) -> class_SDKKnowledgeBase:
    # 若为正则规则，按约定把正则写入 package_prefix 以供匹配模块读取
    effective_prefix = (regex_pattern or pkg_norm) if (is_regex_rule and regex_pattern) else pkg_norm
    row = _find_kb_by_pkg(session, effective_prefix)
    now_ts = datetime.now(timezone.utc)
    created_new = (row is None)
    if row is None:
        row = class_SDKKnowledgeBase(
            package_prefix=effective_prefix,
            sdk_name=(sdk_name or None),
            subcategory_id=(int(subcategory_id) if subcategory_id is not None else None),
            description=(description or None),
            company_name=(company_name or None),
            official_web=(official_web or None),
            sdk_document_url=(sdk_document_url or None),
            privacy_policy_url=(privacy_policy_url or None),
            compliance_instructions_url=(compliance_instructions_url or None),
            version=(version or None),
            brief_message=(brief or None),
            detection_type=(int(detection_type) if detection_type is not None else None),
            is_regex_rule=(bool(is_regex_rule) if is_regex_rule is not None else None),
            regex_pattern=(regex_pattern or None),
            last_checked_at=now_ts,
            status='identified' if sdk_name else 'pending_analysis',
        )
        session.add(row)
        session.flush()  # 以便拿到 id
    else:
        # 仅在提供新信息时更新
        if sdk_name:
            row.sdk_name = sdk_name
            row.status = 'identified'
        if subcategory_id is not None:
            try:
                row.subcategory_id = int(subcategory_id)
            except Exception:
                pass
        # 以下字段为可选补充信息，有则更新
        if description:
            row.description = description
        if company_name:
            row.company_name = company_name
        if official_web:
            row.official_web = official_web
        if sdk_document_url:
            row.sdk_document_url = sdk_document_url
        if privacy_policy_url:
            row.privacy_policy_url = privacy_policy_url
        if compliance_instructions_url:
            row.compliance_instructions_url = compliance_instructions_url
        if version:
            row.version = version
        if brief is not None:
            row.brief_message = brief
        # 新增字段：检测相关
        if detection_type is not None:
            try:
                row.detection_type = int(detection_type)
            except Exception:
                pass
        if is_regex_rule is not None:
            try:
                row.is_regex_rule = bool(is_regex_rule)
            except Exception:
                pass
        if regex_pattern:
            row.regex_pattern = regex_pattern
            # 若标记为正则规则，则将有效前缀也更新为正则表达式
            try:
                if bool(is_regex_rule):
                    row.package_prefix = regex_pattern
            except Exception:
                pass
        row.last_checked_at = now_ts
    # 根据来源回传状态
    try:
        if asked_by and asked_by.lower() == 'gemini':
            row.status = 'gemini_asked'
    except Exception:
        pass
    session.commit()
    # 统计：仅在创建新 KB 时计数
    try:
        if created_new and stats is not None:
            if bool(is_regex_rule):
                stats['regex_kb_created'] = stats.get('regex_kb_created', 0) + 1
            else:
                stats['nonregex_kb_created'] = stats.get('nonregex_kb_created', 0) + 1
    except Exception:
        pass
    return row


def _backfill_kb_id(session, pkg_norm: str, kb_id: int, dry_run: bool = False) -> int:
    """批量把相同包名的 potential 记录绑定到 KB。返回影响行数。"""
    if dry_run:
        logger.info(f"[DRY-RUN] 回填: pkg={pkg_norm} -> sdk_knowledge_base_id={kb_id}")
        return 0
    sql = text(
        """
        UPDATE class_app_version_sdks s
        SET sdk_knowledge_base_id = :kb_id,
            kb_last_ask_at = now(),
            kb_last_ask_result = NULL
        WHERE trim(s.sdk_package_prefix) = :pkg
          AND s.match_type = 'potential'
          AND s.sdk_knowledge_base_id IS NULL
        """
    )
    res = session.execute(sql, {"kb_id": int(kb_id), "pkg": pkg_norm})
    session.commit()
    return res.rowcount or 0


def _backfill_kb_by_regex(
    session,
    regex_pattern: str,
    kb_id: int,
    dry_run: bool = False,
    sample_limit: int = 10,
    candidates: Optional[List[str]] = None,
    stats: Optional[Dict[str, int]] = None,
) -> Tuple[int, int, List[str]]:
    """按正则匹配回填（安全全库模式）：
    - 若提供 candidates：仅在该集合内匹配；
    - 若未提供：从全库收集 potential 且未绑定 KB 的去重包名作为候选，并做类型预筛以减少误匹配；
    - 使用 fullmatch、锚点校验、过匹配熔断与最大候选上限保障安全。
    返回：匹配到的包个数、受影响行数总和、示例包名列表。
    """

    matched_pkgs: List[str] = []
    affected_sum = 0
    try:
        rx = re.compile(regex_pattern)
    except Exception:
        return 0, 0, []

    # 若未提供 candidates，则全库收集候选
    if candidates is None:
        try:
            rows = session.execute(text(
                """
                SELECT DISTINCT trim(s.sdk_package_prefix) AS pkg
                FROM class_app_version_sdks s
                WHERE s.match_type = 'potential'
                  AND s.sdk_knowledge_base_id IS NULL
                """
            )).fetchall()
            candidates = [r[0] for r in rows if r and r[0]]
        except Exception:
            candidates = []

        # 类型预筛（启发式）：若正则包含 \.so 或以 .so$ 结尾，仅保留疑似原生库名
        try:
            pattern_str = str(regex_pattern)
            if ('\\.so' in pattern_str) or pattern_str.endswith('.so$'):
                candidates = [p for p in candidates if isinstance(p, str) and p.startswith('lib') and p.endswith('.so')]
        except Exception:
            pass

        # 候选数量上限防护
        MAX_CANDIDATES = 100000
        if len(candidates) > MAX_CANDIDATES:
            logger.warning(_yellow(f"[regex_backfill_guard] global candidates truncated: {len(candidates)} -> {MAX_CANDIDATES}"))
            candidates = candidates[:MAX_CANDIDATES]

    for p in candidates:
        try:
            if rx.fullmatch(p):
                matched_pkgs.append(p)
        except Exception:
            continue

    # 过多命中保护：若命中比例过高，认为模式不安全，跳过回填
    try:
        if candidates and len(candidates) >= 10:
            ratio = len(matched_pkgs) / max(1, len(candidates))
            if ratio >= 0.6:
                logger.warning(
                    _yellow(f"[regex_backfill_guard] pattern hit ratio too high: {ratio:.2%}; skip backfill. pattern={regex_pattern}")
                )
                try:
                    if stats is not None:
                        stats['regex_overmatch_skipped'] = stats.get('regex_overmatch_skipped', 0) + 1
                except Exception:
                    pass
                return 0, 0, matched_pkgs[:max(0, int(sample_limit))]
    except Exception:
        pass

    for p in matched_pkgs:
        try:
            affected_sum += _backfill_kb_id(session, p, kb_id, dry_run=dry_run)
        except Exception:
            continue

    sample = matched_pkgs[:max(0, int(sample_limit))]
    return len(matched_pkgs), affected_sum, sample


def _mark_unknown(session, pkg_norm: str, result_code: int, dry_run: bool = False, stats: Optional[Dict[str, int]] = None) -> int:
    """为该包名的所有待关联记录写 unknown/error 冷却标记。result_code: 0=unknown, 2=error"""
    if dry_run:
        logger.info(f"[DRY-RUN] 标记: pkg={pkg_norm} -> kb_last_ask_result={result_code}")
        try:
            if result_code == 0 and stats is not None:
                stats['unknown'] = stats.get('unknown', 0) + 1
        except Exception:
            pass
        return 0
    sql = text(
        """
        UPDATE class_app_version_sdks s
        SET kb_last_ask_at = now(),
            kb_last_ask_result = :rc
        WHERE trim(s.sdk_package_prefix) = :pkg
          AND s.match_type = 'potential'
          AND s.sdk_knowledge_base_id IS NULL
        """
    )
    res = session.execute(sql, {"rc": int(result_code), "pkg": pkg_norm})
    session.commit()
    try:
        if result_code == 0 and stats is not None:
            stats['unknown'] = stats.get('unknown', 0) + 1
    except Exception:
        pass
    return res.rowcount or 0


def _build_batch_prompt(items: List[Dict[str, Any]], subcategories: List[Dict[str, Any]]) -> str:
    sc_brief = [{"id": int(s["id"]), "name": s["name"], "category_id": int(s["category_id"])} for s in subcategories]
    dt_mapping = {
        0: "Native Libraries (.so)",
        1: "Service Component",
        2: "Activity Component",
        3: "Receiver Component",
        4: "Provider Component",
        5: "DEX Packages (Java/Kotlin libs & SDKs)",
        6: "Static Libraries",
        7: "Permissions",
        8: "Metadata",
        9: "Intent Actions"
    }
    instruction = (
        "你是移动应用SDK识别助手，通过包名或本地库名识别 SDK 或功能。只输出严格的JSON对象，不要任何多余文本。\n"
        "批量模式返回 {\"items\":[...]}，其中每个元素为一个对象。必须包含所有字段。除专有名词外，描述性字段必须使用中文；无法确认时返回 null，禁止仅给英文。\n"
        "字段要求：package、sdk_name、subcategory_id（若有 sdk_name 则必须从 subcategory_list 选择一个有效 id，无法完全确认也要选择最接近的一项，禁止 0/空/不在列表的值）、detection_type、is_regex_rule、regex_pattern，及其他可选字段（一次性尽量填写，确实未知再为 null）。\n"
        "brief 与 description 必须为中文表述；且严禁使用不确定措辞（如：可能/大概/或许/猜测/疑似/似乎/应该/大约/大致 等）。\n"
        "当 sdk_name 非空时，应提供准确、简洁的中文 description（可夹杂英文专有名词）；若无法确认，description 必须为 JSON null。严禁使用字符串占位（例如 'null'、'None'、'N/A'、空串）。\n"
        "对于知名 SDK（如 高德/百度/友盟/极光/JPush/Firebase/Facebook/Google/AdMob/AppsFlyer/Bugly/Umeng/TalkingData 等），\n"
        "必须填写主体名称（company_name，可为公司/组织/团队）与 official_web；若可确认文档与隐私政策，提供 sdk_document_url 与 privacy_policy_url。严禁凭空杜撰来源。\n"
        "合并规则（重要）：当输入 items 中出现多个记录，其 brief/description 含义明显相似且 package 前缀仅在末尾存在小幅变体（如附加子段、版本号、地区/渠道后缀等），请合并为一个 item：\n"
        "- 合并后仅返回一个 item，不要为这些相似记录分别返回多个 item。\n"
        "- 设置 is_regex_rule=true，并在 regex_pattern 中给出可读、可维护的正则表达式，以统一匹配这些前缀变体；如无须正则则给出最短稳定前缀（is_regex_rule=false）。\n"
        "- 正则示例：^com\\.example(?:\\.[a-z0-9_]+){0,2}$ 或 ^com\\.vendor\\.(?:ads?|ad|adv)(?:\\.[a-z0-9_]+)*$。避免过度匹配，禁止使用 .*, .+ 无界贪婪覆盖整段。\n"
        "- 仅当语义与功能一致时才合并；若语义差异显著（不同 SDK/不同模块），则不要合并。\n"
        "示例（请学习这种合并方式）：\n"
        "输入 items 可能包含：\n"
        "  - com.qzone.module.covercomponent\n"
        "  - com.qzone.module.feedcomponent\n"
        "  - com.qzone.module.vipcomponent\n"
        "期望合并为单条 item：\n"
        "  package=\"com.qzone.module\"，is_regex_rule=true，regex_pattern=^com\\.qzone\\.module\\.(?:covercomponent|feedcomponent|vipcomponent)$；\n"
        "  若后续发现更多同类变体且语义一致，可改用受控通配：^com\\.qzone\\.module\\.[a-z][a-z0-9_]*$（避免无界 .*）。\n"
    )
    payload = {
        "instruction": instruction,
        "subcategory_list": sc_brief,
        "items": items,
        "detection_type_mapping": dt_mapping,
        "output_schema": {
            "package": "string",
            "sdk_name": "string|null",
            "subcategory_id": "int|null",
            "brief": "string|null",
            "description": "string|null",
            "company_name": "string|null",
            "official_web": "string|null",
            "sdk_document_url": "string|null",
            "privacy_policy_url": "string|null",
            "compliance_instructions_url": "string|null",
            "version": "string|null",
            "detection_type": "int|null",
            "is_regex_rule": "bool|null",
            "regex_pattern": "string|null",
        }
    }
    return json.dumps(payload, ensure_ascii=False)


def _redact_subcategories_in_prompt(prompt_text: str) -> str:
    """用于日志打印：将 prompt 中的 subcategory_list 进行省略显示，避免长输出。
    支持两种结构：
      - {"instruction":..., "input": {"subcategory_list": [...]}}
      - {"instruction":..., "subcategory_list": [...], "items": [...]}
    """
    try:
        obj = json.loads(prompt_text)
        # 单条结构
        if isinstance(obj, dict) and isinstance(obj.get('input'), dict):
            sc = obj['input'].get('subcategory_list')
            if isinstance(sc, list):
                obj['input']['subcategory_list'] = f"[omitted {len(sc)} items]"
        # 批量结构
        if isinstance(obj, dict) and isinstance(obj.get('subcategory_list'), list):
            cnt = len(obj['subcategory_list'])
            obj['subcategory_list'] = f"[omitted {cnt} items]"
        return json.dumps(obj, ensure_ascii=False)
    except Exception:
        # 非法 JSON 时直接返回原文
        return prompt_text


def _yellow(text: str) -> str:
    """将文本包装为黄色 ANSI 输出。"""
    return f"\033[33m{text}\033[0m"

def _blue(text: str) -> str:
    """将文本包装为蓝色 ANSI 输出。"""
    return f"\033[34m{text}\033[0m"


def _clean_nullable_text(text: Optional[str]) -> Optional[str]:
    """仅做最小化处理：
    - None -> None
    - 去首尾空白；若为空串则返回 None
    - 否则返回原文（不再过滤占位符或不确定措辞）
    """
    try:
        if text is None:
            return None
        s = str(text).strip()
        # 特殊处理：将 'null' 字符串（大小写不敏感，允许包裹引号）视为 None
        try:
            sx = s.strip().strip('"\'')
        except Exception:
            sx = s
        if sx.lower() == 'null':
            return None
        if not s:
            return None
        return s
    except Exception:
        return None

def _call_gemini(
    prompt: str,
    model_name: str = 'gemini-2.5-flash',
    max_output_tokens: int = 500000,
    expect_items_wrapper: bool = False,
) -> Optional[Dict[str, Any]]:
    model = genai.GenerativeModel(model_name)
    
    def _build_schema(strict: bool) -> Dict[str, Any]:
        """
        构建响应 Schema：
        - 严格版(strict=True)：包含所有字段，但不设置大量 required，避免 400/InvalidArgument。
        - 精简版(strict=False)：仅保留关键字段，进一步降低复杂度。
        注意：根据 Gemini 最佳实践，尽量减少必填字段与复杂限制；字段留空让模型可跳过，服务端更稳。
        """
        # 通用属性（全部作为可选属性，不使用 nullable 开关，交由模型选择填写与否）
        common_props: Dict[str, Any] = {
            "package": {"type": "string"},
            "sdk_name": {"type": "string"},
            "subcategory_id": {"type": "integer"},
            "brief": {"type": "string"},
            "description": {"type": "string"},
            "company_name": {"type": "string"},
            "official_web": {"type": "string"},
            "sdk_document_url": {"type": "string"},
            "privacy_policy_url": {"type": "string"},
            "compliance_instructions_url": {"type": "string"},
            "version": {"type": "string"},
            "detection_type": {"type": "integer"},
            "is_regex_rule": {"type": "boolean"},
            "regex_pattern": {"type": "string"},
        }
        if strict:
            single = {
                "type": "object",
                "properties": common_props,
                # 仅保留最小必填，避免复杂度导致 400。其它字段由模型尽力填写。
                "required": ["package"],
            }
        else:
            # 精简：只保留核心决策字段，进一步降低复杂度
            simple_props = {
                "package": {"type": "string"},
                "sdk_name": {"type": "string"},
                "subcategory_id": {"type": "integer"},
                "brief": {"type": "string"},
                "description": {"type": "string"},
                "detection_type": {"type": "integer"},
                "is_regex_rule": {"type": "boolean"},
                "regex_pattern": {"type": "string"},
            }
            single = {
                "type": "object",
                "properties": simple_props,
                "required": ["package"],
            }
        if expect_items_wrapper:
            return {
                "type": "object",
                "properties": {
                    "items": {"type": "array", "items": single}
                },
                "required": ["items"],
            }
        return single

    def _parse_json_loose(txt: str) -> Optional[Any]:
        """尝试宽松修复 JSON：
        - 直接 json.loads
        - 若失败，截取第一个 '{' 到最后一个 '}' 的片段再解析
        - 若仍失败，返回 None
        """
        if not txt:
            return None
        try:
            return json.loads(txt)
        except Exception:
            pass
        try:
            l = txt.find('{')
            r = txt.rfind('}')
            if l != -1 and r != -1 and r > l:
                segment = txt[l:r+1]
                return json.loads(segment)
        except Exception:
            pass
        return None

    # 先用较严格但低 required 的 Schema；若出现 InvalidArgument/400，再回退到精简 Schema
    schemas = [
        _build_schema(strict=True),
        _build_schema(strict=False),
    ]
    last_err: Optional[str] = None

    # 自适应 token 上限：首次用传入值；若解析失败，再提升一档（最多到 500000）重试两套 schema
    token_attempts: List[int] = []
    try:
        base = int(max_output_tokens)
    except Exception:
        base = 500000
    token_attempts.append(base)
    increased = min(base * 2, 500000)
    if increased > base:
        token_attempts.append(increased)

    for ti, tok_limit in enumerate(token_attempts, 1):
        for si, schema in enumerate(schemas, 1):
            try:
                resp = model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        max_output_tokens=tok_limit,
                        temperature=0.0,
                        response_mime_type="application/json",
                        response_schema=schema,
                    )
                )
                content = ''
                try:
                    content = getattr(resp, 'text', '') or ''
                except Exception:
                    content = ''
                try:
                    logger.info(f"RAW LLM TEXT (gemini, token_try={ti}, schema_try={si}, max_tokens={tok_limit}):\n" + _yellow(content))
                except Exception:
                    pass
                data = _parse_json_loose(content)
                if isinstance(data, (dict, list)):
                    return data
                # 未能解析则继续下一轮（更精简 Schema / 更高 token 上限）
                last_err = "invalid_or_empty_json"
            except Exception as e:
                msg = str(e)
                last_err = msg
                # 命中 Schema 复杂度问题时，尝试回退
                if any(k in msg for k in ["InvalidArgument", "400", "response_schema"]):
                    logger.warning(_yellow(f"Gemini 调用遇到 Schema 错误，尝试回退更精简 Schema (token_try={ti}, schema_try={si}): {msg}"))
                    continue
                else:
                    logger.error(f"Gemini 调用失败 (token_try={ti}, schema_try={si}): {e}")
                    break

    logger.error(f"Gemini 最终失败: {last_err}")
    return None


def _warn_missing_fields(data: Dict[str, Any], context: str) -> None:
    """当关键字段缺失或为 null 时打印黄色告警。"""
    try:
        keys = [
            'sdk_name', 'subcategory_id', 'detection_type',
            'is_regex_rule', 'regex_pattern'
        ] + _META_FIELDS
        missing = [k for k in keys if k not in data]
        nulls = [k for k in keys if k in data and data[k] is None]
        if missing:
            logger.warning(_yellow(f"[{context}] 缺少字段: {missing}"))
        if nulls:
            logger.info(_yellow(f"[{context}] 字段为 null: {nulls}"))

        # 对知名 SDK 缺失关键信息加重告警
        try:
            sdk_name = (data.get('sdk_name') or '').strip()
            if sdk_name:
                if any(h.lower() in sdk_name.lower() for h in _WELL_KNOWN_HINTS):
                    critical = [k for k in ['company_name', 'official_web'] if data.get(k) is None]
                    if critical:
                        logger.warning(_yellow(
                            f"[{context}] 知名SDK缺少关键信息: {critical}；请一次性填写 company_name 与 official_web，如可确认请给 sdk_document_url 与 privacy_policy_url"
                        ))
        except Exception:
            pass
    except Exception:
        pass

def _call_deepseek(prompt: str, max_output_tokens: int = 2000, expect_array: bool = False) -> Optional[Dict[str, Any]]:
    try:
        client = get_ds_client()
    except Exception as e:
        logger.error(f"DeepSeek 客户端初始化失败: {e}")
        return None
    try:
        system_msg = (
            '你是严格的JSON输出助手。只输出一个JSON对象(json)，不要任何多余文本。'
            '批量模式请返回 {"items":[...]}，单条模式返回一个对象。'
            '所有可选字段不确定时一律为 null，不要Markdown。'
        )
        resp = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": system_msg},
                {"role": "user", "content": prompt},
            ],
            temperature=0.0,
            max_tokens=max_output_tokens,
            stream=False,
            response_format={"type": "json_object"},
        )
        txt = ''
        try:
            if resp and getattr(resp, 'choices', None):
                txt = resp.choices[0].message.content or ''
        except Exception:
            txt = ''
        # 打印原始文本，便于排查解析失败的原因
        try:
            logger.info("RAW LLM TEXT (deepseek):\n" + _yellow(txt))
        except Exception:
            pass
        # DeepSeek JSON Output：content 应该就是一个合法 JSON 对象字符串
        data = None
        try:
            data = json.loads(txt) if txt else None
        except Exception:
            data = None
        return data if isinstance(data, dict) else None
    except Exception as e:
        logger.error(f"DeepSeek 调用失败: {e}")
        return None

def process_batch(session, pkgs: List[str], subcategories: List[Dict[str, Any]], provider: str, model_name: str,
                 dry_run: bool, print_prompt_only: bool, stats: Optional[Dict[str, int]] = None) -> List[Tuple[str, str, int]]:
    """批量处理一组包名，返回 [(pkg, status, affected)]。"""
    results: List[Tuple[str, str, int]] = []
    to_query: List[Dict[str, Any]] = []

    # 统计：总包数
    try:
        if stats is not None:
            stats['total_pkgs'] = stats.get('total_pkgs', 0) + len(pkgs)
    except Exception:
        pass

    # 1) 先处理命中 KB 的项
    for pkg in pkgs:
        kb = _find_kb_by_pkg(session, pkg)
        if kb is not None:
            affected = _backfill_kb_id(session, pkg, kb.id, dry_run=dry_run)
            try:
                meta = {
                    'company_name': getattr(kb, 'company_name', None),
                    'sdk_name': getattr(kb, 'sdk_name', None),
                    'brief': getattr(kb, 'brief_message', None),
                    'description': getattr(kb, 'description', None),
                }
                results.append((pkg, 'linked', affected, meta))  # type: ignore
            except Exception:
                results.append((pkg, 'linked', affected))
            # 统计：命中已有 KB 的直接链接
            try:
                if stats is not None:
                    stats['linked'] = stats.get('linked', 0) + 1
            except Exception:
                pass
        else:
            child_pkgs = _fetch_child_packages(session, pkg)
            detection_types = _fetch_detection_types(session, pkg)
            app_ctx = _fetch_app_context(session, pkg)
            to_query.append({
                "package": pkg,
                "child_packages": child_pkgs,
                "detection_types": detection_types,
                "apps": app_ctx  # 为 LLM 提供 app.id 与 app.name 参考
            })

    if not to_query:
        return results

    # 2) 构建并打印批量 Prompt
    prompt = _build_batch_prompt(to_query, subcategories)
    try:
        logger.info(f"BATCH PROMPT for pkgs={pkgs}:\n{_redact_subcategories_in_prompt(prompt)}")
    except Exception:
        pass

    if print_prompt_only:
        # 仅打印，不提交
        results.extend([(itm["package"], 'skipped', 0) for itm in to_query])
        return results

    # 3) 提交模型
    data = None
    used_provider = None
    if provider.lower() == 'deepseek':
        data = _call_deepseek(prompt, expect_array=True)
        used_provider = 'deepseek' if data else None
    elif provider.lower() == 'gemini':
        data = _call_gemini(prompt, model_name=model_name, expect_items_wrapper=True)
        used_provider = 'gemini' if data else None
    else:
        data = _call_gemini(prompt, model_name=model_name, expect_items_wrapper=True)
        used_provider = 'gemini' if data else None
        if not data:
            data = _call_deepseek(prompt, expect_array=True)
            used_provider = 'deepseek' if data else None

    # 打印模型返回（已解析的 JSON），便于观察
    try:
        logger.info("RAW LLM JSON (batch):\n" + _yellow(json.dumps(data, ensure_ascii=False, indent=2)))
    except Exception:
        pass

    if not data:
        # 解析失败 -> 整批标记 error
        for itm in to_query:
            affected = _mark_unknown(session, itm["package"], result_code=2, dry_run=dry_run, stats=stats)
            try:
                meta = {"error": "llm_empty_or_invalid_json"}
                results.append((itm["package"], 'error', affected, meta))  # type: ignore
            except Exception:
                results.append((itm["package"], 'error', affected))
        return results

    # DeepSeek JSON Output（批量）：期望为 {"items": [...]}。兼容数组直返。
    parsed: List[Dict[str, Any]] = []
    try:
        if isinstance(data, dict) and isinstance(data.get('items'), list):
            parsed = data['items']
        elif isinstance(data, list):
            parsed = data
        elif isinstance(data, dict):
            # 退化兼容：若返回单对象
            parsed = [data]
    except Exception:
        parsed = []

    # 统计并列出“合并为正则表达式”的 item（仅日志输出，便于观测）
    has_regex_merge_items = False
    try:
        regex_items_info: List[Tuple[str, str]] = []
        for it in parsed if isinstance(parsed, list) else []:
            try:
                if bool(it.get('is_regex_rule')) and it.get('regex_pattern'):
                    pkg_name = str(it.get('package') or '').strip()
                    pat = str(it.get('regex_pattern') or '').strip()
                    if pkg_name or pat:
                        regex_items_info.append((pkg_name, pat))
            except Exception:
                continue
        if regex_items_info:
            has_regex_merge_items = True
            try:
                pretty_list = "\n".join([f"  - package={p or '<none>'}, regex_pattern={r}" for p, r in regex_items_info])
            except Exception:
                pretty_list = str(regex_items_info)
            logger.info(_yellow(
                f"[batch] 合并为正则表达式的 item 数量: {len(regex_items_info)}\n" +
                f"明细:\n{pretty_list}"
            ))

            # 进一步统计：这些正则项覆盖了多少输入记录，估算合并减少量
            try:
                input_pkgs = [itm.get('package') for itm in to_query]
                input_pkgs = [str(p).strip() for p in input_pkgs if p]
                covered_by_any: set = set()
                per_pattern_counts: List[Tuple[str, int, List[str]]] = []
                for base_pkg, pattern in regex_items_info:
                    matched: List[str] = []
                    try:
                        rx = re.compile(pattern)
                    except Exception:
                        # 正则非法时跳过
                        continue
                    for p in input_pkgs:
                        try:
                            if rx.match(p):
                                matched.append(p)
                                covered_by_any.add(p)
                        except Exception:
                            continue
                    # 仅展示前 10 个避免刷屏
                    per_pattern_counts.append((pattern, len(matched), matched[:10]))

                est_reduction = max(0, len(covered_by_any) - len(regex_items_info))
                # 汇总蓝色输出
                summary_lines = [
                    f"[batch] 正则项统计 -> 项数: {len(regex_items_info)}, 覆盖输入记录数: {len(covered_by_any)}, 估算合并减少: {est_reduction}",
                ]
                for pat, cnt, sample in per_pattern_counts:
                    sample_str = ", ".join(sample)
                    summary_lines.append(f"  - /{pat}/ 覆盖 {cnt} 条；示例: {sample_str}{' ...' if cnt > len(sample) else ''}")
                logger.info(_blue("\n".join(summary_lines)))
            except Exception:
                pass
        else:
            logger.info(_yellow("[batch] 本次返回未发现合并为正则表达式的 item"))
    except Exception:
        pass

    if len(parsed) != len(to_query) and not has_regex_merge_items:
        # 数量不符，整批标记 error 以免错配
        for itm in to_query:
            affected = _mark_unknown(session, itm["package"], result_code=2, dry_run=dry_run, stats=stats)
            try:
                meta = {"error": "items_len_mismatch"}
                results.append((itm["package"], 'error', affected, meta))  # type: ignore
            except Exception:
                results.append((itm["package"], 'error', affected))
        return results
    elif len(parsed) != len(to_query) and has_regex_merge_items:
        try:
            logger.info(_yellow(f"[batch] 检测到合并为正则项，跳过严格长度校验：len(parsed)={len(parsed)} vs len(to_query)={len(to_query)}"))
        except Exception:
            pass

    # 4) 逐条处理
    for itm, res in zip(to_query, parsed):
        pkg_in = itm["package"]
        try:
            pkg_out = _normalize_pkg(str(res.get('package')))
            sdk_name = (res.get('sdk_name') or '').strip() or None
            subcategory_id = res.get('subcategory_id', None)
            try:
                subcategory_id = int(subcategory_id) if subcategory_id is not None else None
            except Exception:
                subcategory_id = None
            description = (res.get('description') or None)
            # 兼容 brief/brief_message 两种键名
            _brief_input = res.get('brief') if 'brief' in res else res.get('brief_message')
            brief_val = _clean_nullable_text(_brief_input) if _brief_input is not None else None
            company_name = (res.get('company_name') or None)
            official_web = (res.get('official_web') or None)
            sdk_document_url = (res.get('sdk_document_url') or None)
            privacy_policy_url = (res.get('privacy_policy_url') or None)
            compliance_instructions_url = (res.get('compliance_instructions_url') or None)
            version = (res.get('version') or None)
            detection_type = res.get('detection_type', None)
            try:
                detection_type = int(detection_type) if detection_type is not None else None
            except Exception:
                detection_type = None
            is_regex_rule = res.get('is_regex_rule', None)
            regex_pattern = (res.get('regex_pattern') or None)
        except Exception:
            affected = _mark_unknown(session, pkg_in, result_code=2, dry_run=dry_run, stats=stats)
            results.append((pkg_in, 'error', affected, {"error": "parse_fields_error"}))
            continue

        # 规范化 subcategory_id：<=0 视为无效
        try:
            if subcategory_id is not None and int(subcategory_id) <= 0:
                subcategory_id = None
        except Exception:
            subcategory_id = None

        # 校验 subcategory_id 是否在提供的 subcategory_list 中（强制由 LLM 选择列表内的 id）
        try:
            valid_ids = {int(s.get('id')) for s in subcategories if s.get('id') is not None}
            if subcategory_id is not None and int(subcategory_id) not in valid_ids:
                subcategory_id = None
        except Exception:
            subcategory_id = None

        # 关键字段告警
        try:
            _warn_missing_fields(res, context=f"batch:{pkg_in}")
        except Exception:
            pass

        if not pkg_out:
            pkg_out = pkg_in

        # 在判定前清洗 sdk_name，确保 'null' 等占位被当作 None
        try:
            sdk_name = _clean_nullable_text(sdk_name)
        except Exception:
            sdk_name = None

        if not sdk_name:
            affected = _mark_unknown(session, pkg_in, result_code=0, dry_run=dry_run, stats=stats)
            results.append((pkg_in, 'unknown', affected, {}))
            continue

        # 清洗 description（与单条逻辑一致）
        try:
            try:
                logger.info(f"[batch:{pkg_in}] description_raw={description}")
            except Exception:
                pass
            description = _clean_nullable_text(description)
            try:
                logger.info(f"[batch:{pkg_in}] description_cleaned={description}")
            except Exception:
                pass
        except Exception:
            pass

        # 若给出 sdk_name 但 subcategory_id 缺失/无效 -> 视为错误，整条冷却
        if subcategory_id is None:
            affected = _mark_unknown(session, pkg_in, result_code=2, dry_run=dry_run, stats=stats)
            results.append((pkg_in, 'error', affected, {"error": "missing_subcategory_id"}))
            continue

        # 若给出 sdk_name，但 description 为空/None：仅记录告警，继续处理（选项C）
        if sdk_name and not description:
            try:
                logger.warning(_yellow(f"[batch:{pkg_in}] description 缺失，继续入库但不填 description。"))
            except Exception:
                pass

        # dry-run: 不写库，直接返回元信息供artifact展示
        if dry_run:
            meta = {
                'company_name': company_name,
                'sdk_name': sdk_name,
                'brief': (res.get('brief') or None),
                'description': description,
            }
            results.append((pkg_in, 'linked', 0, meta))  # type: ignore
            continue

        # 在入库前校验正则是否过于宽泛：未锚定或通配过多且字面量不足则拒绝
        if bool(is_regex_rule):
            try:
                regex_clean = _clean_nullable_text(regex_pattern)
            except Exception:
                regex_clean = None
            too_broad = False
            if not (regex_clean and isinstance(regex_clean, str)):
                too_broad = True
            else:
                if not (regex_clean.startswith('^') and regex_clean.endswith('$')):
                    too_broad = True
                else:
                    core = regex_clean[1:-1]
                    # 启发式：大通配 + 字面量不足 视为过宽
                    has_wide = (' .*' in core) or ('.+' in core) or (r'[\s\S]' in core) or (r'[\w\W]' in core)
                    alnum_count = sum(1 for c in core if c.isalnum())
                    # 例外：明显精确的 so 模式，如 ^libxxx\.so$
                    precise_so = bool(re.fullmatch(r"lib[\w.-]+\\\.so", core))
                    if (has_wide and alnum_count < 4) and not precise_so:
                        too_broad = True
            if too_broad:
                logger.warning(_yellow(f"[batch:{pkg_in}] regex too broad, skip insert/backfill: {regex_pattern}"))
                # 统计：正则过宽
                try:
                    if stats is not None:
                        stats['regex_too_broad'] = stats.get('regex_too_broad', 0) + 1
                except Exception:
                    pass
                affected = _mark_unknown(session, pkg_in, result_code=0, dry_run=dry_run, stats=stats)
                results.append((pkg_in, 'unknown', affected, {'reason': 'regex_too_broad'}))
                continue

        kb_row = _upsert_kb(
            session, pkg_out, sdk_name=sdk_name, subcategory_id=subcategory_id,
            description=description, company_name=company_name,
            official_web=official_web, sdk_document_url=sdk_document_url,
            privacy_policy_url=privacy_policy_url, compliance_instructions_url=compliance_instructions_url, version=version,
            brief=brief_val, asked_by=used_provider,
            detection_type=detection_type, is_regex_rule=is_regex_rule, regex_pattern=regex_pattern,
            stats=stats
        )
        # 仅当 pkg_in 与规则一致时才对当前包做直接回填
        do_link_current = True
        if bool(is_regex_rule):
            do_link_current = False
            try:
                regex_clean = _clean_nullable_text(regex_pattern)
            except Exception:
                regex_clean = None
            if regex_clean:
                try:
                    rx0 = re.compile(regex_clean)
                    if rx0.fullmatch(pkg_in):
                        do_link_current = True
                except Exception:
                    pass
        if do_link_current:
            affected = _backfill_kb_id(session, pkg_in, kb_row.id, dry_run=dry_run)
        else:
            affected = 0
            logger.warning(_yellow(f"[batch:{pkg_in}] skip direct link: pkg not fullmatch regex {regex_pattern}"))
            # 统计：直接链接因包不匹配正则被跳过
            try:
                if stats is not None:
                    stats['direct_link_regex_pkg_mismatch_skipped'] = stats.get('direct_link_regex_pkg_mismatch_skipped', 0) + 1
            except Exception:
                pass

        # 若是正则规则，则对全部 potential 项进行正则匹配回填
        try:
            if bool(is_regex_rule):
                # 清洗并校验正则，仅对加了 ^...$ 的模式做回填
                try:
                    regex_clean = _clean_nullable_text(regex_pattern)
                except Exception:
                    regex_clean = None
                if regex_clean and isinstance(regex_clean, str) and regex_clean.startswith('^') and regex_clean.endswith('$'):
                    # 全库安全回填：未提供 candidates 时自动全库收集并做防护
                    matched_count, affected_sum, sample_pkgs = _backfill_kb_by_regex(
                        session, regex_clean, kb_row.id, dry_run=dry_run, candidates=None, stats=stats
                    )
                else:
                    logger.warning(_yellow(f"[batch:{pkg_in}] skip regex backfill due to invalid/unanchored pattern: {regex_pattern}"))
                try:
                    sample_str = ", ".join(sample_pkgs)
                except Exception:
                    sample_str = str(sample_pkgs)
                logger.info(_blue(
                    f"[batch:{pkg_in}] 正则回填：匹配包数={matched_count}, 受影响行数总和={affected_sum}; 示例: {sample_str}{' ...' if matched_count > len(sample_pkgs) else ''}"
                ))
                # 统计：正则回填匹配与影响
                try:
                    if stats is not None:
                        stats['regex_backfill_matched_pkgs'] = stats.get('regex_backfill_matched_pkgs', 0) + int(matched_count)
                        stats['regex_backfill_affected_rows'] = stats.get('regex_backfill_affected_rows', 0) + int(affected_sum)
                except Exception:
                    pass
        except Exception:
            pass
        try:
            meta = {
                'company_name': getattr(kb_row, 'company_name', None),
                'sdk_name': getattr(kb_row, 'sdk_name', None),
                'brief': getattr(kb_row, 'brief_message', None),
                'description': getattr(kb_row, 'description', None),
            }
            results.append((pkg_in, 'linked', affected, meta))  # type: ignore
        except Exception:
            results.append((pkg_in, 'linked', affected))
        # 统计：新建/更新 KB 后的链接（本条）
        try:
            if stats is not None:
                stats['linked'] = stats.get('linked', 0) + 1
        except Exception:
            pass

    return results


def _chunks(lst: List[Any], size: int) -> List[List[Any]]:
    """按固定大小切分列表。
    若 size <= 0，则不切分。
    """
    try:
        size = int(size)
    except Exception:
        size = 0
    if size <= 0:
        return [lst]
    return [lst[i:i + size] for i in range(0, len(lst), size)]


def main(batch_size: int = 20, rpm: int = 8, cooldown_days: int = 7, max_batches: int = 0,
         dry_run: int = 0, provider: str = 'auto', model_name: str = 'gemini-2.5-flash', print_prompt_only: int = 1):
    _ensure_genai_configured()
    engine = get_engine()
    Session = sessionmaker(bind=engine)

    call_times: List[float] = []
    batches = 0

    with Session() as session:
        # 初始化统计
        stats = _init_stats()
        subcategories = fetch_subcategories(session)
        while True:
            if max_batches and batches >= max_batches:
                logger.info("达到 max_batches 限制，停止。")
                break
            cutoff = datetime.now(timezone.utc) - timedelta(days=int(cooldown_days))
            apps = _fetch_candidate_apps(session, cutoff_ts=cutoff, limit=batch_size)
            if not apps:
                logger.info("没有更多待处理的 App（含 potential 包名）。")
                break
            batches += 1

            # 合并本批所有 App 的 potential 包名，一次性提交
            combined_pkgs: List[str] = []
            combined_meta: List[Tuple[str, Optional[str], int]] = []  # (app_id, app_name, pkg_count)
            for (app_id, app_name) in apps:
                pkgs = _fetch_app_all_pkgs(session, app_id, cutoff_ts=cutoff)
                if not pkgs:
                    continue
                combined_pkgs.extend(pkgs)
                combined_meta.append((app_id, app_name, len(pkgs)))

            if not combined_pkgs:
                continue

            # 速率限制：按“整批 App”计一次调用
            rate_limit_guard(call_times, rpm)
            try:
                results = process_batch(
                    session, combined_pkgs, subcategories,
                    provider=provider, model_name=model_name,
                    dry_run=bool(dry_run), print_prompt_only=bool(print_prompt_only), stats=stats
                )
            except Exception:
                logger.exception(
                    "处理合并 App 批失败: %s",
                    ", ".join([f"{aid}({cnt})" for aid, _, cnt in combined_meta])
                )
                results = [(p, 'error', 0) for p in combined_pkgs]

            # 逐条打印结果（兼容 3元/4元返回）
            try:
                meta_str = "; ".join([f"{aid or ''}:{an or ''} count={cnt}" for aid, an, cnt in combined_meta])
                logger.info(f"[apps_batch size={len(apps)}] {meta_str}")
            except Exception:
                pass

            for item in results:
                try:
                    if isinstance(item, (list, tuple)) and len(item) >= 3:
                        pkg, status, affected = item[0], item[1], item[2]
                        err_txt = ''
                        try:
                            if len(item) >= 4 and isinstance(item[3], dict):
                                e = (item[3] or {}).get('error')
                                if e:
                                    err_txt = f", reason={e}"
                        except Exception:
                            err_txt = ''
                    else:
                        pkg, status, affected = None, 'error', 0
                        err_txt = ''
                    logger.info(f"pkg={pkg} -> {status}, affected={affected}{err_txt}")
                except Exception:
                    pass

        # 运行结束输出统计汇总（放在 with 块内，确保 stats 作用域可用）
        try:
            summary_lines = [
                "统计汇总:",
                f"  - total_pkgs: {stats.get('total_pkgs', 0)}",
                f"  - linked: {stats.get('linked', 0)}",
                f"  - unknown: {stats.get('unknown', 0)}",
                f"  - regex_kb_created: {stats.get('regex_kb_created', 0)}",
                f"  - nonregex_kb_created: {stats.get('nonregex_kb_created', 0)}",
                f"  - regex_backfill_matched_pkgs: {stats.get('regex_backfill_matched_pkgs', 0)}",
                f"  - regex_backfill_affected_rows: {stats.get('regex_backfill_affected_rows', 0)}",
                f"  - regex_overmatch_skipped: {stats.get('regex_overmatch_skipped', 0)}",
                f"  - regex_too_broad: {stats.get('regex_too_broad', 0)}",
                f"  - direct_link_regex_pkg_mismatch_skipped: {stats.get('direct_link_regex_pkg_mismatch_skipped', 0)}",
            ]
            logger.info("\n" + "\n".join(summary_lines))
        except Exception:
            pass

    logger.info("完成。")


if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser(description='Ask LLM for potential SDKs and backfill KB links')
    parser.add_argument('--batch-size', type=int, default=20)
    parser.add_argument('--rpm', type=int, default=8)
    parser.add_argument('--cooldown-days', type=int, default=7)
    parser.add_argument('--max-batches', type=int, default=0, help='0 表示不限制')
    parser.add_argument('--dry-run', type=int, default=0, help='1=仅打印不落库')
    parser.add_argument('--provider', type=str, default='auto', help='auto|gemini|deepseek')
    parser.add_argument('--model-name', type=str, default='gemini-2.5-flash')
    parser.add_argument('--print-prompt-only', type=int, default=1, help='1=仅打印prompt不提交模型，0=正常提交')
    args = parser.parse_args()

    main(batch_size=args.batch_size, rpm=args.rpm, cooldown_days=args.cooldown_days,
         max_batches=args.max_batches, dry_run=args.dry_run,
         provider=args.provider, model_name=args.model_name,
         print_prompt_only=args.print_prompt_only)
