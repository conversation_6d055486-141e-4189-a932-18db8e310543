# coding: utf-8
"""
基于 gemini_processor 的工具：
- 从 class_SDKKnowledgeBase 表中查询 subcategory_id 为 NULL 的记录
- 构造包含 Rule1（来源于 class_subcategory 全表） 的提示词
- 使用 Gemini 2.5 Flash 批量分类，返回 subcategory_id（或 null）
- 受限于免费版（约 10 RPM / 500 req/day），做了速率限制与分批控制
- 为避免超上下文窗口，批量内仅携带必要字段

运行：
  python -m tenyy.src.apkinfo_analysis.ask_gemini.ask_gemini_knowledge \
    --batch-size 10 --rpm 8 --max-batches 0 --dry-run 0
参数：
- batch-size：单次发送到模型的条目数（默认 10）
- rpm：每分钟最大请求次数（默认 8，留安全余量）
- max-batches：最多处理多少个批次（0 表示尽可能多，直到无数据）
- dry-run：1 表示仅打印不落库，0 表示实际更新数据库

注意：
- 读取 GEMINI_API_KEY 环境变量，如未设置将沿用 gemini_processor 的全局配置（若其已调用 genai.configure）。
"""
import os
import sys
import json
import time
import logging
from datetime import datetime, UTC
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ProcessPoolExecutor, as_completed

from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine, and_, select

from tenyy.src.config.settings import settings
from tenyy.src.models.class_sdk_knowledge_base import class_SDKKnowledgeBase
from tenyy.src.models.class_subcategory import class_Subcategory

# 复用解析工具与模型引用风格
from tenyy.src.apkinfo_analysis.ask_gemini import gemini_processor
from tenyy.src.apkinfo_analysis.ask_gemini.deepseek_client import get_client as get_ds_client

import google.generativeai as genai
import difflib

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='[%(asctime)s] %(levelname)s - %(message)s')


def _ensure_genai_configured():
    """优先从当前目录 key_config.json 读取 GEMINI_API_KEY，其次读取环境变量 GEMINI_API_KEY。"""
    key_from_file = None
    # 读取同目录 ask_gemini/key_config.json
    try:
        here = os.path.dirname(__file__)
        cfg_path = os.path.join(here, 'key_config.json')
        if os.path.exists(cfg_path):
            with open(cfg_path, 'r', encoding='utf-8') as f:
                data = json.load(f) or {}
                if isinstance(data, dict):
                    k = data.get('GEMINI_API_KEY')
                    if k:
                        key_from_file = str(k)
    except Exception:
        key_from_file = None
    api_key = key_from_file or os.getenv('GEMINI_API_KEY')
    if api_key:
        try:
            genai.configure(api_key=api_key)
            src = 'ask_gemini/key_config.json' if key_from_file else '环境变量'
            logger.info(f"Gemini API 通过{src}配置完成")
        except Exception as e:
            logger.warning(f"Gemini API 配置失败，尝试使用现有全局配置: {e}")


def get_engine():
    return create_engine(settings.DATABASE_URL)


def fetch_rule1_text(session) -> str:
    """从 class_subcategory 构造规则文本（尽量简洁）。"""
    rows: List[class_Subcategory] = session.query(class_Subcategory).order_by(
        class_Subcategory.category_id.asc(), class_Subcategory.id.asc()
    ).all()
    groups: Dict[int, List[Dict[str, Any]]] = {}
    for r in rows:
        groups.setdefault(r.category_id, []).append({
            'id': r.id,
            'name': r.name
        })
    # 紧凑 JSON，控制上下文
    rule = {
        'rule1': {
            'desc': '分类标准，先根据 category_id 分组，最终输出 subcategory_id（必须是以下 id 且不得为 null）',
            'subcategories_by_category': groups
        }
    }
    return json.dumps(rule, ensure_ascii=False)


def fetch_subcategories(session) -> List[Dict[str, Any]]:
    """返回所有子分类的简要信息: [{id,name,category_id}]"""
    rows: List[class_Subcategory] = session.query(class_Subcategory).order_by(
        class_Subcategory.category_id.asc(), class_Subcategory.id.asc()
    ).all()
    out = []
    for r in rows:
        out.append({'id': r.id, 'name': r.name or '', 'category_id': r.category_id})
    return out


def _normalize_text(s: Optional[str]) -> str:
    return (s or '').strip().lower()


def best_guess_subcategory_id(item_text: str, subcategories: List[Dict[str, Any]]) -> Optional[int]:
    """基于简单相似度从子分类名称中选出最可能的 id。"""
    t = _normalize_text(item_text)
    if not t:
        return None
    # 先尝试包含匹配：若子分类名完整出现在文本中，优先选
    best_id = None
    best_score = -1.0
    for sc in subcategories:
        name = _normalize_text(sc.get('name'))
        if not name:
            continue
        score = 0.0
        if name and name in t:
            score = 1.1  # 明确包含，给更高权重
        else:
            # difflib 相似度
            score = difflib.SequenceMatcher(None, t, name).ratio()
        if score > best_score:
            best_score = score
            best_id = sc.get('id')
    return int(best_id) if best_id is not None else None


def fetch_kb_null_batch(session, last_id: int, limit: int) -> List[class_SDKKnowledgeBase]:
    """按 id 递增分页抓取 subcategory_id 为 NULL 的记录。"""
    q = session.query(class_SDKKnowledgeBase).filter(
        class_SDKKnowledgeBase.subcategory_id.is_(None),
        class_SDKKnowledgeBase.id > last_id
    ).order_by(class_SDKKnowledgeBase.id.asc()).limit(limit)
    return q.all()


def build_prompt(rule1_text: str, items: List[class_SDKKnowledgeBase]) -> str:
    minimal_items = []
    for x in items:
        # 极致压缩：仅保留分类最关键线索，避免上下文爆炸
        minimal_items.append({
            'id': x.id,
            'package_prefix': getattr(x, 'package_prefix', None),
            'sdk_name': getattr(x, 'sdk_name', None),
            # 仅给很短的描述线索
            'brief': (getattr(x, 'brief_message', None) or '')[:200],
            # 同时提供完整 description，便于模型参考
            'description': getattr(x, 'description', None)
        })
    prompt = (
        "你是移动应用SDK分类专家。\n"
        "依据下方 Rule1 的子分类清单，将给定 SDK 列表中的每一项映射到一个 subcategory_id。\n"
        "要求：\n"
        "1) 必须返回最可能的 subcategory_id，不得为 null，必须从给定子分类 id 中二择其一。\n"
        "2) 只输出 JSON 数组，每个元素形如 {\"id\": <kb_id>, \"subcategory_id\": <id>}（id 必须来自 Rule1 中的 id）。\n"
        "3) 严禁输出除 JSON 以外的任何文本。\n"
        "4) 若无法完全确定，也必须基于 sdk_name/package_prefix/brief 的线索选择最接近的一项。\n"
        "\nRule1（分类清单）：\n" + rule1_text +
        "\n\n待分类的 SDK 列表：\n" + json.dumps(minimal_items, ensure_ascii=False) +
        "\n\n只返回 JSON 数组（不要解释，不要多余文字）："
    )
    return prompt


def estimate_tokens(text: str) -> int:
    """粗略估算 tokens（平均 4 字符 ≈ 1 token）。"""
    if not text:
        return 0
    return max(1, len(text) // 4)


def call_gemini_classify(prompt: str, max_output_tokens: int = 256, model_name: str = 'gemini-2.5-flash') -> Tuple[Optional[List[Dict[str, Any]]], Optional[int]]:
    model = genai.GenerativeModel(model_name)
    try:
        resp = model.generate_content(
            prompt,
            generation_config=genai.types.GenerationConfig(
                max_output_tokens=max_output_tokens,
                temperature=0.0,
            )
        )
        # 安全访问 text/candidates
        finish = None
        try:
            if resp.candidates:
                finish = getattr(resp.candidates[0], 'finish_reason', None)
        except Exception:
            pass
        try:
            # 尽量解析（即便没有 Part，也不会因访问 resp.text 抛错）
            data_text = ''
            try:
                data_text = getattr(resp, 'text', '') or ''
            except Exception:
                data_text = ''
            data = gemini_processor.extract_json_from_response(data_text)
        except Exception:
            data = None
        if isinstance(data, list):
            return data, finish
        # 无法解析时，尽量打印模型原始返回（避免直接访问 resp.text 以规避无 Part 的异常）
        try:
            cand_cnt = len(getattr(resp, 'candidates', []) or [])
            # 尝试从 parts 中提取文本片段
            snippet = ''
            try:
                if cand_cnt:
                    c0 = resp.candidates[0]
                    content = getattr(c0, 'content', None)
                    parts = getattr(content, 'parts', None)
                    if isinstance(parts, list) and parts:
                        texts = []
                        for part in parts:
                            t = getattr(part, 'text', None)
                            if isinstance(t, str) and t:
                                texts.append(t)
                        if texts:
                            joined = '\n'.join(texts)
                            snippet = (joined[:2000] + '…') if len(joined) > 2000 else joined
            except Exception:
                # parts 提取失败不致命
                pass

            logger.warning(
                "Gemini 返回无法解析为JSON，model=%s, candidates=%d, finish=%s, snippet_len=%d",
                model_name, cand_cnt, str(finish), len(snippet)
            )
            try:
                print("=== Gemini 原始响应诊断开始 ===")
                print(f"model={model_name}, candidates={cand_cnt}, finish={finish}, snippet_len={len(snippet)}")
                if snippet:
                    print(snippet)
                else:
                    print("<no text snippet available>")
                print("=== Gemini 原始响应诊断结束 ===")
            except Exception:
                pass

            # 打印 prompt_feedback（若存在）
            try:
                pf = getattr(resp, 'prompt_feedback', None)
                if pf:
                    logger.warning("prompt_feedback=%s", str(pf))
            except Exception:
                logger.warning("读取 prompt_feedback 失败", exc_info=True)

            # 进一步打印候选的结构化关键信息（尽量安全）
            try:
                if cand_cnt:
                    c0 = resp.candidates[0]
                    parts_types = []
                    try:
                        p = getattr(getattr(c0, 'content', None), 'parts', None)
                        if isinstance(p, list):
                            parts_types = [type(x).__name__ for x in p][:10]
                    except Exception:
                        logger.warning("读取候选 parts 失败", exc_info=True)
                    logger.warning(
                        "候选#0信息: safety_ratings=%s, groundedness_metadata=%s, parts(sample_types)=%s",
                        str(getattr(c0, 'safety_ratings', None)),
                        str(getattr(c0, 'grounding_metadata', None)),
                        parts_types,
                    )
            except Exception:
                logger.warning("读取候选信息失败", exc_info=True)
        except Exception:
            logger.warning("无法记录原始响应调试信息", exc_info=True)
        return None, finish
    except Exception as e:
        logger.error(f"Gemini 调用失败: {e}")
        return None, None


def _classify_prompt_worker(args: Dict[str, Any]) -> Tuple[Optional[List[Dict[str, Any]]], str]:
    """子进程执行：根据参数对一个 prompt 进行分类。
    返回 (results, provider_used)，provider_used in {'gemini','deepseek','none'}。
    注意：子进程内各自初始化 DeepSeek 客户端（在 call_deepseek_classify 内已处理）。
    """
    prompt: str = args.get('prompt')
    model_name: str = args.get('model_name', 'gemini-2.5-flash')
    deepseek_only: bool = bool(args.get('deepseek_only', False))

    # 若已进入 DeepSeek-only，直接用 DeepSeek
    if deepseek_only:
        res, _ = call_deepseek_classify(prompt)
        return (res, 'deepseek' if res is not None else 'none')

    # 否则先试 Gemini，再兜底 DeepSeek
    res, finish = call_gemini_classify(prompt, model_name=model_name)
    if res is not None:
        return (res, 'gemini')
    res2, _ = call_deepseek_classify(prompt)
    return (res2, 'deepseek' if res2 is not None else 'none')


def call_deepseek_classify(prompt: str, max_output_tokens: int = 512) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
    """使用 DeepSeek 进行兜底分类。
    返回 (results, source)；results 为 list 则成功，否则为 None。
    """
    try:
        client = get_ds_client()
    except Exception as e:
        logger.error(f"DeepSeek 客户端初始化失败: {e}")
        return None, None
    try:
        resp = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "你是一个严格的JSON输出助手。只输出JSON数组，不要任何多余文本。必须从用户提供的子分类ID列表中选择一个最接近的 subcategory_id，禁止返回 null 或空。"},
                {"role": "user", "content": prompt},
            ],
            temperature=0.0,
            max_tokens=max_output_tokens,
            stream=False,
        )
        content = ''
        try:
            if resp and getattr(resp, 'choices', None):
                content = resp.choices[0].message.content or ''
        except Exception:
            content = ''
        data = None
        try:
            data = gemini_processor.extract_json_from_response(content)
        except Exception:
            data = None
        if isinstance(data, list):
            return data, 'deepseek'
        # 打印片段便于诊断
        snippet = (content[:1200] + '…') if content and len(content) > 1200 else content
        logger.warning("DeepSeek 返回无法解析为JSON，snippet_len=%d", len(snippet or ''))
        try:
            print("=== DeepSeek 原始响应诊断开始 ===")
            print(f"snippet_len={len(snippet or '')}")
            if snippet:
                print(snippet)
            else:
                print("<no text snippet available>")
            print("=== DeepSeek 原始响应诊断结束 ===")
        except Exception:
            pass
        return None, 'deepseek'
    except Exception as e:
        logger.error(f"DeepSeek 调用失败: {e}")
        return None, None


def rate_limit_guard(call_times: List[float], rpm: int):
    """简单速率限制：过去 60s 内不超过 rpm 次。"""
    now = time.time()
    # 清理 60s 前的记录
    call_times[:] = [t for t in call_times if now - t < 60]
    if len(call_times) >= rpm:
        sleep_time = 60 - (now - call_times[0]) + 1
        sleep_time = max(1.0, sleep_time)
        logger.info(f"速率限制触发，睡眠 {sleep_time:.1f}s …")
        time.sleep(sleep_time)
        now = time.time()
        call_times[:] = [t for t in call_times if now - t < 60]
    call_times.append(now)


def apply_updates(session, results: List[Dict[str, Any]], dry_run: bool) -> Tuple[int, int]:
    """将分类结果写回数据库。
    返回 (classified_count, checked_only_count)：
    - classified_count：subcategory_id 被成功设置（非 None）的条数
    - checked_only_count：仅更新 last_checked_at（subcategory_id 为 None）的条数
    规则：
    - 若提供了 subcategory_id（非 None），更新该字段并刷新 last_checked_at。
    - 若 subcategory_id 为 None，仅刷新 last_checked_at，表示已检查但不确定。
    - dry_run 时不写库，只打印将会进行的更新。
    """
    classified = 0
    checked_only = 0
    now_ts = datetime.now(UTC)
    for item in results:
        try:
            kb_id = int(item.get('id'))
        except Exception:
            continue
        sub_id = item.get('subcategory_id', None)
        try:
            sub_id = int(sub_id) if sub_id is not None else None
        except Exception:
            sub_id = None
        if dry_run:
            if sub_id is None:
                logger.info(f"[DRY-RUN] id={kb_id} -> 仅更新 last_checked_at")
                checked_only += 1
            else:
                logger.info(f"[DRY-RUN] id={kb_id} -> subcategory_id={sub_id}, 并更新 last_checked_at")
                classified += 1
            continue

        row = session.get(class_SDKKnowledgeBase, kb_id)
        if not row:
            continue
        # 按规则更新
        if sub_id is not None:
            row.subcategory_id = sub_id
            classified += 1
        else:
            checked_only += 1
        row.last_checked_at = now_ts
    # 仅在有任何更新时提交
    if not dry_run and (classified or checked_only):
        session.commit()
    return classified, checked_only


def mark_status_asked(session, items: List[Any], dry_run: bool) -> int:
    """将即将请求模型的记录标记为 status='asked'。
    返回成功标记的条数；dry_run 时仅打印不落库。
    """
    try:
        ids: List[int] = []
        for it in items:
            kb_id = None
            if isinstance(it, dict):
                kb_id = it.get('id') or it.get('kb_id')
            else:
                kb_id = getattr(it, 'id', None)
            if kb_id is not None:
                try:
                    ids.append(int(kb_id))
                except Exception:
                    continue
        if not ids:
            return 0
        if dry_run:
            try:
                logger.info(f"[DRY-RUN] 标记 status='asked' {len(ids)} 条: {ids[:10]}{'...' if len(ids)>10 else ''}")
            except Exception:
                pass
            return 0
        # 批量查询并更新
        rows = (
            session.query(class_SDKKnowledgeBase)
            .filter(class_SDKKnowledgeBase.id.in_(ids))
            .all()
        )
        cnt = 0
        for r in rows:
            try:
                r.status = 'asked'
                cnt += 1
            except Exception:
                continue
        if cnt:
            session.commit()
        return cnt
    except Exception:
        logger.warning("标记 status='asked' 失败", exc_info=True)
        return 0


def main(batch_size: int = 10, rpm: int = 8, max_batches: int = 0, dry_run: int = 0, model_name: str = 'gemini-2.5-flash', workers: int = 1, provider: str = 'auto', log_null_sample: int = 0, force_non_null: int = 0):
    _ensure_genai_configured()
    engine = get_engine()
    Session = sessionmaker(bind=engine)

    call_times: List[float] = []
    total_classified = 0
    total_checked_only = 0
    total_sent = 0
    total_failed_items = 0
    total_failed_slices = 0

    last_id = 0
    batch_count = 0
    # provider 控制：auto（默认先 Gemini 失败再切）、deepseek（直接 DeepSeek-only）
    deepseek_only = (provider.lower() == 'deepseek')
    if deepseek_only:
        try:
            logger.info("启动于 DeepSeek-only 模式：跳过 Gemini、不限速、不 sleep")
        except Exception:
            pass

    with Session() as session:
        rule1_text = fetch_rule1_text(session)
        subcategories = fetch_subcategories(session)
        while True:
            if max_batches and batch_count >= max_batches:
                logger.info("达到 max_batches 限制，停止。")
                break
            items = fetch_kb_null_batch(session, last_id=last_id, limit=batch_size)
            if not items:
                logger.info("没有更多待分类记录。")
                break
            last_id = items[-1].id
            batch_count += 1

            # 先标记本批次为 asked
            try:
                _ = mark_status_asked(session, items, dry_run=bool(dry_run))
            except Exception:
                logger.warning("批次 asked 标记失败", exc_info=True)

            # 动态预算：尽量控制在 ~120k tokens 内（保守，避免服务端其他限制）
            current_items = items
            retry = 0
            while True:
                prompt = build_prompt(rule1_text, current_items)
                tok = estimate_tokens(prompt)
                if tok > 120_000 and len(current_items) > 1:
                    # 超预算，减半
                    current_items = current_items[: max(1, len(current_items)//2) ]
                    logger.info(f"提示过长({tok} tokens 估算)，降批量到 {len(current_items)} 继续尝试…")
                    continue
                break

            # 注：不再在发送前标记 asked，改为在本批处理完成后再标记

            # 速率限制（DeepSeek-only 模式不做限速）
            if not deepseek_only:
                rate_limit_guard(call_times, rpm)

            # 并发：将当前批切分为 workers 份（至少 1）并行分类；避免多进程操作 DB，只并行模型调用
            results_all: List[Dict[str, Any]] = []
            used_deepseek = False
            batch_failed_items = 0
            batch_failed_slices = 0
            # 为强制非空准备：构建 id->文本 映射
            id2text: Dict[int, str] = {}
            try:
                for it in current_items:
                    kb_id = getattr(it, 'id', None)
                    if kb_id is None:
                        continue
                    parts = [
                        getattr(it, 'sdk_name', None),
                        getattr(it, 'package_prefix', None),
                        (getattr(it, 'brief_message', None) or '')[:400],
                        getattr(it, 'description', None),
                    ]
                    id2text[int(kb_id)] = ' '.join([p for p in parts if p])
            except Exception:
                id2text = {}

            if workers <= 1:
                # 保持原先逻辑（单进程）
                if deepseek_only:
                    try:
                        logger.info(
                            "DeepSeek-only 模式: items=%d, token_rough=%d",
                            len(current_items), estimate_tokens(prompt)
                        )
                    except Exception:
                        pass
                    results, _ = call_deepseek_classify(prompt)
                    finish = None
                else:
                    try:
                        logger.info(
                            "准备调用Gemini: items=%d, token_rough=%d",
                            len(current_items), estimate_tokens(prompt)
                        )
                    except Exception:
                        pass
                    results, finish = call_gemini_classify(prompt, model_name=model_name)
                # 干跑时打印 prompt 与解析后的回复（回复使用黄色高亮）
                try:
                    if bool(dry_run) and results is not None:
                        print("=== Prompt (dry-run) ===")
                        print(prompt)
                        print("\033[33m=== Reply (parsed, dry-run) ===\033[0m")
                        try:
                            print("\033[33m" + json.dumps(results, ensure_ascii=False) + "\033[0m")
                        except Exception:
                            print("\033[33m" + str(results) + "\033[0m")
                except Exception:
                    pass
                if results is None:
                    # 若失败，尝试将批量再减半重试最多 2 次
                    if len(current_items) > 1 and retry < 2:
                        retry += 1
                        current_items = current_items[: max(1, len(current_items)//2) ]
                        logger.warning(f"本批次返回不可解析(finish={finish})，降批量到 {len(current_items)} 重试#{retry}…")
                        # 速率限制
                        if not deepseek_only:
                            rate_limit_guard(call_times, rpm)
                        prompt = build_prompt(rule1_text, current_items)
                        # 第一次重试仍失败时，最后再尝试备用模型以排除模型侧问题
                        fallback_model = 'gemini-1.5-flash' if model_name == 'gemini-2.5-flash' else model_name
                        use_model = model_name if retry == 1 else fallback_model
                        if retry == 2 and fallback_model != model_name:
                            logger.info(f"切换备用模型重试: {fallback_model}")
                        results, finish = call_gemini_classify(prompt, model_name=use_model)
                    # 若 Gemini 仍不行，使用 DeepSeek 兜底
                    if results is None:
                        logger.info("尝试使用 DeepSeek 兜底…（后续将切换为 DeepSeek-only 模式）")
                        deepseek_only = True
                        prompt = build_prompt(rule1_text, current_items)
                        results, _ = call_deepseek_classify(prompt)
                        used_deepseek = True
                    if results is None:
                        logger.warning("本批次模型返回不可解析，跳过。")
                        batch_failed_items = len(current_items)
                        batch_failed_slices = 1
                        total_failed_items += batch_failed_items
                        total_failed_slices += batch_failed_slices
                        # 失败也要标记 last_checked_at
                        failed_updates = []
                        for it in current_items:
                            kb = None
                            if isinstance(it, dict):
                                kb = it.get('id') or it.get('kb_id')
                            else:
                                kb = getattr(it, 'id', None)
                            if kb is not None:
                                failed_updates.append({'id': kb, 'subcategory_id': None})
                        if failed_updates:
                            _ = apply_updates(session, failed_updates, dry_run=bool(dry_run))
                        continue
                # 统计成功/失败数量（保守按返回条目数计算）
                ok_count = len(results) if isinstance(results, list) else 0
                fail_count = max(0, len(current_items) - ok_count)
                if fail_count > 0:
                    batch_failed_items += fail_count
                    batch_failed_slices += 1  # 单批单请求视作 1 个切片
                    total_failed_items += fail_count
                    total_failed_slices += 1
                # 统计成功/失败数量（保守按返回条目数计算）并给失败条目补写 last_checked_at
                ok_count = len(results) if isinstance(results, list) else 0
                fail_count = max(0, len(current_items) - ok_count)
                if fail_count > 0:
                    # 计算失败 id
                    succ_ids = set()
                    for r in results:
                        if isinstance(r, dict):
                            rid = r.get('id') or r.get('kb_id')
                            if rid is not None:
                                succ_ids.add(rid)
                    batch_ids = []
                    for it in current_items:
                        kb = None
                        if isinstance(it, dict):
                            kb = it.get('id') or it.get('kb_id')
                        else:
                            kb = getattr(it, 'id', None)
                        if kb is not None:
                            batch_ids.append(kb)
                    failed_ids = [i for i in batch_ids if i not in succ_ids]
                    # 扩展结果用于仅更新时间戳
                    for fid in failed_ids:
                        results.append({'id': fid, 'subcategory_id': None})
                    batch_failed_items += len(failed_ids)
                    batch_failed_slices += 1
                    total_failed_items += len(failed_ids)
                    total_failed_slices += 1
                results_all.extend(results)
            else:
                # 多进程并发：切分 current_items
                chunk_size = max(1, (len(current_items) + workers - 1) // workers)
                chunks = [current_items[i:i+chunk_size] for i in range(0, len(current_items), chunk_size)]
                tasks = []
                fut2exp = {}
                with ProcessPoolExecutor(max_workers=workers) as ex:
                    for ch in chunks:
                        p = build_prompt(rule1_text, ch)
                        fut = ex.submit(_classify_prompt_worker, {
                            'prompt': p,
                            'model_name': model_name,
                            'deepseek_only': deepseek_only,
                        })
                        tasks.append(fut)
                        fut2exp[fut] = len(ch)
                    for fut in as_completed(tasks):
                        try:
                            r, provider = fut.result()
                        except Exception as e:
                            logger.error(f"并发子任务异常: {e}")
                            r, provider = None, 'none'
                        exp = fut2exp.get(fut, 0)
                        if isinstance(r, list):
                            results_all.extend(r)
                            okn = len(r)
                            if okn < exp:
                                failn = exp - okn
                                batch_failed_items += failn
                                batch_failed_slices += 1
                                total_failed_items += failn
                                total_failed_slices += 1
                        else:
                            # 整个切片失败
                            batch_failed_items += exp
                            batch_failed_slices += 1
                            total_failed_items += exp
                            total_failed_slices += 1
                        if provider == 'deepseek':
                            used_deepseek = True
                if not results_all:
                    logger.warning("本批次模型返回不可解析，跳过。")
                    # 此时 batch_failed 已在上面累加；同时也要为所有条目标记 last_checked_at
                    failed_updates = []
                    for it in current_items:
                        kb = None
                        if isinstance(it, dict):
                            kb = it.get('id') or it.get('kb_id')
                        else:
                            kb = getattr(it, 'id', None)
                        if kb is not None:
                            failed_updates.append({'id': kb, 'subcategory_id': None})
                    if failed_updates:
                        _ = apply_updates(session, failed_updates, dry_run=bool(dry_run))
                    continue
                if used_deepseek and not deepseek_only:
                    logger.info("检测到子任务使用了 DeepSeek，切换为 DeepSeek-only 模式（后续不再限速）")
                    deepseek_only = True
                # 并发成功但部分失败：补写失败条目的 last_checked_at
                succ_ids = set()
                for r in results_all:
                    if isinstance(r, dict):
                        rid = r.get('id') or r.get('kb_id')
                        if rid is not None:
                            succ_ids.add(rid)
                batch_ids = []
                for it in current_items:
                    kb = None
                    if isinstance(it, dict):
                        kb = it.get('id') or it.get('kb_id')
                    else:
                        kb = getattr(it, 'id', None)
                    if kb is not None:
                        batch_ids.append(kb)
                extra_failed_ids = [i for i in batch_ids if i not in succ_ids]
                for fid in extra_failed_ids:
                    results_all.append({'id': fid, 'subcategory_id': None})

            # 如开启强制非空，将 None 替换为最可能的 id
            coerced = 0
            if force_non_null and results_all:
                for r in results_all:
                    if not isinstance(r, dict):
                        continue
                    rid = r.get('id')
                    sub_id = r.get('subcategory_id')
                    if sub_id is None or str(sub_id).strip().lower() in ('', 'null'):
                        text = id2text.get(int(rid)) if rid is not None else None
                        guess = best_guess_subcategory_id(text or '', subcategories)
                        if guess is not None:
                            r['subcategory_id'] = guess
                            coerced += 1
                if coerced:
                    logger.info(f"强制非空：基于相似度补全 {coerced} 条 subcategory_id")

            # 先写库并得到真实分类与仅检查数量
            classified, checked_only = apply_updates(session, results_all, dry_run=bool(dry_run))
            # 最后再标记 asked（仅在本批处理完成后）
            try:
                asked_n = mark_status_asked(session, current_items, dry_run=bool(dry_run))
                if asked_n:
                    logger.info(f"已标记 status='asked' {asked_n} 条")
            except Exception:
                logger.warning("标记 status='asked' 过程出现异常", exc_info=True)
            total_classified += classified
            total_checked_only += checked_only
            total_sent += len(current_items)
            # 原因拆解：
            # - 仅检查数 = 模型返回null + 切片失败补写
            # - 其中“切片失败补写”已计入 batch_failed_items（缺失/不可解析）
            #   因此可用近似：模型返回null ≈ max(0, checked_only - batch_failed_items)
            approx_model_null = max(0, checked_only - batch_failed_items)
            # 可选输出部分 null 的样本 id
            if log_null_sample and results_all:
                sample_ids = []
                for r in results_all:
                    try:
                        rid = r.get('id') if isinstance(r, dict) else None
                        sub_id = r.get('subcategory_id') if isinstance(r, dict) else None
                        if rid is not None and (sub_id is None or str(sub_id).strip() == '' or str(sub_id).lower() == 'null'):
                            sample_ids.append(rid)
                            if len(sample_ids) >= log_null_sample:
                                break
                    except Exception:
                        continue
                if sample_ids:
                    logger.info(f"示例：模型返回 null 的样本ID(最多 {log_null_sample}): {sample_ids}")
            logger.info(
                f"批次#{batch_count}: 发送 {len(current_items)} 条，更新 {classified} 条，已检查未分类 {checked_only} 条，"
                f"本批失败 {batch_failed_items} 条（{batch_failed_slices} 切片），"
                f"累计失败 {total_failed_items} 条（{total_failed_slices} 切片），累计更新 {total_classified} 条，累计仅检查 {total_checked_only} 条"
            )
            logger.info(
                f"原因拆解：模型返回 null 约 {approx_model_null} 条；切片/缺失 {batch_failed_items} 条"
            )
            # DeepSeek-only 模式下不休眠
            if not deepseek_only:
                # 小幅休眠，进一步降低触发概率
                time.sleep(1)

    logger.info(
        f"完成。总发送 {total_sent} 条，累计更新 {total_classified} 条，累计仅检查 {total_checked_only} 条，"
        f"累计失败 {total_failed_items} 条（{total_failed_slices} 切片）。dry_run={bool(dry_run)}"
    )


if __name__ == '__main__':
    import argparse
    p = argparse.ArgumentParser()
    p.add_argument('--batch-size', type=int, default=10)
    p.add_argument('--rpm', type=int, default=8)
    p.add_argument('--max-batches', type=int, default=0)
    p.add_argument('--dry-run', type=int, default=0)
    p.add_argument('--model', type=str, default='gemini-2.5-flash')
    p.add_argument('--workers', type=int, default=1)
    p.add_argument('--provider', type=str, default='auto', help="auto|deepseek")
    p.add_argument('--log-null-sample', type=int, default=0, help='每批打印部分模型返回null的样本ID数量(0为不打印)')
    p.add_argument('--force-non-null', type=int, default=0, help='1=对模型返回的null进行相似度补全，0=不补全')
    args = p.parse_args()
    main(batch_size=args.batch_size, rpm=args.rpm, max_batches=args.max_batches, dry_run=args.dry_run, model_name=args.model, workers=args.workers, provider=args.provider, log_null_sample=args.log_null_sample, force_non_null=args.force_non_null)
