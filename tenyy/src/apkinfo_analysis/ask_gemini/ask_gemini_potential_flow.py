# -*- coding: utf-8 -*-
"""
ask_gemini_potential_flow.py

基于 `ask_gemini_potential.py` 的 Prefect Flow 封装：
- 以 Flow 参数形式暴露 batch_size/rpm/cooldown_days/max_batches/dry_run/provider/model_name/print_prompt_only；
- 循环批量处理 potential 包名前缀，复用模块内的查询与批处理逻辑；
- 生成 Prefect Artifacts（表格与Markdown汇总）。

在 Prefect UI 中可直接调整参数运行；在部署时由 prefect.yaml 传参。
"""
import os
import sys
import json
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Tuple

from prefect import flow, task, get_run_logger
from prefect.artifacts import create_table_artifact, create_markdown_artifact

# 修正路径，使得可直接导入项目内部模块
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

# 复用 ask_gemini_potential 的核心方法
from tenyy.src.apkinfo_analysis.ask_gemini.ask_gemini_potential import (
    get_engine,
    process_batch,
    _normalize_pkg,
    _find_kb_by_pkg,
    _fetch_candidate_apps,
    _fetch_app_all_pkgs,
)
from tenyy.src.apkinfo_analysis.ask_gemini.ask_gemini_knowledge import (
    _ensure_genai_configured,
    rate_limit_guard,
    fetch_subcategories,
)
from sqlalchemy.orm import sessionmaker


@task
def fetch_candidates_and_meta(batch_size: int, cooldown_days: int) -> Dict[str, Any]:
    """读取一批待处理 App 及必要元信息。
    注意：此处 batch_size 表示 App 数量。
    """
    plog = get_run_logger()
    engine = get_engine()
    Session = sessionmaker(bind=engine)
    with Session() as session:
        cutoff = datetime.now(timezone.utc) - timedelta(days=int(cooldown_days))
        apps = _fetch_candidate_apps(session, cutoff_ts=cutoff, limit=batch_size)
        subcategories = fetch_subcategories(session)
    plog.info(f"领取到 {len(apps)} 个待处理 App")
    return {"apps": apps, "subcategories": subcategories}


@task
def process_one_chunk(
    chunk: List[str],
    subcategories: List[Dict[str, Any]],
    provider: str,
    model_name: str,
    dry_run: bool,
    print_prompt_only: bool,
) -> List[Tuple[str, str, int]]:
    """处理一小批包名，返回 [(pkg, status, affected)]。"""
    plog = get_run_logger()
    engine = get_engine()
    Session = sessionmaker(bind=engine)
    with Session() as session:
        results = process_batch(
            session,
            [_normalize_pkg(p) for p in chunk],
            subcategories,
            provider=provider,
            model_name=model_name,
            dry_run=dry_run,
            print_prompt_only=print_prompt_only,
        )
    # 简要日志（兼容 3元/4元返回）
    try:
        for item in results:
            try:
                if isinstance(item, (list, tuple)) and len(item) >= 3:
                    pkg, status, affected = item[0], item[1], item[2]
                else:
                    pkg, status, affected = None, 'error', 0
                # 打印 error 原因（若有 meta.error）
                if isinstance(item, (list, tuple)) and len(item) >= 4 and isinstance(item[3], dict):
                    err = item[3].get('error') if status == 'error' else None
                else:
                    err = None
                if status == 'error' and err:
                    plog.error(f"chunk item: pkg={pkg} -> {status}, affected={affected}, reason={err}")
                else:
                    plog.info(f"chunk item: pkg={pkg} -> {status}, affected={affected}")
            except Exception:
                pass
    except Exception:
        pass
    return results


@task
def process_one_app(
    app: Tuple[str, str],
    subcategories: List[Dict[str, Any]],
    provider: str,
    model_name: str,
    dry_run: bool,
    print_prompt_only: bool,
    cooldown_days: int,
) -> List[Tuple[str, str, int]]:
    """处理单个 App：获取该 App 下所有包前缀并一次性处理。"""
    plog = get_run_logger()
    engine = get_engine()
    Session = sessionmaker(bind=engine)
    app_id, app_name = None, None
    try:
        if isinstance(app, (list, tuple)) and len(app) >= 1:
            app_id = str(app[0]) if app[0] is not None else None
            app_name = app[1] if len(app) >= 2 else None
    except Exception:
        pass
    if not app_id:
        plog.warning("process_one_app: app_id 缺失，跳过。")
        return []

    cutoff = datetime.now(timezone.utc) - timedelta(days=int(cooldown_days))
    with Session() as session:
        pkgs = _fetch_app_all_pkgs(session, app_id=app_id, cutoff_ts=cutoff)
        if not pkgs:
            plog.info(f"App {app_id} / {app_name or ''} 无待处理包名前缀，跳过。")
            return []
        plog.info(f"App {app_id} / {app_name or ''} 待处理包数：{len(pkgs)}")
        results = process_batch(
            session,
            [_normalize_pkg(p) for p in pkgs],
            subcategories,
            provider=provider,
            model_name=model_name,
            dry_run=dry_run,
            print_prompt_only=print_prompt_only,
        )

    # 与 process_one_chunk 相同的简要日志
    try:
        for item in results:
            try:
                if isinstance(item, (list, tuple)) and len(item) >= 3:
                    pkg, status, affected = item[0], item[1], item[2]
                else:
                    pkg, status, affected = None, 'error', 0
                if isinstance(item, (list, tuple)) and len(item) >= 4 and isinstance(item[3], dict):
                    err = item[3].get('error') if status == 'error' else None
                else:
                    err = None
                if status == 'error' and err:
                    plog.error(f"app item: app={app_id} pkg={pkg} -> {status}, affected={affected}, reason={err}")
                else:
                    plog.info(f"app item: app={app_id} pkg={pkg} -> {status}, affected={affected}")
            except Exception:
                pass
    except Exception:
        pass
    return results


@flow(name="ask_gemini_potential_flow")
def ask_gemini_potential_flow(
    batch_size: int = 5,
    rpm: int = 15,
    cooldown_days: int = 7,
    max_batches: int = 2,
    dry_run: bool = True,
    provider: str = "gemini",
    model_name: str = "gemini-2.5-flash",
    print_prompt_only: bool = False,
):
    """将 ask_gemini_potential 的主逻辑封装为 Prefect Flow，并输出Artifacts。"""
    rlog = get_run_logger()
    _ensure_genai_configured()

    call_times: List[float] = []
    batches = 0

    all_rows: List[Dict[str, Any]] = []

    while True:
        if max_batches and batches >= max_batches:
            rlog.info("达到 max_batches 限制，停止。")
            break

        meta = fetch_candidates_and_meta.submit(batch_size, cooldown_days)
        apps = meta.result().get("apps", [])
        subcategories = meta.result().get("subcategories", [])
        if not apps:
            rlog.info("没有更多待处理的 App。")
            break
        batches += 1

        # 按 App 并行处理（每个 App 即一小批）
        chunk_futures = []
        total_chunks = len(apps)
        for ci, app in enumerate(apps, 1):
            rate_limit_guard(call_times, rpm)
            fut = process_one_app.submit(
                app,
                subcategories,
                provider,
                model_name,
                bool(dry_run),
                bool(print_prompt_only),
                int(cooldown_days),
            )
            chunk_futures.append((ci, fut))

        # 收集结果
        for ci, fut in chunk_futures:
            try:
                results = fut.result()
            except Exception as e:
                rlog.error(f"收集 chunk={ci}/{total_chunks} 结果失败: {e}")
                results = []
            # 按结果构造行，若已关联（linked）则补充 company/sdk_name/brief
            engine = get_engine()
            Session = sessionmaker(bind=engine)
            for item in results:
                # 兼容 3元/4元返回
                pkg = status = None
                affected = 0
                meta = None
                try:
                    if isinstance(item, (list, tuple)):
                        if len(item) >= 3:
                            pkg, status, affected = item[0], item[1], item[2]
                        if len(item) >= 4 and isinstance(item[3], dict):
                            meta = item[3]
                except Exception:
                    pass

                row = {
                    "batch": batches,
                    "chunk": ci,
                    "package": pkg,
                    "status": status,
                    "affected": int(affected),
                    # 预置列，保证 Artifact 总是显示这些列
                    "company": None,
                    "sdk_name": None,
                    "brief": None,
                    "description": None,
                    "error": None,
                }

                # 优先使用上游返回的 meta；若无 meta 且为 linked，再回退查库
                if status == "linked":
                    filled = False
                    if isinstance(meta, dict):
                        try:
                            row.update({
                                "company": meta.get("company_name"),
                                "sdk_name": meta.get("sdk_name"),
                                "brief": meta.get("brief"),
                                "description": meta.get("description"),
                            })
                            filled = True
                        except Exception:
                            filled = False
                    if not filled:
                        try:
                            with Session() as s:
                                kb = _find_kb_by_pkg(s, _normalize_pkg(pkg))
                                if kb is not None:
                                    row.update({
                                        "company": getattr(kb, "company_name", None),
                                        "sdk_name": getattr(kb, "sdk_name", None),
                                        "brief": getattr(kb, "brief_message", None),
                                        "description": getattr(kb, "description", None),
                                    })
                        except Exception:
                            pass

                # error 场景：记录上游返回的错误原因，便于排查
                if status == "error" and isinstance(meta, dict):
                    try:
                        row["error"] = meta.get("error")
                    except Exception:
                        pass

                all_rows.append(row)

    # 汇总与Artifacts
    try:
        # 表格
        create_table_artifact(
            key="ask-gemini-potential-rows",
            table=all_rows,
            description=f"ask_gemini_potential_flow 明细（共 {len(all_rows)} 条）",
        )
        # 汇总 Markdown（状态统计 + 错误 Top-N）
        from collections import Counter, defaultdict
        status_cnt = Counter([r.get("status") for r in all_rows])
        err_cnt = Counter([r.get("error") for r in all_rows if r.get("status") == "error" and r.get("error")])
        top_err = err_cnt.most_common(10)
        lines = []
        lines.append("# ask_gemini_potential 汇总")
        lines.append("")
        lines.append("## 参数")
        lines.append(f"- batch_size: {batch_size}")
        lines.append(f"- max_batches: {max_batches}")
        lines.append(f"- rpm: {rpm}")
        lines.append(f"- cooldown_days: {cooldown_days}")
        lines.append(f"- dry_run: {dry_run}")
        lines.append(f"- provider: {provider}")
        lines.append(f"- model_name: {model_name}")
        lines.append("")
        lines.append("## 状态统计")
        for k in sorted(status_cnt.keys()):
            lines.append(f"- {k}: {status_cnt.get(k, 0)}")
        lines.append("")
        lines.append("## 错误 Top-N（按原因）")
        if top_err:
            for reason, cnt in top_err:
                safe_reason = str(reason).replace("\n", " ")
                lines.append(f"- {cnt} × {safe_reason}")
        else:
            lines.append("- 无")
        create_markdown_artifact(
            key="ask-gemini-potential-summary",
            markdown="\n".join(lines),
            description="状态分布与错误原因统计"
        )
    except Exception:
        pass

    rlog.info("ask_gemini_potential_flow 完成")


def main():
    import argparse
    parser = argparse.ArgumentParser(description="Ask Gemini Potential Flow (Prefect)")
    parser.add_argument("--batch-size", type=int, default=5)
    parser.add_argument("--rpm", type=int, default=15)
    parser.add_argument("--cooldown-days", type=int, default=7)
    parser.add_argument("--max-batches", type=int, default=2)
    parser.add_argument("--dry-run", type=int, default=0)
    parser.add_argument("--provider", type=str, default="gemini")
    parser.add_argument("--model-name", type=str, default="gemini-2.5-flash")
    parser.add_argument("--print-prompt-only", type=int, default=0)
    args = parser.parse_args()

    ask_gemini_potential_flow(
        batch_size=args.batch_size,
        rpm=args.rpm,
        cooldown_days=args.cooldown_days,
        max_batches=args.max_batches,
        dry_run=bool(args.dry_run),
        provider=args.provider,
        model_name=args.model_name,
        print_prompt_only=bool(args.print_prompt_only),
    )


if __name__ == "__main__":
    sys.exit(main() or 0)
