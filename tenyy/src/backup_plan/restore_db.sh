#!/bin/bash

# Tenyy 数据库恢复脚本
# 用于从备份文件恢复 PostgreSQL 数据库

set -e  # 遇到错误时退出

# 数据库连接信息（从环境变量获取或使用默认值），适配容器环境
DB_HOST=${POSTGRES_HOST:-${DB_HOST:-"app-db"}}

# 终止连接并删除数据库（用于重建模式）
terminate_and_drop_db() {
    local db_name="$1"
    log "禁用新连接到数据库: ${db_name}"
    psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d postgres -v ON_ERROR_STOP=1 \
      -c "UPDATE pg_database SET datallowconn=false WHERE datname = '${db_name}';" || true

    log "终止现有连接: ${db_name}"
    psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d postgres -v ON_ERROR_STOP=1 \
      -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '${db_name}' AND pid <> pg_backend_pid();" || true

    log "删除数据库(若存在): ${db_name}"
    psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d postgres -v ON_ERROR_STOP=1 \
      -c "DROP DATABASE IF EXISTS \"${db_name}\";" || true
}
DB_PORT=${POSTGRES_PORT:-${DB_PORT:-"5432"}}
DB_NAME=${POSTGRES_DB:-${DB_NAME:-"tenyy_app"}}
DB_USER=${POSTGRES_USER:-${DB_USER:-"admin"}}
DB_PASSWORD=${POSTGRES_PASSWORD:-${DB_PASSWORD:-"zhangdi168"}}

# 导出密码环境变量供 pg_dump/pg_restore 使用
export PGPASSWORD="${DB_PASSWORD}"

# 备份基础目录 - 从环境变量获取或使用默认值
BACKUP_BASE_DIR=${BACKUP_DIR:-"/mnt/ssd/tenyy/db_backups"}

# 解析可选 CLI 覆盖参数，与备份脚本风格一致
OVR_HOST=""; OVR_PORT=""; OVR_DB=""; OVR_USER=""; OVR_PASS=""
while [[ $# -gt 0 ]]; do
  case "$1" in
    --host=*) OVR_HOST="${1#*=}"; shift ;;
    --port=*) OVR_PORT="${1#*=}"; shift ;;
    --db=*|--database=*) OVR_DB="${1#*=}"; shift ;;
    --user=*) OVR_USER="${1#*=}"; shift ;;
    --password=*) OVR_PASS="${1#*=}"; shift ;;
    -h|--help)
      echo "用法: $0 [--host=HOST --port=PORT --db=NAME --user=USER --password=PASS] <备份文件路径> [clean]"; exit 0 ;;
    *) break ;;
  esac
done

# 应用 CLI 覆盖
[[ -n "${OVR_HOST}" ]] && DB_HOST="${OVR_HOST}"
[[ -n "${OVR_PORT}" ]] && DB_PORT="${OVR_PORT}"
[[ -n "${OVR_DB}" ]] && DB_NAME="${OVR_DB}"
[[ -n "${OVR_USER}" ]] && DB_USER="${OVR_USER}"
[[ -n "${OVR_PASS}" ]] && DB_PASSWORD="${OVR_PASS}" && export PGPASSWORD="${DB_PASSWORD}"

# 恢复模式与并行度
# RESTORE_MODE: recreate(默认，重建数据库) | inplace(不删库就地恢复)
# PARALLEL: pg_restore 并行度，默认 4
RESTORE_MODE=${RESTORE_MODE:-"recreate"}
PARALLEL=${PARALLEL:-"4"}

# 日志函数
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1"
}

# 清理现有数据函数
cleanup_existing_data() {
    log "清理现有数据..."
    
    psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" <<-EOF
        DO \$\$ 
        DECLARE 
            r RECORD; 
        BEGIN 
            -- 禁用所有触发器
            SET session_replication_role = replica;
            
            -- 清空所有表的数据
            FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP 
                EXECUTE 'TRUNCATE TABLE ' || quote_ident(r.tablename) || ' CASCADE'; 
            END LOOP; 
            
            -- 重新启用所有触发器
            SET session_replication_role = DEFAULT;
        END \$\$;
	EOF
	
    if [ $? -eq 0 ]; then
        log "现有数据清理完成"
    else
        log "清理现有数据失败"
        exit 1
    fi
}

# 检查参数
if [ $# -lt 1 ]; then
    echo "用法: $0 [--host=HOST --port=PORT --db=NAME --user=USER --password=PASS] <备份文件路径> [clean]"
    echo "示例: $0 /mnt/ssd/tenyy/db_backups/full/base_20250731_120000.tar.gz"
    echo "示例: $0 /mnt/ssd/tenyy/db_backups/full/base_20250731_120000.tar.gz clean"
    exit 1
fi

BACKUP_FILE="$1"
CLEAN_OPTION="$2"

# 检查备份文件是否存在
if [ ! -f "${BACKUP_FILE}" ]; then
    log "错误: 备份文件不存在: ${BACKUP_FILE}"
    exit 1
fi

log "开始恢复数据库: ${DB_NAME}"
MASKED_PASS=""; [[ -n "${DB_PASSWORD}" ]] && MASKED_PASS="********"
log "连接参数: host=${DB_HOST} port=${DB_PORT} db=${DB_NAME} user=${DB_USER} password=${MASKED_PASS}"
log "备份文件: ${BACKUP_FILE}"

# 如果指定了clean选项，则清理现有数据
if [ "${CLEAN_OPTION}" = "clean" ]; then
    cleanup_existing_data
fi

# 根据文件扩展名判断备份类型并执行相应恢复操作
if [[ "${BACKUP_FILE}" == *.sql.gz ]]; then
    # 处理SQL格式的压缩备份
    log "检测到SQL压缩备份文件"
    
    # 解压缩并恢复，添加详细输出
    if gunzip -c "${BACKUP_FILE}" | psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -v ON_ERROR_STOP=1; then
        log "SQL压缩备份恢复成功"
    else
        log "SQL压缩备份恢复失败"
        exit 1
    fi
    
elif [[ "${BACKUP_FILE}" == *.sql ]]; then
    # 处理SQL格式的未压缩备份
    log "检测到SQL备份文件"
    
    if psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -f "${BACKUP_FILE}" -v ON_ERROR_STOP=1; then
        log "SQL备份恢复成功"
    else
        log "SQL备份恢复失败"
        exit 1
    fi
    
elif [[ "${BACKUP_FILE}" == *.dump ]]; then
    # 处理自定义格式(-Fc)的备份
    log "检测到自定义格式备份文件 (.dump)，恢复模式: ${RESTORE_MODE}，并行度: ${PARALLEL}"

    if [ "${RESTORE_MODE}" = "recreate" ]; then
        # 重建数据库：禁新连 -> 终止连接 -> 删除库 -> pg_restore -C 到 postgres
        terminate_and_drop_db "${DB_NAME}"
        if pg_restore -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d postgres \
                      -C -j "${PARALLEL}" --verbose "${BACKUP_FILE}"; then
            log "自定义格式备份恢复成功（重建数据库）"
        else
            log "自定义格式备份恢复失败（重建数据库）"
            exit 1
        fi
    else
        # 就地恢复：不删库，覆盖对象，并将所有者指向当前用户以避免权限问题
        if pg_restore -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" \
                      -j "${PARALLEL}" --verbose --clean --if-exists --no-owner --role="${DB_USER}" \
                      --no-privileges --no-tablespaces "${BACKUP_FILE}"; then
            log "自定义格式备份恢复成功（就地覆盖）"
        else
            log "自定义格式备份恢复失败（就地覆盖）"
            exit 1
        fi
    fi

elif [[ "${BACKUP_FILE}" == *.tar.gz ]]; then
    # 处理tar.gz格式的基础备份
    log "检测到基础备份压缩文件"
    
    # 创建临时目录
    TEMP_DIR=$(mktemp -d)
    
    # 解压缩到临时目录
    tar -xzf "${BACKUP_FILE}" -C "${TEMP_DIR}"
    
    # 获取解压后的目录名
    BASE_BACKUP_DIR=$(find "${TEMP_DIR}" -maxdepth 1 -type d -name "base_*" | head -n 1)
    
    if [ -z "${BASE_BACKUP_DIR}" ]; then
        log "错误: 无法找到基础备份目录"
        rm -rf "${TEMP_DIR}"
        exit 1
    fi
    
    # 使用pg_restore恢复（对于基础备份，通常需要停止数据库服务进行恢复）
    # 注意：在生产环境中，这可能需要更复杂的操作
    log "注意: 基础备份恢复需要停止数据库服务"
    log "请手动停止数据库服务后执行以下命令:"
    log "pg_ctl stop"
    log "然后将${BASE_BACKUP_DIR}目录内容复制到数据目录"
    log "最后重新启动数据库服务"
    
    # 清理临时目录
    rm -rf "${TEMP_DIR}"
    
else
    log "错误: 不支持的备份文件格式"
    exit 1
fi

if [ $? -eq 0 ]; then
    log "数据库恢复成功完成"
else
    log "数据库恢复失败"
    exit 1
fi

# 取消密码环境变量
unset PGPASSWORD

log "恢复任务完成"