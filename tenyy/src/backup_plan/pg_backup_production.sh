#!/bin/bash

# PostgreSQL 数据库生产环境备份脚本
# 支持全量备份和基于WAL的增量备份
# 全量备份：每天执行
# 增量备份：基于WAL日志

set -e  # 遇到错误时退出

# 备份基础目录 - 从环境变量获取或使用默认值，适配容器环境
BACKUP_BASE_DIR=${BACKUP_DIR:-"/mnt/ssd/tenyy/db_backups"}
FULL_BACKUP_DIR="${BACKUP_BASE_DIR}/full"
INCREMENTAL_BACKUP_DIR="${BACKUP_BASE_DIR}/incremental"
WAL_BACKUP_DIR="${BACKUP_BASE_DIR}/wal"

# 创建备份目录
mkdir -p "${FULL_BACKUP_DIR}"
mkdir -p "${INCREMENTAL_BACKUP_DIR}"
mkdir -p "${WAL_BACKUP_DIR}"

# 获取当前时间
CURRENT_DATE=$(date +"%Y%m%d")
CURRENT_TIME=$(date +"%Y%m%d_%H%M%S")

# 参数解析：支持通过命令行覆盖数据库连接
# 示例： ./pg_backup_production.sh --host=app-db --port=5432 --db=tenyy_app --user=admin --password=xxx base
OVR_HOST=""; OVR_PORT=""; OVR_DB=""; OVR_USER=""; OVR_PASS=""; MODE=""
while [[ $# -gt 0 ]]; do
    case "$1" in
        --host=*) OVR_HOST="${1#*=}"; shift ;;
        --port=*) OVR_PORT="${1#*=}"; shift ;;
        --db=*|--database=*) OVR_DB="${1#*=}"; shift ;;
        --user=*) OVR_USER="${1#*=}"; shift ;;
        --password=*) OVR_PASS="${1#*=}"; shift ;;
        base|incremental|wal) MODE="$1"; shift ;;
        -h|--help) show_usage; exit 0 ;;
        *) shift ;;
    esac
done

# 数据库连接信息（优先使用命令行覆盖，其次环境变量，最后默认值）
DB_HOST=${OVR_HOST:-${POSTGRES_HOST:-${DB_HOST:-"app-db"}}}
DB_PORT=${OVR_PORT:-${POSTGRES_PORT:-${DB_PORT:-"5432"}}}
DB_NAME=${OVR_DB:-${POSTGRES_DB:-${DB_NAME:-"tenyy_app"}}}
DB_USER=${OVR_USER:-${POSTGRES_USER:-${DB_USER:-"admin"}}}
DB_PASSWORD=${OVR_PASS:-${POSTGRES_PASSWORD:-${DB_PASSWORD:-"zhangdi168"}}}

# 导出密码环境变量供 pg_dump 使用
export PGPASSWORD="${DB_PASSWORD}"

# 日志函数
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1"
}

# 打印数据库连接信息（隐藏密码）
log "数据库连接: host=${DB_HOST} port=${DB_PORT} db=${DB_NAME} user=${DB_USER} password=********"

# 规范化主机名/数据库名用于文件名（替换非字母数字为-）
SAFE_HOST=$(echo -n "${DB_HOST}" | sed 's/[^A-Za-z0-9._-]/-/g')
SAFE_DB=$(echo -n "${DB_NAME}" | sed 's/[^A-Za-z0-9._-]/-/g')

# 全量备份函数（使用pg_dump替代pg_basebackup）
base_backup() {
    log "开始执行生产环境全量备份（使用pg_dump）"
    
    local backup_file="${FULL_BACKUP_DIR}/full_${SAFE_HOST}_${SAFE_DB}_${CURRENT_TIME}.dump"
    
    # 使用pg_dump执行全量备份，使用自定义格式(-Fc)
    # -Fc: 自定义格式，二进制格式，避免特殊字符问题
    # --create: 包含创建数据库的命令
    # --clean: 在创建对象前先删除已存在的对象
    # --if-exists: 配合--clean使用，添加IF EXISTS条件
    if pg_dump -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" \
               --create --clean --if-exists \
               --no-password -Fc \
               -f "${backup_file}"; then
        log "生产环境全量备份成功: ${backup_file}"
        
        # 删除7天前的全量备份
        find "${FULL_BACKUP_DIR}" -name "full_*.dump" -mtime +7 -delete
        log "已清理7天前的全量备份文件"
    else
        log "生产环境全量备份失败"
        exit 1
    fi
}

# WAL日志备份函数
wal_backup() {
    log "开始执行WAL日志备份"
    
    # 这里需要数据库启用WAL归档
    # 在实际使用中，需要配置postgresql.conf中的archive_command
    
    # 示例：手动触发WAL切换
    psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -c "SELECT pg_switch_wal();"
    
    log "WAL日志备份完成"
}

# 基于时间点的逻辑增量备份函数
incremental_backup_by_time() {
    log "开始执行基于时间点的增量备份"
    
    local backup_file="${INCREMENTAL_BACKUP_DIR}/incremental_${SAFE_HOST}_${SAFE_DB}_${CURRENT_TIME}.dump"
    
    # 计算一小时前的时间
    ONE_HOUR_AGO=$(date -d '1 hour ago' +"%Y-%m-%d %H:%M:%S")
    
    # 为每个关键表导出最近更新的数据，使用自定义格式
    pg_dump -h "${DB_HOST}" \
           -p "${DB_PORT}" \
           -U "${DB_USER}" \
           -d "${DB_NAME}" \
           --table=app \
           --data-only \
           --column-inserts \
           --where="updated_at >= '${ONE_HOUR_AGO}'" \
           -Fc \
           -f "${backup_file}.app.tmp"
    
    pg_dump -h "${DB_HOST}" \
           -p "${DB_PORT}" \
           -U "${DB_USER}" \
           -d "${DB_NAME}" \
           --table=store_app \
           --data-only \
           --column-inserts \
           --where="updated_at >= '${ONE_HOUR_AGO}'" \
           -Fc \
           -f "${backup_file}.store_app.tmp"
    
    pg_dump -h "${DB_HOST}" \
           -p "${DB_PORT}" \
           -U "${DB_USER}" \
           -d "${DB_NAME}" \
           --table=app_version \
           --data-only \
           --column-inserts \
           --where="last_updated >= '${ONE_HOUR_AGO}' OR analyzed_at >= '${ONE_HOUR_AGO}' OR last_attempt >= '${ONE_HOUR_AGO}'" \
           -Fc \
           -f "${backup_file}.app_version.tmp"
    
    # 合并所有表的备份文件到一个文件中
    cat "${backup_file}.app.tmp" "${backup_file}.store_app.tmp" "${backup_file}.app_version.tmp" > "${backup_file}"
    
    # 清理临时文件
    rm -f "${backup_file}.app.tmp" "${backup_file}.store_app.tmp" "${backup_file}.app_version.tmp"
    
    # 添加标识信息到日志
    log "增量备份时间范围: ${ONE_HOUR_AGO} 到 $(date +"%Y-%m-%d %H:%M:%S")" 
    
    if [ $? -eq 0 ]; then
        log "基于时间点的增量备份成功: ${backup_file}"
        
        # 删除3天前的增量备份
        find "${INCREMENTAL_BACKUP_DIR}" -name "incremental_*.dump" -mtime +3 -delete
        log "已清理3天前的增量备份文件"
    else
        log "基于时间点的增量备份失败"
        exit 1
    fi
}

# 显示用法
show_usage() {
    echo "用法: $0 [--host=HOST] [--port=PORT] [--db=DB|--database=DB] [--user=USER] [--password=PASS] <base|incremental|wal>"
    echo "示例:"
    echo "  $0 --host=app-db --port=5432 --db=tenyy_app --user=admin --password=*** base"
    echo "说明:"
    echo "  可通过命令行覆盖环境变量（POSTGRES_HOST/PORT/DB/USER/PASSWORD）"
}

# 根据参数决定执行哪种备份
case "${MODE:-${1:-base}}" in
    "base")
        base_backup
        ;;
    "incremental")
        incremental_backup_by_time
        ;;
    "wal")
        wal_backup
        ;;
    *)
        show_usage
        exit 1
        ;;
esac

# 取消密码环境变量
unset PGPASSWORD

log "备份任务完成"