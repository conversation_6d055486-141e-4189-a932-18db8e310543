#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库备份 Prefect Flow
用于在容器环境中执行数据库备份任务
"""

import subprocess
import os
from typing import Optional, Annotated
from pydantic import Field
from prefect import flow, task, get_run_logger


@task
def run_backup_script(
    backup_type: str,
    backup_dir: Optional[str] = None,
    host: Optional[str] = None,
    port: Optional[str] = None,
    db: Optional[str] = None,
    user: Optional[str] = None,
    password: Optional[str] = None,
) -> str:
    """
    在容器中执行备份脚本
    
    Args:
        backup_type: 备份类型 ("base", "incremental", "wal")
        backup_dir: 备份目录路径（可选）
        
    Returns:
        脚本执行输出
    """
    logger = get_run_logger()
    
    # 构建命令
    cmd = ["/app/tenyy/src/backup_plan/pg_backup_production.sh"]
    # 连接参数通过 CLI 传递，脚本会优先使用 CLI 覆盖环境变量
    if host:
        cmd.append(f"--host={host}")
    if port:
        cmd.append(f"--port={port}")
    if db:
        cmd.append(f"--db={db}")
    if user:
        cmd.append(f"--user={user}")
    if password:
        cmd.append(f"--password={password}")
    cmd.append(backup_type)
    
    # 设置环境变量
    env = dict()
    if backup_dir:
        env["BACKUP_DIR"] = backup_dir
    
    logger.info(f"执行备份命令: {' '.join(cmd[:-1])} {backup_type}")
    if any([host, port, db, user, password]):
        masked = '********' if password else ''
        logger.info(
            f"备份连接参数: host={host or ''} port={port or ''} db={db or ''} user={user or ''} password={masked}"
        )
    if env:
        logger.info(f"环境变量: {env}")
    
    # 执行备份脚本
    result = subprocess.run(
        cmd, 
        capture_output=True, 
        text=True,
        env=env if env else None
    )
    
    if result.returncode != 0:
        logger.error(f"备份失败: {result.stderr}")
        raise Exception(f"备份失败: {result.stderr}")
    
    logger.info("备份成功完成")
    return result.stdout


@task
def run_restore_script(
    backup_file: str,
    restore_mode: str = "recreate",
    parallel: int = 4,
    clean: bool = False,
    host: Optional[str] = None,
    port: Optional[str] = None,
    db: Optional[str] = None,
    user: Optional[str] = None,
    password: Optional[str] = None,
) -> str:
    """
    在容器中执行恢复脚本
    
    Args:
        backup_file: 备份文件路径
        
    Returns:
        脚本执行输出
    """
    logger = get_run_logger()
    
    # 构建命令
    cmd = ["/app/tenyy/src/backup_plan/restore_db.sh", backup_file]

    # 环境变量：传递恢复模式、并行度与可选的数据库连接参数
    env = {**os.environ, "RESTORE_MODE": restore_mode, "PARALLEL": str(parallel)}
    if host:
        env["POSTGRES_HOST"] = host
    if port:
        env["POSTGRES_PORT"] = port
    if db:
        env["POSTGRES_DB"] = db
    if user:
        env["POSTGRES_USER"] = user
    if password:
        env["POSTGRES_PASSWORD"] = password
    if clean:
        # 兼容原脚本的第二参数 clean（如果需要清表的 SQL .sql/.sql.gz 情况）
        cmd.append("clean")

    logger.info(f"执行恢复命令: {' '.join(cmd)}")
    masked = '********' if password else ''
    logger.info(
        f"恢复环境变量: RESTORE_MODE={restore_mode}, PARALLEL={parallel}, CLEAN={clean}, "
        f"host={host or ''} port={port or ''} db={db or ''} user={user or ''} password={masked}"
    )

    # 执行恢复脚本
    result = subprocess.run(cmd, capture_output=True, text=True, env=env)
    
    if result.returncode != 0:
        logger.error(
            "恢复失败 (returncode=%s)\nSTDOUT:\n%s\nSTDERR:\n%s",
            result.returncode,
            result.stdout,
            result.stderr,
        )
        # 同时包含 stdout/stderr，避免仅 stderr 为空导致信息缺失
        raise Exception(
            f"恢复失败 (code={result.returncode})\nSTDOUT:\n{result.stdout}\nSTDERR:\n{result.stderr}"
        )
    
    logger.info("恢复成功完成")
    return result.stdout


@flow(name="Database Backup")
def database_backup(
    backup_type: Annotated[str, Field(description="备份类型：base(全量)/incremental(增量)/wal(WAL 归档)")] = "base",
    backup_dir: Annotated[Optional[str], Field(description="备份目录（容器内路径）")] = "/mnt/ssd/tenyy/db_backups",
    host: Annotated[Optional[str], Field(description="数据库主机名或 Service 名称")] = "app-db",
    port: Annotated[Optional[str], Field(description="数据库端口")] = "5432",
    db: Annotated[Optional[str], Field(description="数据库名")] = "tenyy_app",
    user: Annotated[Optional[str], Field(description="数据库用户")] = "admin",
    password: Annotated[Optional[str], Field(description="数据库密码（建议用 Prefect Secret/Variables 配置）")] = None,
) -> str:
    """
    数据库备份任务
    
    Args:
        backup_type: 备份类型 ("base", "incremental", "wal")
        backup_dir: 备份目录路径（可选）
        
    Returns:
        备份执行结果
    """
    logger = get_run_logger()
    logger.info(f"开始执行数据库备份任务: {backup_type}")
    
    if backup_dir:
        logger.info(f"备份目录: {backup_dir}")
    
    result = run_backup_script(
        backup_type,
        backup_dir,
        host=host,
        port=port,
        db=db,
        user=user,
        password=password,
    )
    logger.info("数据库备份任务完成")
    return result


@flow(name="Database Restore")
def database_restore(
    backup_file: Annotated[str, Field(description="备份文件路径（容器内可读）")],
    restore_mode: Annotated[str, Field(description="恢复模式：recreate(重建库)/inplace(就地覆盖)")] = "recreate",
    parallel: Annotated[int, Field(description="pg_restore 并行度")]= 4,
    clean: Annotated[bool, Field(description="在 SQL 恢复前清空现有数据（仅 .sql/.sql.gz 场景）")] = False,
    host: Annotated[Optional[str], Field(description="数据库主机名或 Service 名称")] = "app-db",
    port: Annotated[Optional[str], Field(description="数据库端口")] = "5432",
    db: Annotated[Optional[str], Field(description="数据库名")] = "tenyy_app",
    user: Annotated[Optional[str], Field(description="数据库用户")] = "admin",
    password: Annotated[Optional[str], Field(description="数据库密码（建议用 Prefect Secret/Variables 配置）")] = None,
) -> str:
    """
    数据库恢复任务
    
    Args:
        backup_file: 备份文件路径
        
    Returns:
        恢复执行结果
    """
    logger = get_run_logger()
    logger.info(f"开始执行数据库恢复任务: {backup_file}")
    
    result = run_restore_script(
        backup_file,
        restore_mode=restore_mode,
        parallel=parallel,
        clean=clean,
        host=host,
        port=port,
        db=db,
        user=user,
        password=password,
    )
    logger.info("数据库恢复任务完成")
    return result