build: null
deployments:
- description: 腾讯应用宝应用爬虫 - 爬取应用信息
  entrypoint: tenyy/src/crawler/prefect_flow/prefect_flow.py:universal_crawler
  name: yinyongbao-app-crawler
  parameters:
    app_type: app
    max_categories: -1
    max_pages: -1
    store_type: yinyongbao
  schedule: null
  tags:
  - crawler
  - yinyongbao
  - app
  - k8s
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '1.0'
      env:
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        POSTGRES_PASSWORD: zhangdi168
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: 127.0.0.1:5000/tenyy-unified:latest
      image_pull_policy: IfNotPresent
      memory_limit: 2Gi
      namespace: tenyy
    name: tenyy-unified-pool
    work_queue_name: crawler-queue
- description: 腾讯应用宝游戏爬虫 - 爬取游戏信息
  entrypoint: tenyy/src/crawler/prefect_flow/prefect_flow.py:universal_crawler
  name: yinyongbao-game-crawler
  parameters:
    app_type: game
    max_categories: -1
    max_pages: -1
    store_type: yinyongbao
  schedule: null
  tags:
  - crawler
  - yinyongbao
  - game
  - k8s
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '1.0'
      env:
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        POSTGRES_PASSWORD: zhangdi168
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: 127.0.0.1:5000/tenyy-unified:latest
      image_pull_policy: IfNotPresent
      memory_limit: 2Gi
      namespace: tenyy
    name: tenyy-unified-pool
    work_queue_name: crawler-queue
- description: 华为应用市场爬虫 - 爬取应用信息
  entrypoint: tenyy/src/crawler/prefect_flow/prefect_flow.py:universal_crawler
  name: huawei-app-crawler
  parameters:
    max_categories: -1
    max_pages: -1
    store_type: huawei
  schedule: null
  tags:
  - crawler
  - huawei
  - app
  - k8s
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '1.0'
      env:
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        POSTGRES_PASSWORD: zhangdi168
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: 127.0.0.1:5000/tenyy-unified:latest
      image_pull_policy: IfNotPresent
      memory_limit: 2Gi
      namespace: tenyy
    name: tenyy-unified-pool
    work_queue_name: crawler-queue
- description: 腾讯应用宝推荐爬虫 - 爬取推荐应用信息
  entrypoint: tenyy/src/crawler/prefect_flow/prefect_flow.py:universal_crawler
  name: yinyongbao-similar-crawler
  parameters:
    max_categories: 0
    max_pages: -1
    store_type: yinyongbao_similar
  schedule: null
  tags:
  - crawler
  - yinyongbao
  - similar
  - k8s
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '1.0'
      env:
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        POSTGRES_PASSWORD: zhangdi168
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: 127.0.0.1:5000/tenyy-unified:latest
      image_pull_policy: IfNotPresent
      memory_limit: 2Gi
      namespace: tenyy
    name: tenyy-unified-pool
    work_queue_name: crawler-queue
- description: 批量APK下载和分析处理器 - 处理待下载的APK文件
  entrypoint: tenyy/src/download_extract/flows/batch_processor.py:batch_apk_processing_flow
  name: batch-apk-processing
  parameters:
    batch_size: 10
    max_concurrent: 3
  schedule: null
  tags:
  - download
  - extract
  - apk
  - k8s
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '0'
      cpu_request: '1.0'
      env:
        ARIA2_RPC_TOKEN: zhangdi168
        ARIA2_RPC_URL: http://aria2-service:6800/jsonrpc
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        DOCKER_ENV: 'true'
        DOWNLOAD_DIR: /downloads
        POSTGRES_PASSWORD: zhangdi168
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: 127.0.0.1:5000/tenyy-unified:latest
      image_pull_policy: IfNotPresent
      memory_limit: '0'
      memory_request: 2Gi
      namespace: tenyy
    name: tenyy-unified-pool
    work_queue_name: download-queue
- description: 循环批量APK下载和分析处理器 - 持续处理直到没有待处理任务
  entrypoint: tenyy/src/download_extract/flows/batch_processor.py:continuous_batch_apk_processing_flow
  name: continuous-batch-apk-processing
  parameters:
    batch_size: 50
    cycle_delay: 30
    max_concurrent: 10
    max_cycles: 100
  schedule: null
  tags:
  - download
  - extract
  - apk
  - continuous
  - k8s
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '0'
      cpu_request: '2.0'
      env:
        ARIA2_RPC_TOKEN: zhangdi168
        ARIA2_RPC_URL: http://host.docker.internal:6800/jsonrpc
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        DOCKER_ENV: 'true'
        DOWNLOAD_DIR: /downloads
        POSTGRES_PASSWORD: zhangdi168
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: 127.0.0.1:5000/tenyy-unified:latest
      image_pull_policy: IfNotPresent
      memory_limit: '0'
      memory_request: 4Gi
      namespace: tenyy
    name: tenyy-unified-pool
    work_queue_name: download-queue
- description: 🚀 真正的流水线APK处理器 - 任务队列+工作池，无批次等待
  entrypoint: tenyy/src/download_extract/flows/batch_processor.py:continuous_pipeline_apk_processing_flow
  name: pipeline-apk-processing
  parameters:
    check_interval: 5
    max_concurrent: 10
    max_idle_time: 60
    task_fetch_size: 20
  schedule: null
  tags:
  - download
  - extract
  - apk
  - pipeline
  - continuous
  - k8s
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '0'
      cpu_request: '2.0'
      env:
        ARIA2_RPC_TOKEN: zhangdi168
        ARIA2_RPC_URL: http://aria2-service:6800/jsonrpc
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        DOCKER_ENV: 'true'
        DOWNLOAD_DIR: /downloads
        POSTGRES_PASSWORD: zhangdi168
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: 127.0.0.1:5000/tenyy-unified:latest
      image_pull_policy: IfNotPresent
      memory_limit: '0'
      memory_request: 4Gi
      namespace: tenyy
    name: tenyy-unified-pool
    work_queue_name: pipeline-queue
- description: 合并 APK 提取与 SDK 匹配的本地Flow，按批循环处理直至无待处理任务，支持Artifacts输出
  entrypoint: tenyy/src/apkinfo_analysis/apk_analysis_flow/combine_analysis_flow.py:combine_analysis_flow
  name: combine-apk-analysis-and-sdk-match
  parameters:
    limit: 1000
    max_workers: 4
  schedule: null
  tags:
  - apk
  - sdk_match
  - combine
  - k8s
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '0'
      cpu_request: '1.0'
      env:
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        POSTGRES_PASSWORD: zhangdi168
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: 127.0.0.1:5000/tenyy-unified:latest
      image_pull_policy: IfNotPresent
      memory_limit: '0'
      memory_request: 2Gi
      namespace: tenyy
    name: tenyy-unified-pool
    work_queue_name: analysis-queue
- description: 按 App 分批询问潜在 SDK 并回填知识库链接（batch_size=App 数量；每个 App 内处理其所有包前缀），输出 Artifacts
  entrypoint: tenyy/src/apkinfo_analysis/ask_gemini/ask_gemini_potential_flow.py:ask_gemini_potential_flow
  name: ask-gemini-potential
  parameters:
    batch_size: 5
    cooldown_days: 7
    dry_run: true
    max_batches: 2
    model_name: gemini-2.5-flash
    print_prompt_only: false
    provider: gemini
    rpm: 15
  schedule: null
  tags:
  - apk
  - sdk_kb
  - llm
  - k8s
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '0'
      cpu_request: '1.0'
      env:
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        POSTGRES_PASSWORD: zhangdi168
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: 127.0.0.1:5000/tenyy-unified:latest
      image_pull_policy: IfNotPresent
      memory_limit: '0'
      memory_request: 2Gi
      namespace: tenyy
    name: tenyy-unified-pool
    work_queue_name: analysis-queue
- description: 数据库备份任务 - 执行数据库备份操作
  entrypoint: tenyy/src/backup_plan/database_backup_flow.py:database_backup
  name: database-backup
  parameters:
    backup_dir: /mnt/ssd/tenyy/db_backups
    backup_type: base
  schedule: null
  tags:
  - database
  - backup
  - k8s
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '1.0'
      cpu_request: '0.5'
      env:
        BACKUP_DIR: /mnt/ssd/tenyy/db_backups
        DOCKER_ENV: 'true'
        POSTGRES_DB: tenyy_app
        POSTGRES_HOST: app-db
        POSTGRES_PASSWORD: zhangdi168
        POSTGRES_PORT: '5432'
        POSTGRES_USER: admin
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: 127.0.0.1:5000/tenyy-unified:latest
      image_pull_policy: IfNotPresent
      memory_limit: 2Gi
      memory_request: 1Gi
      namespace: tenyy
    name: tenyy-unified-pool
    work_queue_name: backup-queue
- description: 数据库恢复任务 - 从备份文件恢复数据库
  entrypoint: tenyy/src/backup_plan/database_backup_flow.py:database_restore
  name: database-restore
  parameters:
    backup_file: /mnt/ssd/tenyy/db_backups/full_backup_20250829_071745.dump
  schedule: null
  tags:
  - database
  - restore
  - k8s
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '1.0'
      cpu_request: '0.5'
      env:
        DOCKER_ENV: 'true'
        POSTGRES_DB: tenyy_app
        POSTGRES_HOST: app-db
        POSTGRES_PASSWORD: zhangdi168
        POSTGRES_PORT: '5432'
        POSTGRES_USER: admin
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: 127.0.0.1:5000/tenyy-unified:latest
      image_pull_policy: IfNotPresent
      memory_limit: 2Gi
      memory_request: 1Gi
      namespace: tenyy
    name: tenyy-unified-pool
    work_queue_name: backup-queue
name: tenyy-unified
prefect-version: 3.0.0
pull:
- prefect.deployments.steps.set_working_directory:
    directory: /app
push: null
