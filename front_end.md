###Tailwind CSS

--color-satin-linen-50: #f7f6f3;
--color-satin-linen-100: #e2ded0;
--color-satin-linen-200: #d2cbb5;
--color-satin-linen-300: #baaf8f;
--color-satin-linen-400: #aa9a75;
--color-satin-linen-500: #9d8663;
--color-satin-linen-600: #897056;
--color-satin-linen-700: #735b4a;
--color-satin-linen-800: #604b40;
--color-satin-linen-900: #503f37;
--color-satin-linen-950: #2c221c;


# 基于 Next.js + Supabase 的 SaaS 前端设计

本文档为本项目分析结果的前端（SaaS）方案。技术栈：Next.js(App Router) + TypeScript + Tailwind CSS + shadcn/ui + Supabase(Auth/DB/Storage)。功能范围覆盖：注册登录、SDK 分类展示(参考 Wappalyzer 交互范式)、App 搜索、按分类展示 App。

## 1. 页面信息架构与路由

- __首页__ `/`
  - 英雄区：项目简介、核心指标摘要（总 App 数、SDK 数、最近分析时间）。
  - 快捷入口：SDK、App 搜索、分类。
- __认证__ `/auth`
  - `/auth/sign-in` 邮箱/密码、魔法链接；可选 OAuth。
  - `/auth/sign-up` 邮箱/密码注册，验证邮件。
- __控制台__ `/dashboard`
  - 最近分析、个人偏好（默认排序、每页条数）。
- __SDK 列表__ `/sdks`
  - 类似 Wappalyzer：按类别分组、图标、计数、流行度、趋势。
- __SDK 详情__ `/sdks/[slug]`
  - SDK 基本信息、关联 App 列表、使用趋势、相关 SDK。
- __App 搜索__ `/apps`
  - 搜索框 + 高级筛选：平台、分类、SDK、更新时间、国家/市场（若有）。
  - 结果卡片：图标、名称、包名、匹配 SDK 徽章、评分（若有）。
- __分类列表__ `/categories`
  - 展示所有分类及每类 App/SDK 计数。
- __分类详情__ `/categories/[slug]`
  - 分类描述、该分类下的 App 列表、Top SDK。
- __App 详情__ `/app/[packageName]`
  - 基本信息、检测到的 SDK 列表、变更历史、分析时间线。

备注：全部使用 App Router + RSC；数据获取优先服务端组件拉取，结合 `next/headers` 携带 Supabase 会话。

## 2. 数据模型（前端所需视图）

为避免与后端现有表结构强耦合，前端以“查询视图字段需求”描述，后端可通过视图/Materialized View 或 API 适配：

- __SDK__：`id, name, slug, category, icon_url, description, app_count, trend_score`
- __App__：`id, name, package_name, icon_url, categories[], last_analyzed_at, sdk_badges[]`
- __Category__：`id, name, slug, description, app_count, sdk_count`
- __App-SDK 关联__：`app_id, sdk_id, detected_version, first_seen_at, last_seen_at`
- __分析事件（可选）__：`app_id, event_type, at, diff`

若 Supabase 直连：建议创建以下只读视图：
- `v_sdks`、`v_apps`、`v_categories`、`v_app_sdks`、`v_app_events`

## 3. 认证与权限（Supabase）

- __Auth__：Email/Password + Magic Link；可选 GitHub/Google OAuth。
- __RLS 策略__：
  - 公共只读：`v_sdks`、`v_categories`、公开字段的 `v_apps` 支持匿名/已登录读取。
  - 用户数据（如收藏、偏好）启用基于 `auth.uid()` 的行级策略。
- __会话传递__：在 Server Components 中通过 `createServerClient()` 读取，会话保存在 `cookies()`。

## 4. 关键页面与交互细节

- __SDK 列表 `/sdks`__
  - 分组折叠：按 `category` 折叠/展开；分组内支持排序（App 数、趋势、名称）。
  - 快速筛选：搜索框（按 name/slug），多选分类 Tag。
  - 交互参考 Wappalyzer：卡片展示图标、名称、计数、趋势箭头。
- __SDK 详情 `/sdks/[slug]`__
  - 头部：名称、描述、图标、总使用 App 数、增长率。
  - App 列：分页、排序、导出 CSV（客户端生成）。
- __App 搜索 `/apps`__
  - 搜索：前缀匹配 + 全文检索；键入即显示建议。
  - 筛选：`categories[]`、`sdks[]`、时间范围、排序（最新分析、最常见 SDK 数、名称）。
  - UX：保留查询参数到 URL；支持分享链接。
- __分类页 `/categories` & `/categories/[slug]`__
  - 列表：带计数与描述；详情：Top App、Top SDK、最近活跃。
- __App 详情 `/app/[packageName]`__
  - 基本信息 + SDK 列表（版本/时间）；变更时间线（首次见到/最后更新）。

## 5. 搜索与性能

- __搜索实现__：
  - 优先 Postgres：`pg_trgm` 或 `tsvector`；若暂不可用，回退 ILIKE + 前端高亮。
  - 重要字段：`name`, `package_name`, `sdk_names`, `categories`。
- __分页__：基于游标（`id`/`created_at`）优先；降级 offset/limit。
- __缓存__：Next.js RSC `revalidate`/`cache: force-cache` 用于公共数据（SDK、分类热门榜）。

## 6. 组件与样式

- __UI 组件__：SearchBar、SdkCard、AppCard、CategoryPill、FilterDrawer、TrendBadge、Pagination。
- __色板__：沿用文首 Tailwind 定制色；明暗主题。
- __图标__：iconify/heroicons；SDK 图标存放于 Supabase Storage（公开只读）。

## 7. 跟踪与可观察性

- __埋点__：页面浏览、搜索查询、筛选器使用、详情页点击。
- __错误__：前端 Sentry（可选），服务端 Route Handler 打点日志（结构化）。

## 8. 最小可行实现（MVP）拆解

1) 认证：邮箱注册/登录；仪表盘基础页。
2) SDK 列表/详情：列表分组与排序；详情页带分页。
3) App 搜索：关键词 + 分类/SDK 筛选；结果列表。
4) 分类列表/详情：分类聚合与详情页。
5) App 详情页：基础信息 + SDK 列表。

## 9. 下一步与依赖

- 与后端确认/提供视图：`v_sdks`, `v_apps`, `v_categories`, `v_app_sdks` 字段定义与索引。
- 索引：为 `LOWER(name)`, `package_name`, 连接列与时间列建立索引；若做全文检索，建立 `GIN` 索引。
- RLS：公共视图只读策略；用户偏好/收藏表的基于 `auth.uid()` 策略。

---

小结：以上结构可直接落地为 Next.js App Router 项目，先行完成公开只读浏览与基本检索，随后增强趋势、图表与更高级的分析维度。

## 10. 前端数据模型与 Supabase 结合

基于现有后端模型（见 `tenyy/src/models/`）：

- __`app`__（`App`）：应用主表，主键 `id`=包名/Bundle ID，含 `name`, `icon`, `category`, `is_game`, `developer`, `created_at`, `updated_at`, `metadata`。
- __`store_app`__（`StoreApp`）：商店侧 App 映射，`app_id -> app.id`，`store_type`（huawei/yyb 等），一对多。
- __`app_version`__（`AppVersion`）：版本信息，外键 `store_app_id -> store_app.id`，含商店字段（评分、下载量等）与 APK 分析结果（`packages_class`, `lib_files` 等）。
- __`class_sdk_knowledge_base`__（`class_SDKKnowledgeBase`）：SDK 知识库，关键字段 `package_prefix`, `sdk_name`, `subcategory_id -> class_subcategory.id`。
- __`class_app_version_sdks`__（`class_AppVersionSDK`）：版本与 SDK 的识别关系（组合主键 `app_version_id + sdk_package_prefix`），含 `sdk_knowledge_base_id`（可空）、`match_type`, `is_potential` 等。
- __`class_category`__ / __`class_subcategory`__：分类与子分类（`subcategory.category_id -> category.id`）。

为前端查询友好，建议建立只读视图（或 Supabase Edge Functions/RouteHandlers 包装 API）。以下为基于 Postgres 视图的推荐方案：

### 10.1 v_apps（App 列表/搜索）

用途：App 列表、搜索、详情页头部。

核心字段：
- `id`, `name`, `icon_url`, `category`, `is_game`, `developer`
- `latest_analyzed_at`（来自 `app_version.analyzed_at` 最大值）
- `store_types`（array，来自 `store_app.store_type` 去重）
- `sdk_count`（该 App 近一次版本识别到的 SDK 数；或全部版本累积数，按需选择）

示例 SQL（按“最新版本”聚合）：

```sql
create or replace view v_apps as
with latest_version as (
  select av.*
  from app_version av
  join (
    select store_app_id, max(analyzed_at) as latest
    from app_version
    group by store_app_id
  ) t on t.store_app_id = av.store_app_id and t.latest = av.analyzed_at
), app_store as (
  select a.id as app_id, array_agg(distinct sa.store_type) as store_types
  from app a
  join store_app sa on sa.app_id = a.id
  group by a.id
), app_sdk as (
  select lv.store_app_id,
         count(distinct case when cas.is_potential is false then coalesce(cas.sdk_knowledge_base_id::text, cas.sdk_package_prefix) end) as sdk_count
  from latest_version lv
  left join class_app_version_sdks cas on cas.app_version_id = lv.id
  group by lv.store_app_id
)
select a.id,
       a.name,
       a.icon as icon_url,
       a.category,
       a.is_game,
       a.developer,
       max(lv.analyzed_at) as latest_analyzed_at,
       coalesce(asg.store_types, '{}') as store_types,
       coalesce(max(ask.sdk_count), 0) as sdk_count
from app a
left join store_app sa on sa.app_id = a.id
left join latest_version lv on lv.store_app_id = sa.id
left join app_store asg on asg.app_id = a.id
left join app_sdk ask on ask.store_app_id = sa.id
group by a.id, a.name, a.icon, a.category, a.is_game, a.developer, asg.store_types;
```

索引建议：`app(id)`, `store_app(app_id)`, `app_version(store_app_id, analyzed_at desc)`；可加 `gin_trgm_ops` 于 `app.name` 与 `app.id` 以支持搜索。

### 10.2 v_sdks（SDK 列表/详情）

用途：SDK 列表、详情页、关联 App 计数。

核心字段：
- `id`（= `class_sdk_knowledge_base.id`）、`sdk_name`, `package_prefix`, `subcategory_id`, `description`, `official_web`
- `category_id`, `category_name`, `subcategory_name`
- `app_count`（使用该 SDK 的 App 数，按最新版本或全量版本去重）

示例 SQL（按“App 维度去重”）：

```sql
create or replace view v_sdks as
with sdks as (
  select kb.id, kb.sdk_name, kb.package_prefix, kb.description, kb.official_web, kb.subcategory_id
  from class_sdk_knowledge_base kb
), sc as (
  select s.id as subcategory_id, s.name as subcategory_name, c.id as category_id, c.name as category_name
  from class_subcategory s
  join class_category c on c.id = s.category_id
), usage_app as (
  select distinct kb.id as sdk_id, a.id as app_id
  from class_app_version_sdks cas
  join class_sdk_knowledge_base kb on kb.id = cas.sdk_knowledge_base_id
  join app_version av on av.id = cas.app_version_id
  join store_app sa on sa.id = av.store_app_id
  join app a on a.id = sa.app_id
  where cas.is_potential is false
)
select sd.id,
       sd.sdk_name,
       sd.package_prefix,
       sd.description,
       sd.official_web,
       sc.category_id,
       sc.category_name,
       sd.subcategory_id,
       sc.subcategory_name,
       count(distinct ua.app_id) as app_count
from sdks sd
left join sc on sc.subcategory_id = sd.subcategory_id
left join usage_app ua on ua.sdk_id = sd.id
group by sd.id, sd.sdk_name, sd.package_prefix, sd.description, sd.official_web,
         sc.category_id, sc.category_name, sd.subcategory_id, sc.subcategory_name;
```

索引建议：`class_app_version_sdks(app_version_id, sdk_knowledge_base_id, is_potential)`；`class_sdk_knowledge_base(package_prefix, sdk_name)`。

### 10.3 v_categories（分类/子分类聚合）

```sql
create or replace view v_categories as
select c.id as category_id,
       c.name as category_name,
       count(distinct s.id) as subcategory_count,
       count(distinct kb.id) as sdk_count
from class_category c
left join class_subcategory s on s.category_id = c.id
left join class_sdk_knowledge_base kb on kb.subcategory_id = s.id
group by c.id, c.name;
```

子分类详情视图（可选）：

```sql
create or replace view v_subcategories as
select s.id as subcategory_id, s.name as subcategory_name, c.id as category_id, c.name as category_name,
       count(distinct kb.id) as sdk_count
from class_subcategory s
join class_category c on c.id = s.category_id
left join class_sdk_knowledge_base kb on kb.subcategory_id = s.id
group by s.id, s.name, c.id, c.name;
```

### 10.4 v_app_sdks（App 详情页的 SDK 清单）

```sql
create or replace view v_app_sdks as
select a.id as app_id,
       av.id as app_version_id,
       kb.id as sdk_id,
       kb.sdk_name,
       cas.sdk_package_prefix,
       cas.match_type,
       cas.is_potential,
       av.analyzed_at
from class_app_version_sdks cas
join app_version av on av.id = cas.app_version_id
join store_app sa on sa.id = av.store_app_id
join app a on a.id = sa.app_id
left join class_sdk_knowledge_base kb on kb.id = cas.sdk_knowledge_base_id;
```

前端在 App 详情页可按 `app_id` 过滤，并按 `analyzed_at desc` 排序，或仅取最新版本数据。

### 10.5 Supabase 访问与 RLS 策略

- __公开只读__：`v_apps`、`v_sdks`、`v_categories`、`v_subcategories`、`v_app_sdks` 均设置 `security_invoker` 视图或在 Supabase 中配置公开只读策略（`select using (true)`）。
- __用户数据表（可选）__：如 `user_preferences`, `favorites` 等，基于 `auth.uid()` 的 RLS：

```sql
alter table favorites enable row level security;
create policy "owner can read/write" on favorites
  for all using (user_id = auth.uid()) with check (user_id = auth.uid());
```

### 10.6 前端 TypeScript 类型（与视图绑定）

```ts
export type VApp = {
  id: string;
  name: string | null;
  icon_url: string | null;
  category: string | null;
  is_game: boolean | null;
  developer: string | null;
  latest_analyzed_at: string | null;
  store_types: string[];
  sdk_count: number;
}

export type VSdk = {
  id: number;
  sdk_name: string | null;
  package_prefix: string;
  description: string | null;
  official_web: string | null;
  category_id: number | null;
  category_name: string | null;
  subcategory_id: number | null;
  subcategory_name: string | null;
  app_count: number;
}

export type VCategory = {
  category_id: number;
  category_name: string;
  subcategory_count: number;
  sdk_count: number;
}

export type VAppSdk = {
  app_id: string;
  app_version_id: number;
  sdk_id: number | null;
  sdk_name: string | null;
  sdk_package_prefix: string;
  match_type: string | null;
  is_potential: boolean;
  analyzed_at: string | null;
}
```

### 10.7 与现有 Next.js 查询的契合

- `/apps`：`from('v_apps').select('*').ilike('name', '%kw%')` 或使用 Postgres 全文检索；分页用游标或 `range()`。
- `/sdks`：`from('v_sdks').select('*').order('app_count', { descending: true })`；按分类/子分类过滤。
- `/categories`：`from('v_categories').select('*')`；详情页联查 `v_subcategories` 与 `v_sdks`。
- `/app/[id]`：`from('v_app_sdks').select('*').eq('app_id', id).order('analyzed_at', { descending: true })`。

备注：若生产库非 Supabase 托管，可在 Supabase 创建外部数据源或通过 ETL 周期性同步必要视图到 Supabase 实例。

## 11. 部署与运维：混合读方案（推荐过渡）

目标：保持现有数据库继续“写入/分析”，在 Supabase 搭建“只读查询 + Auth”。前端全部连 Supabase，降低风险、快速上线。

### 11.1 步骤总览

- __创建 Supabase 项目__：拿到 `SUPABASE_URL`、`SUPABASE_ANON_KEY`、`SUPABASE_SERVICE_ROLE_KEY`。
- __在 Supabase 执行视图/索引/策略__：参考第 10 节的 `v_apps`、`v_sdks`、`v_categories`、`v_app_sdks` 与索引、RLS（视图匿名只读或登录只读）。
- __建立同步（ETL）__：将现库的变化按计划同步到 Supabase（可选表→目标表/物化视图；或直接在 Supabase 建视图并复制源表数据）。
- __前端接入__：Next.js 使用 Supabase Auth + 视图查询，所有读走 Supabase。

### 11.2 同步策略（ETL）

- __粒度__：仅同步前端所需表/字段（`app`, `store_app`, `app_version`, `class_app_version_sdks`, `class_sdk_knowledge_base`, `class_category`, `class_subcategory`）。
- __模式__：
  - 定时批同步：每 N 分钟/小时全量或按时间戳增量（`updated_at`、`analyzed_at`）。
  - 逻辑增量：基于主键/更新时间游标，插入/更新/删除三类变更。
- __校验__：每次同步后做计数校验（表行数、聚合校验），异常报警。

示例增量思路（伪代码）：

```python
# 从源库拉取 updated_at > last_checkpoint 的记录，upsert 到 Supabase
# 表：app、store_app、app_version、class_app_version_sdks、class_sdk_knowledge_base、class_category、class_subcategory
```

### 11.3 环境变量与前端接入

- Next.js `.env.local`：
```
NEXT_PUBLIC_SUPABASE_URL=...
NEXT_PUBLIC_SUPABASE_ANON_KEY=...
SUPABASE_SERVICE_ROLE_KEY=...  # 仅在服务端任务/ETL 使用，不要暴露到客户端
```
- Server Components 使用 `createServerClient()`；客户端组件使用 `createBrowserClient()` 读取 `v_*` 视图。

### 11.4 权限与安全

- 视图只读策略：对 `anon`、`authenticated` 授权 `select`。
- 私有表（收藏、偏好）启用 RLS：`user_id = auth.uid()`。
- ETL 使用 `service_role` 进行 upsert；限制来源 IP 与最小权限。

### 11.5 监控与回滚

- __监控__：
  - 同步时长、行数、错误率、校验差异；Supabase 查询错误日志。
  - 前端探针：关键视图 `count(*)` 健康检查。
- __回滚__：
  - ETL 保留上一个检查点与导出快照（行数、哈希摘要）。
  - 出错时切换前端到“只读降级页”或拉取上次成功快照。

小结：混合读能在不动生产写入的前提下快速上线 SaaS 前端，并逐步完善同步与权限；后续视业务再评估是否完全迁移到 Supabase。
