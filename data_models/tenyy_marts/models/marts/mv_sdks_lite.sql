{{ config(
  alias='mv_sdks_lite',
  materialized='materialized_view',
  post_hook=[
    "{{ create_mv_unique_index(this, ['id']) }}"
  ]
) }}

SELECT
  kb.id::bigint                           AS id,
  kb.sdk_name,
  kb.package_prefix::varchar(255)        AS package_prefix,
  kb.description,
  kb.official_web::varchar(1024)         AS official_web,
  kb.company_name::varchar(255)          AS company_name,
  kb.subcategory_id
FROM public.class_sdk_knowledge_base kb;
