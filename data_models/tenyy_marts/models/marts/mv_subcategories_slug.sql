{{ config(
  alias='mv_subcategories_slug',
  materialized='materialized_view',
  post_hook=[
    "{{ create_mv_unique_index(this, ['subcategory_id']) }}"
  ]
) }}

SELECT
  subcategory_id,
  subcategory_name::character varying(255) AS subcategory_name,
  category_id,
  category_name::character varying(255) AS category_name,
  category_slug::character varying(255) AS category_slug,
  subcategory_slug::character varying(255) AS subcategory_slug,
  sdk_count::integer AS sdk_count
FROM marts.v_subcategories_slug;
