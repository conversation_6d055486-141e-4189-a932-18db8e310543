{{ config(
  alias='mv_app_sdk_count_by_category',
  materialized='materialized_view',
  post_hook=[
    "{{ create_mv_unique_index(this, ['app_id','category_name','subcategory_name']) }}"
  ]
) }}

WITH recent AS (
  -- 使用窗口函数替代 DISTINCT ON：每个 app 仅保留最近一条，减少全局排序压力
  SELECT app_id::text AS app_id,
         app_version_id,
         analyzed_at
  FROM (
    SELECT
      vas.app_id,
      vas.app_version_id,
      vas.analyzed_at,
      row_number() OVER (
        PARTITION BY vas.app_id::text
        ORDER BY vas.analyzed_at DESC, vas.app_version_id DESC
      ) AS rn
    FROM marts.v_app_sdks vas
    WHERE vas.is_potential IS DISTINCT FROM true
  ) r
  WHERE r.rn = 1
), src AS (
  SELECT
    vas.app_id,
    vas.app_version_id,
    lower(s.sdk_name::text) AS sdk_name_norm,
    sub.category_name,
    sub.subcategory_name,
    vas.match_type,
    CASE
      WHEN coalesce(vas.match_type,'') ilike '%exact%' THEN 2
      WHEN coalesce(vas.match_type,'') ilike '%heur%' THEN 1
      ELSE 0
    END AS match_rank,
    vas.analyzed_at
  FROM marts.v_app_sdks vas
  JOIN marts.mv_sdks s ON s.id = vas.sdk_id
  LEFT JOIN marts.v_subcategories_slug sub ON sub.subcategory_id = s.subcategory_id
  WHERE vas.is_potential IS DISTINCT FROM true
), filtered AS (
  SELECT src.*
  FROM src
  JOIN recent r ON r.app_id = src.app_id::text AND r.app_version_id = src.app_version_id
), dedup AS (
  SELECT DISTINCT ON (app_id, sdk_name_norm)
    app_id,
    sdk_name_norm,
    category_name,
    subcategory_name,
    match_rank,
    analyzed_at
  FROM filtered
  ORDER BY app_id, sdk_name_norm, match_rank DESC, analyzed_at DESC
)
SELECT
  app_id,
  coalesce(category_name,'未分类')::varchar(255) AS category_name,
  coalesce(subcategory_name,'未分子类')::varchar(255) AS subcategory_name,
  count(*)::integer AS sdk_count
FROM dedup
GROUP BY app_id, category_name, subcategory_name;
