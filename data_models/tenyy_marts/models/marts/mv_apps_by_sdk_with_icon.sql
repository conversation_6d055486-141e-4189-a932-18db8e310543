{{ config(
  alias='mv_apps_by_sdk_with_icon',
  materialized='materialized_view',
  post_hook=[
    "{{ create_mv_sort_index(this, ['kb_subcategory_id','kb_sdk_name','download_max DESC','app_id']) }}"
  ]
) }}

SELECT
  d.kb_subcategory_id,
  d.kb_sdk_name,
  d.app_id,
  d.app_name,
  d.download_max,
  det.icon_url AS icon
FROM marts.mv_apps_by_sdk_with_downloads d
LEFT JOIN marts.mv_apps_with_detail det
  ON det.app_id::text = d.app_id::text;
