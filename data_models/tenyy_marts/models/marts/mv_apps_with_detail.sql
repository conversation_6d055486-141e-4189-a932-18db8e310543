{{ config(
  alias='mv_apps_with_detail',
  materialized='materialized_view',
  post_hook=[
    "{{ create_mv_unique_index(this, ['app_id']) }}"
  ]
) }}

WITH latest_version AS (
  SELECT
    sa_1.app_id,
    av.store_app_id,
    av.id AS app_version_id,
    av.version,
    av.apk_hash,
    av.download_url,
    av.apk_size,
    av.release_date,
    av.min_sdk,
    av.target_sdk,
    av.permissions,
    av.features,
    av.libraries,
    av.analyzed_at,
    av.analysis_result,
    av.is_latest,
    av.description,
    av.editor_intro,
    av.tags,
    av.snap_shots,
    av.permissions_list,
    av.download_num,
    av.icp_number,
    av.icp_entity,
    av.detail_json,
    av.store_name AS v_store_name,
    av.store_description,
    av.icon_url,
    av.rating,
    av.review_count,
    av.download_count,
    av.price,
    av.is_free,
    av.last_updated,
    av.developer,
    av.operator,
    av.username,
    av.category,
    av.tag_alias,
    av.game_type,
    av.is_game,
    av.cp_id,
    av.ms_store_id,
    av.ms_store_status,
    av.ios_url,
    av.privacy_agreement,
    av.booking_url,
    av.exe_download_url,
    av.video,
    av.is_cloud_game,
    av.is_pc_yyb_available,
    av.is_booking,
    av.restrict_level,
    av.syzs_download_num,
    av.booking_user_cnt,
    av.public_time,
    av.app_update_time,
    av.channel_info,
    av.show_text,
    av.detail_template,
    av.ios_app_link_info,
    av.booking_gift,
    row_number() OVER (PARTITION BY sa_1.app_id ORDER BY av.release_date DESC NULLS LAST, av.id DESC) AS rn
  FROM store_app sa_1
  JOIN app_version av ON av.store_app_id = sa_1.id
)
SELECT
  a.id AS app_id,
  a.name AS app_name,
  sa.id AS store_app_pk,
  sa.store_type,
  sa.store_id,
  sa.store_name AS store_app_name,
  lv.app_version_id,
  lv.version,
  lv.apk_hash,
  lv.download_url,
  lv.apk_size,
  lv.release_date,
  lv.min_sdk,
  lv.target_sdk,
  lv.permissions,
  lv.features,
  lv.libraries,
  lv.analyzed_at,
  lv.analysis_result,
  lv.is_latest,
  lv.description,
  lv.editor_intro,
  lv.tags,
  lv.snap_shots,
  lv.permissions_list,
  lv.download_num,
  lv.icp_number,
  lv.icp_entity,
  lv.detail_json,
  COALESCE(lv.v_store_name, sa.store_name) AS store_name,
  lv.store_description,
  lv.icon_url,
  lv.rating,
  lv.review_count,
  lv.download_count,
  lv.price,
  lv.is_free,
  lv.last_updated,
  lv.developer,
  lv.operator,
  lv.username,
  lv.category,
  lv.tag_alias,
  lv.game_type,
  lv.is_game,
  lv.cp_id,
  lv.ms_store_id,
  lv.ms_store_status,
  lv.ios_url,
  lv.privacy_agreement,
  lv.booking_url,
  lv.exe_download_url,
  lv.video,
  lv.is_cloud_game,
  lv.is_pc_yyb_available,
  lv.is_booking,
  lv.restrict_level,
  lv.syzs_download_num,
  lv.booking_user_cnt,
  lv.public_time,
  lv.app_update_time,
  lv.channel_info,
  lv.show_text,
  lv.detail_template,
  lv.ios_app_link_info,
  lv.booking_gift
FROM latest_version lv
JOIN store_app sa ON sa.id = lv.store_app_id
JOIN app a ON a.id::text = lv.app_id::text
WHERE lv.rn = 1;
