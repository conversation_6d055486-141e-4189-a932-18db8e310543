{{ config(
  alias='mv_apps_by_sdk_with_downloads',
  materialized='materialized_view',
  post_hook=[
    "{{ create_mv_unique_index(this, ['kb_subcategory_id','kb_sdk_name','app_id']) }}",
    "{{ create_mv_sort_index(this, ['kb_subcategory_id','kb_sdk_name','download_max DESC','app_id']) }}"
  ]
) }}

WITH latest_download AS (
  SELECT sa.app_id,
         max(av.download_count) AS max_download
  FROM app_version av
  JOIN store_app sa ON sa.id = av.store_app_id
  GROUP BY sa.app_id
)
SELECT DISTINCT ON (o.kb_subcategory_id, o.kb_sdk_name, o.app_id)
  o.kb_subcategory_id,
  o.kb_sdk_name,
  o.app_id,
  o.app_name,
  COALESCE(d.max_download, 0::bigint) AS download_max
FROM marts.v_app_sdk_overview o
LEFT JOIN latest_download d ON d.app_id::text = o.app_id::text
WHERE o.is_matched_kb = true
ORDER BY o.kb_subcategory_id, o.kb_sdk_name, o.app_id, COALESCE(d.max_download, 0::bigint) DESC, o.app_name;
