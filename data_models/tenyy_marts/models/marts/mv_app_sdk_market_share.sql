{{ config(
  alias='mv_app_sdk_market_share',
  materialized='materialized_view',
  post_hook=[
    "{{ create_mv_unique_index(this, ['subcategory_id','kb_sdk_name']) }}"
  ]
) }}

WITH recent AS (
  -- 使用窗口函数替代 DISTINCT ON：每个 app 仅保留最近一条，减少全局排序压力
  SELECT app_id::text AS app_id,
         app_version_id,
         analyzed_at
  FROM (
    SELECT
      vas.app_id,
      vas.app_version_id,
      vas.analyzed_at,
      row_number() OVER (
        PARTITION BY vas.app_id::text
        ORDER BY vas.analyzed_at DESC, vas.app_version_id DESC
      ) AS rn
    FROM marts.v_app_sdks vas
    WHERE vas.is_potential IS DISTINCT FROM true
  ) r
  WHERE r.rn = 1
), pairs AS (
  SELECT DISTINCT
    vas.app_id,
    lower(s.sdk_name::text) AS sdk_name_norm,
    s.sdk_name AS kb_sdk_name,
    s.subcategory_id
  FROM marts.v_app_sdks vas
  JOIN marts.mv_sdks s ON s.id = vas.sdk_id
  JOIN recent r ON r.app_id = vas.app_id::text AND r.app_version_id = vas.app_version_id
  WHERE vas.is_potential IS DISTINCT FROM true
)
SELECT
  subcategory_id,
  kb_sdk_name,
  count(DISTINCT app_id)::integer AS cnt
FROM pairs p
GROUP BY subcategory_id, kb_sdk_name;
