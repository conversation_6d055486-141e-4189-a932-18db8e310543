{{ config(
  alias='mv_categories_slug',
  materialized='materialized_view',
  post_hook=[
    "{{ create_mv_unique_index(this, ['category_id']) }}"
  ]
) }}

SELECT
  category_id,
  category_name::character varying(255) AS category_name,
  category_slug::character varying(255) AS category_slug,
  subcategory_count::integer AS subcategory_count,
  sdk_count::integer AS sdk_count
FROM marts.v_categories_slug;
