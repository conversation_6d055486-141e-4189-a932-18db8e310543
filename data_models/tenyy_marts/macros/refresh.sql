{% macro refresh_mv(relation, concurrently=True) -%}
  {% if concurrently %}
  refresh materialized view concurrently {{ relation }};
  {% else %}
  refresh materialized view {{ relation }};
  {% endif %}
{%- endmacro %}

{% macro refresh_all_materialized_views(target_schema='marts', concurrently=True) -%}
  {% set sql %}
  select format('%I.%I', n.nspname, c.relname) as fqname
  from pg_class c
  join pg_namespace n on n.oid = c.relnamespace
  where c.relkind = 'm'
    and n.nspname = '{{ target_schema }}'
  order by 1;
  {% endset %}
  {% set results = run_query(sql) %}
  {% if execute %}
    {% for row in results %}
      {% set fqname = row['fqname'] %}
      {{ log('Refreshing ' ~ fqname, info=True) }}
      {% if concurrently %}
      {% do run_query('refresh materialized view concurrently ' ~ fqname) %}
      {% else %}
      {% do run_query('refresh materialized view ' ~ fqname) %}
      {% endif %}
    {% endfor %}
  {% endif %}
{%- endmacro %}
