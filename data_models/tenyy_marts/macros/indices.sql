{% macro create_mv_unique_index(relation, columns) -%}
  {%- set idx_name = relation.identifier ~ '__uniq__' ~ (columns | join('_') | replace(' ', '_') | replace(',', '_') | lower) -%}
  {% set sql %}
  do $$
  begin
    if not exists (
      select 1 from pg_indexes
      where schemaname = '{{ relation.schema }}'
        and indexname = '{{ idx_name }}'
    ) then
      execute format('create unique index %I on %I.%I (%s)',
        '{{ idx_name }}', '{{ relation.schema }}', '{{ relation.identifier }}',
        '{{ columns | join(", ") }}'
      );
    end if;
  end$$;
  {% endset %}
  {{ return(sql) }}
{%- endmacro %}

{% macro create_mv_sort_index(relation, columns) -%}
  {%- set idx_name = relation.identifier ~ '__sort__' ~ (columns | join('_') | replace(' ', '_') | replace(',', '_') | lower) -%}
  {% set sql %}
  do $$
  begin
    if not exists (
      select 1 from pg_indexes
      where schemaname = '{{ relation.schema }}'
        and indexname = '{{ idx_name }}'
    ) then
      execute format('create index %I on %I.%I (%s)',
        '{{ idx_name }}', '{{ relation.schema }}', '{{ relation.identifier }}',
        '{{ columns | join(", ") }}'
      );
    end if;
  end$$;
  {% endset %}
  {{ return(sql) }}
{%- endmacro %}
