# Wrapper dbt project to make VS Code dbt extension detect the project at data_models level
# This file points all paths into the subfolder tenyy_marts/
name: 'tenyy_marts'
version: '1.0.0'

# Use the same profile
profile: 'tenyy_marts'

# Point all search paths to the subfolder
model-paths: ["tenyy_marts/models"]
analysis-paths: ["tenyy_marts/analyses"]
test-paths: ["tenyy_marts/tests"]
seed-paths: ["tenyy_marts/seeds"]
macro-paths: ["tenyy_marts/macros"]
snapshot-paths: ["tenyy_marts/snapshots"]

# Ensure dbt clean operates on the subfolder targets
clean-targets:
  - "tenyy_marts/target"
  - "tenyy_marts/dbt_packages"

# Mirror the models configuration from the real project
models:
  tenyy_marts:
    example:
      +materialized: view
    marts:
      +materialized: materialized_view
      +schema: marts
      +on_schema_change: sync_all_columns
      +tags: ['marts','mv']
