# Tenyy vs TheirStack 界面分析与数据库优化

## 1. TheirStack 界面分析总结

### 1.1 核心界面结构
基于实际浏览 theirstack.com 的发现：

#### **导航结构**
```
主导航:
├── Home (首页)
├── Search (搜索)
│   ├── Jobs (工作搜索)
│   └── Companies (公司搜索)
├── Company lists (公司列表)
└── Settings (设置)

开发者工具:
├── Webhooks (Webhook 管理)
├── Datasets (数据集)
└── API Keys (API 密钥)

设置分类:
Account:
├── Profile (个人资料)
├── API Key (API 密钥)
├── Integrations (集成)
└── Affiliate Program (联盟计划)

Workspace:
├── Team (团队)
├── Billing (账单)
├── Usage (使用情况)
└── Invoices (发票)
```

#### **关键功能特性**
1. **全局搜索 (⌘K)**：智能搜索对话框，支持公司快速查找
2. **配额显示**：右上角实时显示 "Company credits 16 of 50" 和 "API credits 0 of 200"
3. **通知面板 (F8)**：专门的通知区域
4. **搜索历史**：保存和显示最近搜索记录
5. **富集功能**：批量处理公司列表数据

### 1.2 与 Tenyy 的对应关系

| TheirStack | Tenyy | 数据库设计影响 |
|------------|-------|----------------|
| Companies | Apps | 需要应用详情表和搜索优化 |
| Jobs | SDKs | 需要 SDK 分析和关联表 |
| Company lists | App lists | 需要列表管理和批量操作 |
| Technologies | SDK 技术栈 | 需要技术标签和分类系统 |
| Webhooks | Webhooks | 需要事件通知系统 |
| API Keys | API Keys | 需要开发者工具管理 |

## 2. 基于分析的数据库优化

### 2.1 全局搜索优化

#### 搜索快捷方式表
```sql
-- 全局搜索快捷方式配置表
CREATE TABLE public.search_shortcuts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shortcut_key TEXT NOT NULL UNIQUE, -- 'cmd_k', 'f8', etc.
    shortcut_name TEXT NOT NULL,
    search_type TEXT NOT NULL, -- 'global', 'apps', 'sdks'
    is_active BOOLEAN DEFAULT TRUE,
    
    -- 快捷键配置
    key_combination JSONB DEFAULT '{}', -- {"meta": true, "key": "k"}
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 插入基础快捷键配置
INSERT INTO public.search_shortcuts (shortcut_key, shortcut_name, search_type, key_combination) VALUES
('cmd_k', 'Global Search', 'global', '{"meta": true, "key": "k"}'),
('f8', 'Notifications Panel', 'notifications', '{"key": "F8"}'),
('cmd_shift_f', 'Advanced Search', 'advanced', '{"meta": true, "shift": true, "key": "f"}');
```

#### 搜索结果排序优化
```sql
-- 搜索结果权重配置表
CREATE TABLE public.search_ranking_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_type TEXT NOT NULL, -- 'app', 'sdk', 'category'
    ranking_factor TEXT NOT NULL, -- 'name_match', 'popularity', 'rating', 'recent_activity'
    weight DECIMAL(3,2) DEFAULT 1.0,
    is_active BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(entity_type, ranking_factor)
);

-- 插入基础排序权重
INSERT INTO public.search_ranking_config (entity_type, ranking_factor, weight) VALUES
('app', 'exact_name_match', 2.0),
('app', 'partial_name_match', 1.5),
('app', 'description_match', 1.0),
('app', 'popularity_score', 0.8),
('app', 'rating_score', 0.6),
('app', 'recent_activity', 0.4),
('sdk', 'exact_name_match', 2.0),
('sdk', 'company_match', 1.2),
('sdk', 'category_match', 1.0),
('sdk', 'usage_count', 0.9);
```

### 2.2 配额显示系统优化

#### 实时配额监控视图
```sql
-- 用户配额实时监控视图
CREATE OR REPLACE VIEW public.api_user_quota_status AS
SELECT 
    uq.user_id,
    u.email,
    
    -- API 调用配额
    uq.api_calls_used,
    uq.api_calls_limit,
    ROUND((uq.api_calls_used::FLOAT / uq.api_calls_limit * 100), 1) as api_usage_percentage,
    
    -- 搜索配额
    uq.search_used,
    uq.search_limit,
    ROUND((uq.search_used::FLOAT / uq.search_limit * 100), 1) as search_usage_percentage,
    
    -- 导出配额
    uq.exports_used,
    uq.exports_limit,
    ROUND((uq.exports_used::FLOAT / uq.exports_limit * 100), 1) as export_usage_percentage,
    
    -- 列表配额
    (SELECT COUNT(*) FROM public.app_lists WHERE user_id = uq.user_id AND is_deleted = FALSE) as lists_used,
    uq.lists_limit,
    
    -- 配额状态
    CASE 
        WHEN uq.api_calls_used >= uq.api_calls_limit THEN 'exceeded'
        WHEN uq.api_calls_used::FLOAT / uq.api_calls_limit > 0.9 THEN 'warning'
        WHEN uq.api_calls_used::FLOAT / uq.api_calls_limit > 0.7 THEN 'caution'
        ELSE 'normal'
    END as quota_status,
    
    -- 重置时间
    uq.reset_at,
    uq.plan_type,
    uq.updated_at
    
FROM public.user_quotas uq
JOIN public.users u ON uq.user_id = u.id
WHERE u.is_deleted = FALSE;

-- 授权给认证用户
GRANT SELECT ON public.api_user_quota_status TO authenticated;

-- RLS 策略
CREATE POLICY "Users can view own quota status" ON public.api_user_quota_status
    FOR SELECT USING (user_id = auth.uid());
```

### 2.3 搜索历史增强

#### 搜索会话管理
```sql
-- 搜索会话表
CREATE TABLE public.search_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    session_token TEXT NOT NULL UNIQUE,
    
    -- 会话信息
    started_at TIMESTAMPTZ DEFAULT NOW(),
    last_activity_at TIMESTAMPTZ DEFAULT NOW(),
    ended_at TIMESTAMPTZ,
    
    -- 会话统计
    search_count INTEGER DEFAULT 0,
    result_clicks INTEGER DEFAULT 0,
    
    -- 会话上下文
    user_agent TEXT,
    ip_address INET,
    referrer TEXT,
    
    is_active BOOLEAN DEFAULT TRUE
);

-- 创建索引
CREATE INDEX idx_search_sessions_user ON public.search_sessions (user_id);
CREATE INDEX idx_search_sessions_token ON public.search_sessions (session_token);
CREATE INDEX idx_search_sessions_active ON public.search_sessions (is_active, last_activity_at DESC);

-- RLS 策略
ALTER TABLE public.search_sessions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own search sessions" ON public.search_sessions
    FOR SELECT USING (user_id = auth.uid());
```

#### 搜索历史关联会话
```sql
-- 修改搜索历史表，添加会话关联
ALTER TABLE public.search_history 
ADD COLUMN session_id UUID REFERENCES public.search_sessions(id) ON DELETE SET NULL,
ADD COLUMN click_position INTEGER, -- 用户点击结果的位置
ADD COLUMN result_clicked_id TEXT; -- 用户点击的具体结果 ID

-- 创建新索引
CREATE INDEX idx_search_history_session ON public.search_history (session_id);
CREATE INDEX idx_search_history_clicks ON public.search_history (result_clicked_id) WHERE result_clicked_id IS NOT NULL;
```

### 2.4 通知面板 (F8) 系统

#### 通知分类和优先级
```sql
-- 通知分类表
CREATE TABLE public.notification_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category_key TEXT NOT NULL UNIQUE,
    category_name TEXT NOT NULL,
    description TEXT,
    
    -- 显示配置
    icon TEXT,
    color TEXT DEFAULT '#3B82F6',
    default_priority INTEGER DEFAULT 0,
    
    -- 行为配置
    auto_dismiss_after_seconds INTEGER, -- 自动消失时间
    requires_action BOOLEAN DEFAULT FALSE, -- 是否需要用户操作
    
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 插入基础通知分类
INSERT INTO public.notification_categories (category_key, category_name, description, icon, color, default_priority) VALUES
('system', 'System', 'System-wide notifications', 'system', '#6B7280', 1),
('quota', 'Quota', 'Usage and quota notifications', 'warning', '#F59E0B', 2),
('enrichment', 'Enrichment', 'Data enrichment notifications', 'process', '#10B981', 0),
('api', 'API', 'API and integration notifications', 'api', '#8B5CF6', 1),
('update', 'Updates', 'Product updates and news', 'info', '#3B82F6', 0);
```

#### 通知触发规则
```sql
-- 通知触发规则表
CREATE TABLE public.notification_triggers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trigger_name TEXT NOT NULL,
    trigger_type TEXT NOT NULL, -- 'quota_threshold', 'enrichment_complete', 'api_error', etc.
    
    -- 触发条件
    conditions JSONB NOT NULL, -- 触发条件配置
    
    -- 通知配置
    notification_category_id UUID REFERENCES public.notification_categories(id),
    template_id UUID REFERENCES public.notification_templates(id),
    
    -- 频率控制
    cooldown_minutes INTEGER DEFAULT 60, -- 冷却时间，防止重复通知
    max_notifications_per_day INTEGER DEFAULT 10,
    
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 插入基础触发规则
INSERT INTO public.notification_triggers (trigger_name, trigger_type, conditions, notification_category_id, cooldown_minutes) VALUES
('API Quota 90%', 'quota_threshold', '{"quota_type": "api_calls", "threshold": 0.9}', 
 (SELECT id FROM public.notification_categories WHERE category_key = 'quota'), 240),
('Search Quota 90%', 'quota_threshold', '{"quota_type": "search", "threshold": 0.9}', 
 (SELECT id FROM public.notification_categories WHERE category_key = 'quota'), 240),
('Enrichment Completed', 'enrichment_complete', '{"min_apps": 1}', 
 (SELECT id FROM public.notification_categories WHERE category_key = 'enrichment'), 0);
```

### 2.5 列表管理优化

#### 列表操作历史
```sql
-- 列表操作历史表
CREATE TABLE public.app_list_operations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    list_id UUID REFERENCES public.app_lists(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- 操作信息
    operation_type TEXT NOT NULL, -- 'create', 'add_apps', 'remove_apps', 'enrich', 'export', 'rename', 'delete'
    operation_details JSONB DEFAULT '{}',
    
    -- 操作结果
    affected_count INTEGER DEFAULT 0, -- 影响的应用数量
    success_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    
    -- 时间信息
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    
    -- 状态
    status TEXT DEFAULT 'pending' -- pending, processing, completed, failed
);

-- 创建索引
CREATE INDEX idx_list_operations_list ON public.app_list_operations (list_id);
CREATE INDEX idx_list_operations_user ON public.app_list_operations (user_id);
CREATE INDEX idx_list_operations_type ON public.app_list_operations (operation_type);
CREATE INDEX idx_list_operations_status ON public.app_list_operations (status);

-- RLS 策略
ALTER TABLE public.app_list_operations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own list operations" ON public.app_list_operations
    FOR SELECT USING (user_id = auth.uid());
```

#### 列表分享和协作
```sql
-- 列表分享表
CREATE TABLE public.app_list_shares (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    list_id UUID REFERENCES public.app_lists(id) ON DELETE CASCADE,
    owner_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- 分享配置
    share_token TEXT NOT NULL UNIQUE,
    share_type TEXT NOT NULL, -- 'public', 'private', 'team'
    
    -- 权限控制
    permissions JSONB DEFAULT '["read"]', -- read, write, export
    
    -- 访问控制
    allowed_emails TEXT[], -- 允许访问的邮箱列表
    max_access_count INTEGER, -- 最大访问次数
    access_count INTEGER DEFAULT 0,
    
    -- 时间控制
    expires_at TIMESTAMPTZ,
    
    -- 状态
    is_active BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_list_shares_list ON public.app_list_shares (list_id);
CREATE INDEX idx_list_shares_token ON public.app_list_shares (share_token);
CREATE INDEX idx_list_shares_owner ON public.app_list_shares (owner_id);

-- RLS 策略
ALTER TABLE public.app_list_shares ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own list shares" ON public.app_list_shares
    FOR ALL USING (owner_id = auth.uid());
```

## 3. 前端集成优化建议

### 3.1 全局搜索 (⌘K) 实现
```typescript
// 搜索建议 API 调用
const getSearchSuggestions = async (query: string) => {
  const { data } = await supabase.rpc('get_search_suggestions', {
    p_query: query,
    p_limit: 8
  });
  return data;
};

// 搜索历史记录
const recordSearch = async (query: string, resultCount: number) => {
  await supabase.from('search_history').insert({
    search_type: 'global',
    query,
    result_count: resultCount,
    session_id: getCurrentSessionId()
  });
};
```

### 3.2 配额显示组件
```typescript
// 实时配额状态
const useQuotaStatus = () => {
  const { data: quotaStatus } = useQuery({
    queryKey: ['quota-status'],
    queryFn: async () => {
      const { data } = await supabase
        .from('api_user_quota_status')
        .select('*')
        .single();
      return data;
    },
    refetchInterval: 30000 // 30秒刷新一次
  });
  
  return quotaStatus;
};
```

### 3.3 通知面板 (F8) 实现
```typescript
// 通知实时订阅
const useNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  
  useEffect(() => {
    const channel = supabase
      .channel('user-notifications')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'user_notifications',
        filter: `user_id=eq.${userId}`
      }, (payload) => {
        setNotifications(prev => [payload.new, ...prev]);
      })
      .subscribe();
      
    return () => supabase.removeChannel(channel);
  }, [userId]);
  
  return notifications;
};
```

这个分析文档基于对 theirstack.com 的实际界面分析，为 Tenyy 数据库设计提供了针对性的优化建议，确保我们的产品能够提供同等甚至更好的用户体验。
