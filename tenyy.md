# Tenyy 产品需求文档 (PRD)

---

## 1. 产品愿景与目标

**愿景**: 成为全球领先的移动应用情报和 SDK (软件开发工具包) 数据服务商，为企业提供最全面、实时、精准的应用和技术栈数据，帮助客户在移动市场中做出更明智的商业决策。

**目标**:
*   **核心目标**: 提供高质量、结构化的全球移动应用信息及集成的 SDK 数据。
*   **短期目标 (1-2 年)**: 拓展数据覆盖范围（更多的应用市场和 SDK 类型），深化与主流商业智能和自动化工具的集成，提升平台的用户体验和数据查询效率。
*   **长期目标 (3-5 年)**: 利用数据分析和机器学习能力，提供预测性市场洞察（如 SDK 市场占有率趋势、应用技术选型分析），成为企业在移动战略、市场分析和安全合规方面不可或缺的工具。

---

## 2. 目标用户

*   **SDK/第三方服务提供商**: 需要快速找到正在使用其竞品或互补品 SDK 的应用开发者，作为高价值的销售线索。
*   **移动应用开发者与产品团队**: 需要了解竞品应用的技术栈、更新频率和使用的第三方服务，以优化自身产品。
*   **市场分析师和投资机构**: 需要宏观数据来分析移动应用趋势、特定 SDK 的普及度以及不同应用类别的技术生态。
*   **网络安全与合规分析师**: 需要识别应用中集成的 SDK 及其版本，以评估潜在的安全风险和隐私合规问题。
*   **数据科学家与工程师**: 需要通过 API 的方式将海量应用和 SDK 数据集成到自己的分析模型或业务流程中。

---

## 3. 核心功能需求

### 3.1 数据层

| 功能模块 | 需求描述 | 优先级 |
| :--- | :--- | :--- |
| **应用数据 (App Data)** | - 覆盖全球主流应用市场，聚合海量应用元数据。<br>- 提供应用基本信息，如包名(Bundle ID)、名称、描述、分类、开发者、图标 URL 等。<br>- 提供详细的应用版本信息，包括版本号、发布日期、APK 哈希、大小、更新日志、商店截图、下载量、评分等。 | 高 |
| **SDK 识别数据 (SDK Intelligence)** | - 识别 Android 应用 APK 文件中集成的第三方 SDK。<br>- 提供强大的 SDK 知识库，包含 SDK 名称、所属公司、官网、隐私政策链接、功能描述等。<br>- 支持多种匹配方式（包名前缀、正则表达式），并提供匹配类型信息。 | 高 |
| **技术栈数据 (App Technographics)** | - 关联应用及其使用的所有 SDK，形成完整的技术栈画像。<br>- 包含 APK 的详细技术信息，如最低/目标 SDK 版本、权限列表、so 库文件等。 | 高 |
| **数据关联与丰富化** | - 用户可上传应用列表（如包名），系统自动为其匹配并补充应用元数据和 SDK 技术栈数据。 | 中 |

### 3.2 应用层 (WebApp)

| 功能模块 | 需求描述 | 优先级 |
| :--- | :--- | :--- |
| **应用搜索 (App Search)** | - 支持按应用名称或包名 (Bundle ID) 进行快速查找。<br>- 提供超过 20 个筛选维度，如应用分类、下载量、评分、发布日期、所在应用市场等。<br>- 查看应用详情，包括所有历史版本、元数据和完整的 SDK 列表。 | 高 |
| **SDK 搜索 (SDK Search)** | - 支持按 SDK 名称、公司或包名前缀进行搜索。<br>- 筛选出所有正在使用特定 SDK 的应用列表，并可进一步按应用属性进行过滤。<br>- 查看 SDK 详情，包括其在市场中的占有率、关联应用等。 | 高 |
| **保存搜索与提醒** | - 用户可以保存常用的搜索条件（如“金融类应用中使用 XX 支付 SDK 的 App”）。<br>- 支持设置邮件提醒，当有新的应用符合保存的搜索条件时，自动推送通知。 | 高 |
| **用户与团队管理** | - 支持多用户团队协作模式。 | 低 |
| **数据导出** | - 支持将搜索结果（应用列表、SDK 列表）一键导出为 CSV 或 Excel 格式。 | 中 |

### 3.3 数据接入层 (API & Integration)

| 功能模块 | 需求描述 | 优先级 |
| :--- | :--- | :--- |
| **REST API** | - 提供强大的 API 接口，支持对应用、应用版本和 SDK 数据的程序化访问。<br>- API 设计对开发者友好，提供清晰的文档和代码示例 (Python, Node.js, Java)。<br>- 提供免费的预览模式和计数功能，方便用户在不消耗点数的情况下调试查询。 | 高 |
| **Webhooks** | - 当有符合用户设定条件的新事件发生时（如某应用发布新版本、某应用开始使用特定 SDK），通过 Webhook 实时通知用户的系统。 | 高 |
| **数据集 (Datasets)** | - 对于需要全量数据的客户，提供 CSV 或 Parquet 格式的数据集。<br>- 支持通过 S3 进行历史数据和每日增量数据的交付。 | 中 |
| **第三方集成** | - 与主流的无代码/低代码平台（如 Zapier, Make, Clay）集成，方便非技术用户创建自动化工作流。 | 中 |

---

## 4. 非功能性需求

*   **数据质量与新鲜度**: 建立一个由 Prefect 工作流驱动的，包含数据采集、APK 下载、特征提取、SDK 匹配和知识库自学习的 5 步自动化数据处理流程，确保数据的准确性和时效性。
*   **系统性能**: 保证 API 和 WebApp 的高可用性和低延迟，特别是对于高频查询。数据库和匹配逻辑经过高度优化。
*   **用户体验 (UX)**: 提供基于 Next.js 和 Tailwind CSS 的现代化、简洁直观的 UI 界面，即使是无编码背景的用户也能轻松上手。提供清晰的引导和帮助文档。
*   **可扩展性**: 基于 Docker Swarm 的微服务架构，可以轻松地横向扩展爬虫和分析器等组件，以应对海量数据处理需求。
*   **合规性**: 遵守 GDPR 等数据隐私法规，提供清晰的隐私政策和数据处理说明。SDK 知识库中包含隐私政策链接字段。

---

## 5. 商业模式

*   **基于点数的订阅制 (Credit-based)**:
    *   用户购买点数（Credits），不同的操作（如 API 查询、数据丰富化、报告导出）会消耗不同数量的点数。
    *   提供点数自动充值功能，避免服务中断。
*   **数据集销售**: 针对需要大规模离线数据的企业客户，直接销售数据集。
*   **联盟计划 (Affiliate Program)**: 提供推荐佣金，激励用户推广产品，扩大市场份额。